# 项目文档目录

本目录包含了项目相关的所有技术文档，按照不同类别进行组织。

## 📁 目录结构

```
docs/
├── README.md                    # 本文件，文档目录说明
├── langchain技术/               # LangChain相关技术文档
├── rag系统/                     # RAG系统相关文档
├── 系统架构/                    # 系统架构相关文档
├── 文档处理/                    # 文档处理和分割相关
├── 搜索检索/                    # 搜索和检索算法相关
├── 开发指南/                    # 开发相关文档和指南
├── 面试题集/                    # 面试相关文档
└── 学习教程/                    # 教程和实践指南
```

## 📖 文档分类说明

### LangChain技术 (`langchain技术/`)

- **LangChain流式处理完全指南.md**: 深入解析LangChain流式处理的原理、实现和最佳实践
- **LangChain使用模式判断指南.md**: 不同场景下的LangChain使用策略
- **LangChain向量数据库集成详解.md**: 向量数据库选择和集成方案
- **LangChain版本升级分析与建议.md**: 版本迁移和功能对比
- **LangChain语义分割实现指南.md**: 语义级别的文档分割
- **LangChain_LangServe_SSE完整架构指南.md**: LangServe和SSE架构实现
- **直接使用OpenAI库vs LangChain对比分析.md**: 技术选型对比分析
- **完整版本升级指南与功能对比.md**: 详细的版本升级指南

### RAG系统 (`rag系统/`)

- **RAG系统数据流与向量化详解.md**: RAG系统核心流程和数据处理机制
- **RAG答案生成流程深度解析.md**: 答案生成的详细流程和优化策略
- **企业级RAG查询路由策略完全指南.md**: 企业级RAG系统的查询路由和意图识别

### 系统架构 (`系统架构/`)

- **APIManager多供应商代理机制详解.md**: 多LLM供应商管理和代理机制
- **APIManager技术知识点分析.md**: API管理器技术要点分析
- **CLI模块架构与启动流程详解.md**: CLI模块的架构设计和启动流程
- **流式处理框架对比与实现指南.md**: 不同流式处理方案的对比和选择
- **意图识别系统技术实现指南.md**: 用户意图识别技术实现
- **当前项目技术实现现状分析.md**: 项目技术现状和改进建议

### 文档处理 (`文档处理/`)

- **Chunker分片策略完全解析.md**: 文档分割策略和实现详解
- **PDF和Excel文档智能分割方案.md**: 特定格式文档的处理方案
- **LLM模型规模与文档分割效果分析.md**: 模型规模对分割效果的影响分析

### 搜索检索 (`搜索检索/`)

- **关键词搜索算法深度解析.md**: 关键词搜索算法的实现和优化
- **检索系统中的重排序与BM25详解.md**: 检索结果优化和BM25算法
- **混合搜索结果合并算法详解.md**: 多种搜索算法的结合和结果合并

### 开发指南 (`开发指南/`)

- **Python项目VSCode调试完全指南.md**: Python项目开发环境配置和调试技巧
- **Prompt管理核心概念通俗解释.md**: Prompt工程和管理的核心概念
- **personal-command-kb-execution-flow.md**: 个人命令知识库的执行流程

### 面试题集 (`面试题集/`)

- **二线城市AI工程师LangChain面试题集.md**: 专为二线城市AI工程师岗位设计的面试题集
  - 40道精选面试题，涵盖初级、中级、高级三个层次
  - 包含基础概念、实战应用、代码实现三种题型
  - 详细的标准答案和评分标准
  - 追问问题和深度解析

### 学习教程 (`学习教程/`)

- **Java工程师转AI工程师完整指南.md**: Java工程师转型AI工程师的完整路径和技能要求

## 🎯 使用建议

### 1. 学习路径

**初学者路径**：
1. 先阅读 `学习教程/Java工程师转AI工程师完整指南.md` 了解转型路径
2. 学习 `面试题集/二线城市AI工程师LangChain面试题集.md` 掌握基础概念
3. 实践 `langchain技术/LangChain使用模式判断指南.md` 了解使用场景

**进阶开发者路径**：
1. 深入 `langchain技术/LangChain流式处理完全指南.md` 掌握流式处理
2. 研究 `rag系统/RAG系统数据流与向量化详解.md` 理解RAG原理
3. 实现 `rag系统/企业级RAG查询路由策略完全指南.md` 构建企业级系统

**架构师路径**：
1. 分析 `系统架构/APIManager多供应商代理机制详解.md` 了解架构设计
2. 设计 `搜索检索/混合搜索结果合并算法详解.md` 优化搜索算法
3. 优化 `系统架构/流式处理框架对比与实现指南.md` 选择合适方案

### 2. 按需查阅

- **项目开发时**：参考 `开发指南/` 目录下的文档
- **技术选型时**：查看 `langchain技术/` 和 `系统架构/` 目录
- **性能优化时**：参考 `搜索检索/` 和 `文档处理/` 目录
- **面试准备时**：重点学习 `面试题集/` 目录

### 3. 文档维护

- 所有文档使用Markdown格式编写
- 代码示例经过测试验证
- 持续更新最新的技术实践
- 欢迎贡献和反馈改进建议

## 📊 文档统计

- **总文档数**: 20+ 篇
- **技术领域**: LangChain、RAG、搜索检索、系统架构等
- **难度层次**: 从入门到高级，适合不同水平的开发者
- **实用性**: 所有文档都基于实际项目经验

## 📝 更新日志

- **2024-01-XX**: 创建中文分类目录结构
- **2024-01-XX**: 整理所有技术文档到对应分类
- **2024-01-XX**: 添加LangChain流式处理完全指南
- **2024-01-XX**: 完善文档分类和使用指南

---

*文档持续更新中，致力于成为最实用的AI工程师技术知识库！*