# Python项目VSCode调试完全指南

> 基于 Personal Command-Line Vector Knowledge Base 项目的VSCode调试技巧和最佳实践

## 🎯 调试概述

调试是开发过程中最重要的技能之一。对于复杂的RAG项目，掌握调试技巧可以帮助你：
- 理解代码执行流程
- 快速定位问题根源
- 优化性能瓶颈
- 验证业务逻辑

## 🛠️ VSCode调试环境配置

### 1. 安装必要扩展

```bash
# 在VSCode中安装以下扩展:
# 1. Python (Microsoft) - 必装
# 2. Python Debugger (Microsoft) - 必装  
# 3. Python Docstring Generator - 推荐
# 4. Python Type Hint - 推荐
# 5. Error Lens - 推荐，实时显示错误
```

### 2. 配置launch.json

#### 2.1 创建launch.json文件

**步骤1**: 在VSCode中按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)，输入 "Debug: Add Configuration"

**步骤2**: 选择调试器类型
- 在弹出的调试器列表中选择 **"Python Debugger"**

**步骤3**: 选择调试配置类型

**推荐选择：模块** (Module)
- 适用于我们的项目，因为使用 `python -m command_kb.cli.main` 启动

**所有选项详解：**

| 选项 | 使用场景 | 示例 |
|------|----------|------|
| **Python 文件** | 调试单个.py文件 | `python script.py` |
| **带有参数的** | 调试需要命令行参数的单文件 | `python script.py --arg1 value1` |
| **模块** ⭐ | 调试Python包/模块 | `python -m package.module` |
| **远程附加** | 附加到远程运行的Python进程 | 调试服务器上的Python程序 |
| **使用进程ID进行附加** | 附加到本地运行的Python进程 | 调试已启动的Python程序 |
| **Django** | 调试Django Web应用 | `python manage.py runserver` |
| **FastAPI** | 调试FastAPI Web应用 | `uvicorn main:app --reload` |
| **Flask** | 调试Flask Web应用 | `flask run` |
| **Pyramid** | 调试Pyramid Web应用 | `pserve development.ini` |

**选择建议：**
- **我们的RAG项目**: 选择"模块"
- **单个脚本文件**: 选择"Python 文件"
- **Web应用**: 选择对应的框架选项
- **生产环境调试**: 选择"远程附加"

**步骤4**: 输入模块信息
- 选择"模块"后，VSCode会提示输入模块名称
- 输入：`command_kb.cli.main`
- VSCode会自动创建基础配置，然后我们需要自定义

**各选项的后续步骤：**
- **Python 文件**: 选择要调试的.py文件
- **带有参数的**: 输入文件路径和参数
- **模块**: 输入模块名称（如：`command_kb.cli.main`）
- **远程附加**: 输入主机地址和端口
- **Django**: 自动配置Django调试环境
- **FastAPI**: 自动配置FastAPI调试环境

#### 2.2 VSCode生成的默认配置

VSCode会生成如下基础配置：
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python 调试程序: 模块",
            "type": "debugpy",
            "request": "launch",
            "module": "command_kb.cli.main"
        }
    ]
}
```

#### 2.3 自定义launch.json配置

**问题分析**: 默认配置过于简单，缺少：
- 命令行参数（--interactive, --verbose）
- 环境变量设置（PYTHONPATH）
- 工作目录配置
- 调试选项配置

**解决方案**: 将默认配置替换为以下完整配置：

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Interactive Mode",
            "type": "debugpy",
            "request": "launch",
            "module": "command_kb.cli.main",
            "args": ["--interactive", "--verbose"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/personal-command-kb",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"
            },
            "justMyCode": false,
            "stopOnEntry": false
        },
        {
            "name": "Debug Single Query",
            "type": "debugpy",
            "request": "launch",
            "module": "command_kb.cli.commands",
            "args": ["query", "docker logs", "--verbose"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/personal-command-kb",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"
            },
            "justMyCode": false
        },
        {
            "name": "Debug Data Import",
            "type": "debugpy",
            "request": "launch", 
            "module": "command_kb.cli.commands",
            "args": ["import-data", "./data/raw/samples"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src"
            },
            "justMyCode": false
        },
        {
            "name": "Debug Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src"
            },
            "justMyCode": true
        }
    ]
}
```

#### 2.4 配置修改步骤

**步骤1**: 打开生成的 `.vscode/launch.json` 文件

**步骤2**: 将默认的简单配置替换为上面的完整配置

**步骤3**: 保存文件

**步骤4**: 验证配置
- 按 `F5` 或点击调试面板的"开始调试"按钮
- 选择 "Debug Interactive Mode" 配置
- 应该能看到项目正常启动并进入交互模式

**常见问题排查:**

如果遇到 `ModuleNotFoundError: No module named 'command_kb'` 错误，请检查：

1. **项目结构确认** ⚠️ **关键问题点**:

   **实际项目结构**:
   ```
   mlh_pro/                           ← VSCode工作区根目录
   └── personal-command-kb/           ← 项目子目录
       ├── src/
       │   └── command_kb/
       │       ├── __init__.py
       │       └── cli/
       │           ├── __init__.py
       │           └── main.py
       ├── .vscode/
       │   └── launch.json
       └── pyproject.toml
   ```

   **问题**: VSCode工作区在 `mlh_pro`，但项目代码在 `personal-command-kb` 子目录中

2. **PYTHONPATH设置修正** 🔧:
   ```json
   "env": {
       "PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"
   }
   ```

3. **工作目录修正** 🔧:
   ```json
   "cwd": "${workspaceFolder}/personal-command-kb"
   ```

3. **包安装**: 确保项目已安装 ⚠️ **注意目录**
   ```bash
   # 必须在 personal-command-kb 目录执行，不是 mlh_pro 目录
   cd /Users/<USER>/go/mlh_pro/personal-command-kb
   pip install -e .
   # 或者
   uv pip install -e .
   ```

4. **Python解释器**: 确保VSCode使用正确的虚拟环境
   - 按 `Ctrl+Shift+P`，输入 "Python: Select Interpreter"
   - 选择项目的 `.venv/bin/python`

#### 2.5 配置说明

**关键配置项解释:**
- `"type": "debugpy"`: 使用Python调试器
- `"request": "launch"`: 启动新进程（而非附加到现有进程）
- `"module": "command_kb.cli.main"`: 指定要调试的Python模块
- `"args": ["--interactive", "--verbose"]`: 命令行参数
- `"console": "integratedTerminal"`: 在VSCode集成终端中运行
- `"justMyCode": false`: 允许调试第三方库代码
- `"env"`: 设置环境变量，特别是PYTHONPATH

### 3. 配置settings.json

在 `.vscode/settings.json` 中添加：

```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        "*.egg-info": true
    }
}
```

## 🔍 调试技巧详解

### 1. 断点调试 (Breakpoints)

#### 1.1 基础断点设置
```python
# 在main.py的_handle_query方法中设置断点
def _handle_query(self, query_text: str):
    # 点击行号左侧设置断点，或按F9
    try:
        import time
        start_time = time.time()  # ← 在这里设置断点
        
        # 检索相关文档
        retrieval_result = self.retriever.retrieve(query_text, top_k=5)  # ← 关键调用点
```

#### 1.2 条件断点
```python
# 右键断点 → "Edit Breakpoint" → 添加条件
def embed_texts(self, texts: List[str]) -> APIResponse:
    # 条件: len(texts) > 1
    # 只有当文本列表长度大于1时才触发断点
    for provider_name in providers_to_try:
        # 调试逻辑...
```

#### 1.3 日志断点 (Logpoints)
```python
# 右键 → "Add Logpoint"
# 表达式: f"Processing query: {query_text}, provider: {provider_name}"
def _get_provider_fallback_order(self) -> List[str]:
    providers = []
    # 不会停止执行，只会输出日志信息
```

### 2. 调用堆栈分析 (Call Stack)

#### 2.1 理解调用堆栈
当程序在断点处停止时，VSCode会显示完整的调用堆栈：

```
调用堆栈示例 (从上到下):
┌─────────────────────────────────────────────────────────┐
│ embed_texts (api_manager.py:156)          ← 当前位置    │
│ embed_single_text (embedder.py:89)        ← 调用者     │
│ retrieve (retriever.py:67)                ← 上级调用者  │
│ _handle_query (main.py:170)               ← 业务入口    │
│ run_interactive (main.py:88)              ← 交互循环    │
│ main (main.py:377)                        ← 程序入口    │
│ <module> (main.py:385)                    ← 模块加载    │
└─────────────────────────────────────────────────────────┘
```

#### 2.2 堆栈导航技巧
```python
# 在调试时，可以:
# 1. 点击堆栈中的任意层级查看上下文
# 2. 在不同层级查看变量值
# 3. 理解数据如何在层级间传递

# 例如在embed_texts中停止时:
# - 查看当前层: texts参数的值
# - 点击retrieve层: 查看query_text的原始值  
# - 点击_handle_query层: 查看用户输入的完整上下文
```

### 3. 变量监视 (Variables & Watch)

#### 3.1 变量面板使用
```python
def embed_texts(self, texts: List[str], model: Optional[str] = None) -> APIResponse:
    # 在断点处停止时，变量面板会显示:
    # Local Variables:
    #   self: <APIManager object>
    #   texts: ['docker logs']  
    #   model: None
    #   providers_to_try: ['siliconflow', 'openai', 'zhipu']
    #   last_error: None
    
    providers_to_try = self._get_provider_fallback_order()
    last_error = None
    
    for provider_name in providers_to_try:
        # 在循环中，可以看到provider_name的变化
        client = self.clients[provider_name]
```

#### 3.2 监视表达式 (Watch Expressions)
```python
# 在Watch面板中添加表达式:
# 1. self.total_cost                    # 监视总成本
# 2. len(self.clients)                  # 监视客户端数量
# 3. self.health_status[provider_name]  # 监视提供商健康状态
# 4. response.success if 'response' in locals() else 'N/A'  # 条件监视

def _update_provider_stats(self, provider_name: str, response: APIResponse, success: bool):
    # 监视表达式会实时更新显示这些值
    health = self.health_status.get(provider_name)
    if health:
        health.success_rate = min(1.0, health.success_rate + 0.01) if success else max(0.0, health.success_rate - 0.05)
```

### 4. 步进调试 (Step Debugging)

#### 4.1 步进命令详解
```python
def _handle_query(self, query_text: str):
    try:
        start_time = time.time()  # ← 断点在这里
        
        # F10 (Step Over): 执行当前行，不进入函数内部
        retrieval_result = self.retriever.retrieve(query_text, top_k=5)
        
        # F11 (Step Into): 进入retrieve函数内部
        if not retrieval_result.documents:
            return
        
        # Shift+F11 (Step Out): 跳出当前函数，返回调用者
        generation_result = self.generator.generate_answer(query_text, retrieval_result.documents)
        
        # F5 (Continue): 继续执行到下一个断点
```

#### 4.2 步进策略
```python
# 调试策略建议:
# 1. 从高层开始 (main.py) 
# 2. 使用Step Over了解整体流程
# 3. 在关键点使用Step Into深入
# 4. 使用Step Out快速返回上层

# 示例调试路径:
# main.py:_handle_query() [Step Over] 
#   ↓
# retriever.py:retrieve() [Step Into]
#   ↓  
# embedder.py:embed_single_text() [Step Into]
#   ↓
# api_manager.py:embed_text() [Step Into]
#   ↓
# siliconflow_client.py:embed_texts() [Step Into]
```

## 🔧 专项调试技巧

### 1. API调用调试

#### 1.1 网络请求调试
```python
# 在api客户端中添加详细日志
class SiliconFlowClient(BaseAPIClient):
    def embed_texts(self, texts: List[str], model: Optional[str] = None) -> APIResponse:
        # 设置断点查看请求参数
        payload = {
            "input": texts,
            "model": model or self.embedding_model,
        }
        
        # 监视payload内容
        response = requests.post(
            f"{self.base_url}/embeddings",
            headers=self.headers,
            json=payload,
            timeout=self.timeout,
        )
        
        # 设置断点查看响应
        response.raise_for_status()
        data = response.json()
        
        # 在Watch中添加: response.status_code, data.keys()
        return self._parse_embedding_response(data)
```

#### 1.2 API错误调试
```python
# 使用try-except捕获详细错误信息
try:
    response = self.retry_handler.retry_with_backoff(
        client.embed_texts, texts, model
    )
except requests.exceptions.RequestException as e:
    # 设置断点查看详细错误
    logger.error(f"Request failed: {e}")
    # 在Variables面板查看e的详细信息
    # e.response.text, e.response.status_code等
except Exception as e:
    # 捕获其他异常
    logger.exception("Unexpected error")
    # 使用调试器查看完整异常信息
```

### 2. 数据流调试

#### 2.1 向量数据调试
```python
def retrieve(self, query: str, top_k: int = 5) -> RetrievalResult:
    # 1. 查看查询文本
    logger.debug(f"Query: {query}")  # 在断点处查看
    
    # 2. 查看嵌入结果
    embedding_result = self.embedder.embed_single_text(query)
    # 在Watch中添加: len(embedding_result.embeddings), type(embedding_result.embeddings[0])
    
    # 3. 查看搜索结果
    search_results = self.storage.search_similar(
        query_embedding=embedding_result.embeddings,
        top_k=top_k
    )
    # 在Variables面板查看search_results的结构和内容
    
    # 4. 查看文档转换
    documents = []
    for result in search_results:
        # 设置条件断点: result['similarity'] >= 0.7
        if result['similarity'] >= self.similarity_threshold:
            doc = Document(
                page_content=result['document'],
                metadata=result['metadata']
            )
            documents.append(doc)
    
    return RetrievalResult(documents=documents, ...)
```

### 3. 性能调试

#### 3.1 时间分析
```python
import time
import cProfile
import pstats

def _handle_query(self, query_text: str):
    # 方法1: 手动计时
    start_time = time.time()
    
    # 检索阶段
    retrieval_start = time.time()
    retrieval_result = self.retriever.retrieve(query_text, top_k=5)
    retrieval_time = time.time() - retrieval_start
    # 在Watch中添加: retrieval_time
    
    # 生成阶段  
    generation_start = time.time()
    generation_result = self.generator.generate_answer(query_text, retrieval_result.documents)
    generation_time = time.time() - generation_start
    # 在Watch中添加: generation_time
    
    total_time = time.time() - start_time
    logger.info(f"Query timing - Retrieval: {retrieval_time:.2f}s, Generation: {generation_time:.2f}s, Total: {total_time:.2f}s")
```

#### 3.2 内存分析
```python
import tracemalloc
import psutil
import os

def debug_memory_usage():
    # 启动内存跟踪
    tracemalloc.start()
    
    # 获取当前进程
    process = psutil.Process(os.getpid())
    
    # 在关键点检查内存
    def check_memory(label: str):
        current, peak = tracemalloc.get_traced_memory()
        memory_info = process.memory_info()
        print(f"{label} - Current: {current / 1024 / 1024:.1f}MB, Peak: {peak / 1024 / 1024:.1f}MB, RSS: {memory_info.rss / 1024 / 1024:.1f}MB")
    
    check_memory("Start")
    # 执行操作...
    check_memory("After embedding")
    # 执行操作...
    check_memory("After generation")
```

## 🚨 常见调试场景

### 1. API调用失败
```python
# 调试步骤:
# 1. 在api_manager.embed_texts()设置断点
# 2. 检查providers_to_try列表
# 3. 逐个检查每个provider的健康状态
# 4. 在API调用处检查请求参数
# 5. 查看响应状态码和错误信息

# 关键监视表达式:
# - self.health_status
# - provider_name  
# - response.status_code
# - response.text
```

### 2. 检索结果为空
```python
# 调试步骤:
# 1. 在retriever.retrieve()设置断点
# 2. 检查query文本是否正确
# 3. 检查embedding_result是否有效
# 4. 检查search_results数量和相似度
# 5. 检查similarity_threshold设置

# 关键监视表达式:
# - query
# - len(embedding_result.embeddings)
# - len(search_results)
# - [r['similarity'] for r in search_results]
# - self.similarity_threshold
```

### 3. 性能问题
```python
# 调试步骤:
# 1. 使用cProfile分析整体性能
# 2. 在关键方法设置时间断点
# 3. 检查API响应时间
# 4. 分析内存使用情况
# 5. 检查缓存命中率

# 性能监视表达式:
# - time.time() - start_time
# - len(self.cache) if hasattr(self, 'cache') else 0
# - self.total_requests
# - self.total_cost
```

## 📊 调试最佳实践

### 1. 调试前准备
- 确保测试数据完整
- 配置详细日志级别
- 准备好API密钥
- 了解预期的执行流程

### 2. 调试过程中
- 从高层逐步深入
- 重点关注数据变化
- 记录关键变量值
- 注意异常处理路径

### 3. 调试后总结
- 记录问题根因
- 更新文档和注释
- 添加单元测试
- 优化错误处理

---

*掌握这些调试技巧，可以大大提高开发效率和代码质量。调试不仅是发现问题的工具，更是理解代码执行逻辑的最佳方式。*
