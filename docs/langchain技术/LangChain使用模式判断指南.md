# LangChain使用模式判断指南

> 如何判断什么时候用LCEL链式调用（|），什么时候用函数调用

## 🎯 核心区别

### LCEL链式模式 vs 函数调用模式

```python
# LCEL链式模式（数据流处理）
chain = prompt | llm | output_parser
result = chain.invoke({"question": "你好"})

# 函数调用模式（工具性操作）
loader = PyPDFLoader("document.pdf")
documents = loader.load()
```

## 🔍 判断标准

### 1. **数据流 vs 工具操作**

#### LCEL链式模式（数据流处理）
```python
# 特征：数据在组件间流动和转换
prompt | llm | output_parser
retriever | format_docs | llm
```

**判断要点**：
- 数据需要在多个组件间传递
- 每个组件都对数据进行转换
- 有明确的输入→处理→输出流程

#### 函数调用模式（工具性操作）
```python
# 特征：执行具体的功能操作
loader.load()           # 加载文档
splitter.split_documents()  # 分割文档
vectorstore.add_documents()  # 添加到向量库
```

**判断要点**：
- 执行具体的功能操作
- 不需要与其他组件组合
- 一次性完成特定任务

### 2. **可组合性需求**

#### 需要组合 → 使用LCEL
```python
# 需要灵活组合不同组件
basic_chain = prompt | llm
advanced_chain = prompt | llm | output_parser | custom_processor

# 可以动态替换组件
chain_with_different_llm = prompt | different_llm | output_parser
```

#### 不需要组合 → 使用函数调用
```python
# 独立的功能操作
documents = loader.load()
chunks = text_splitter.split_documents(documents)
vectorstore.add_documents(chunks)
```

### 3. **执行时机**

#### 延迟执行 → 使用LCEL
```python
# 定义时不执行，调用时才执行
chain = prompt | llm | output_parser  # 这里只是定义
result = chain.invoke(input_data)     # 这里才真正执行
```

#### 立即执行 → 使用函数调用
```python
# 调用时立即执行
documents = loader.load()  # 立即加载文档
```

## 📋 实用判断清单

### 使用LCEL链式模式的场景 ✅

```python
# 1. 多步骤数据处理
question_chain = (
    {"context": retriever, "question": RunnablePassthrough()} 
    | prompt 
    | llm 
    | StrOutputParser()
)

# 2. 需要流式输出
streaming_chain = prompt | llm | output_parser
for chunk in streaming_chain.stream(input_data):
    print(chunk)

# 3. 需要并行处理
parallel_chain = RunnableParallel({
    "summary": summarize_chain,
    "translation": translate_chain
})

# 4. 条件分支处理
conditional_chain = RunnableBranch(
    (lambda x: x["type"] == "question", qa_chain),
    (lambda x: x["type"] == "summary", summary_chain),
    default_chain
)

# 5. 需要错误处理和重试
robust_chain = prompt | llm.with_retry() | output_parser
```

### 使用函数调用模式的场景 ✅

```python
# 1. 文档加载和预处理
loader = PyPDFLoader("document.pdf")
documents = loader.load()

text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000)
chunks = text_splitter.split_documents(documents)

# 2. 向量数据库操作
vectorstore = Chroma.from_documents(chunks, embeddings)
vectorstore.add_documents(new_documents)
similar_docs = vectorstore.similarity_search("query")

# 3. 一次性工具操作
search_tool = DuckDuckGoSearchRun()
search_result = search_tool.run("Python programming")

# 4. 配置和初始化
llm = ChatOpenAI(model="gpt-4", temperature=0.7)
embeddings = OpenAIEmbeddings()

# 5. 数据转换工具
formatted_docs = format_docs(retrieved_documents)
```

## 🔧 具体示例对比

### 示例1：RAG系统实现

#### ❌ 错误混用方式
```python
# 不要这样混用
def bad_rag_example(question):
    # 这里用函数调用是对的
    docs = vectorstore.similarity_search(question)
    context = format_docs(docs)
    
    # 但这里应该用LCEL链式
    prompt_text = f"Context: {context}\nQuestion: {question}"
    response = llm.invoke(prompt_text)  # 不够灵活
    return response.content
```

#### ✅ 正确使用方式
```python
def good_rag_example():
    # 工具操作用函数调用
    vectorstore = Chroma.from_documents(documents, embeddings)
    retriever = vectorstore.as_retriever()
    
    # 数据流处理用LCEL链式
    rag_chain = (
        {"context": retriever | format_docs, "question": RunnablePassthrough()}
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return rag_chain

# 使用
chain = good_rag_example()
result = chain.invoke("你的问题")
```

### 示例2：Agent系统

#### ✅ 正确的混合使用
```python
# 工具定义用函数调用
search_tool = DuckDuckGoSearchRun()
calculator_tool = LLMMathChain.from_llm(llm)

tools = [search_tool, calculator_tool]

# Agent执行用LCEL链式（如果支持）
agent = create_openai_functions_agent(llm, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools)

# 或者直接函数调用（传统方式）
result = agent_executor.invoke({"input": "搜索今天天气并计算温度转换"})
```

## 🎯 快速判断口诀

### "三问法"快速判断

1. **问数据流**：数据需要在多个组件间流动吗？
   - 是 → LCEL链式
   - 否 → 函数调用

2. **问组合性**：需要灵活组合和替换组件吗？
   - 是 → LCEL链式
   - 否 → 函数调用

3. **问执行时机**：需要延迟执行或流式处理吗？
   - 是 → LCEL链式
   - 否 → 函数调用

### 常见组件分类

#### 天然适合LCEL链式的组件
```python
# 这些组件设计就是为了链式调用
ChatPromptTemplate  # 提示模板
ChatOpenAI         # 语言模型
StrOutputParser    # 输出解析器
RunnablePassthrough # 数据传递
RunnableParallel   # 并行处理
```

#### 天然适合函数调用的组件
```python
# 这些组件设计就是为了独立使用
PyPDFLoader           # 文档加载器
RecursiveCharacterTextSplitter  # 文本分割器
Chroma               # 向量数据库
DuckDuckGoSearchRun  # 搜索工具
```

#### 两种模式都支持的组件
```python
# 这些组件可以灵活使用
retriever = vectorstore.as_retriever()

# 函数调用模式
docs = retriever.get_relevant_documents("query")

# LCEL链式模式
chain = retriever | format_docs | llm
```

## 💡 实践建议

### 1. 新手建议
- 先学会函数调用模式（更直观）
- 再学习LCEL链式模式（更强大）
- 逐步理解什么时候用哪种模式

### 2. 开发建议
- 原型阶段：多用函数调用（快速验证）
- 生产阶段：多用LCEL链式（更灵活）
- 混合使用：根据具体场景选择

### 3. 调试建议
```python
# LCEL链式调试
chain = prompt | llm | output_parser
# 可以分步调试
step1 = prompt.invoke(input_data)
step2 = llm.invoke(step1)
step3 = output_parser.invoke(step2)

# 函数调用调试
documents = loader.load()
print(f"Loaded {len(documents)} documents")
chunks = splitter.split_documents(documents)
print(f"Split into {len(chunks)} chunks")
```

## 🚀 总结

记住这个核心原则：

- **数据流处理** → LCEL链式（`|`）
- **工具性操作** → 函数调用（`.method()`）

当你不确定时，问自己：
1. 这个操作是在处理数据流吗？
2. 我需要组合多个组件吗？
3. 我需要延迟执行或流式处理吗？

如果任何一个答案是"是"，就用LCEL链式；否则用函数调用。

随着经验积累，这种判断会变得越来越自然！
