# LangChain语义分割实现指南

> RecursiveCharacterTextSplitter vs 语义分割的区别与实现

## 🎯 RecursiveCharacterTextSplitter的局限性

### 当前分割方式（基于字符数）
```python
from langchain_text_splitters import RecursiveCharacterTextSplitter

text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1000,        # 固定字符数
    chunk_overlap=200,      # 重叠字符数
    separators=["\n\n", "\n", " ", ""]  # 分割符优先级
)

chunks = text_splitter.split_text(text)
```

### 问题分析
```python
# 示例文档
text = """
第一章：人工智能概述
人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。

第二章：机器学习基础
机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。
监督学习是机器学习的一种方法...
"""

# RecursiveCharacterTextSplitter可能会这样分割：
# Chunk 1: "第一章：人工智能概述\n人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。\n\n第二章：机器学习基础\n机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。\n监督学习是机器学习的一种方法..."

# 问题：章节被强制分割，语义不完整
```

**局限性**：
1. **忽略语义边界**：可能在句子中间分割
2. **破坏逻辑结构**：章节、段落被强制分开
3. **上下文丢失**：相关内容被分散到不同块中

## 🧠 语义分割的实现方案

### 方案1：基于LLM的智能分割

```python
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from typing import List, Dict
import re

class SemanticTextSplitter:
    def __init__(self, llm=None, max_chunk_size=1000):
        self.llm = llm or ChatOpenAI(model="gpt-3.5-turbo")
        self.max_chunk_size = max_chunk_size
        
        self.prompt = ChatPromptTemplate.from_template("""
        请分析以下文本，将其分割成语义完整的块。每个块应该：
        1. 保持主题的完整性
        2. 不超过{max_size}个字符
        3. 在自然的语义边界处分割（如段落、章节、主题转换处）
        
        文本：
        {text}
        
        请返回JSON格式，包含分割后的文本块：
        {{
            "chunks": [
                {{"content": "第一个语义块的内容", "topic": "主题描述"}},
                {{"content": "第二个语义块的内容", "topic": "主题描述"}}
            ]
        }}
        """)
        
        self.parser = JsonOutputParser()
    
    def split_text(self, text: str) -> List[Dict[str, str]]:
        """使用LLM进行语义分割"""
        try:
            chain = self.prompt | self.llm | self.parser
            result = chain.invoke({
                "text": text,
                "max_size": self.max_chunk_size
            })
            return result.get("chunks", [])
        except Exception as e:
            print(f"LLM分割失败，回退到传统方法: {e}")
            return self._fallback_split(text)
    
    def _fallback_split(self, text: str) -> List[Dict[str, str]]:
        """回退到传统分割方法"""
        traditional_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.max_chunk_size,
            chunk_overlap=200
        )
        chunks = traditional_splitter.split_text(text)
        return [{"content": chunk, "topic": "未知主题"} for chunk in chunks]

# 使用示例
semantic_splitter = SemanticTextSplitter(max_chunk_size=1000)
semantic_chunks = semantic_splitter.split_text(document_text)

for i, chunk in enumerate(semantic_chunks):
    print(f"块 {i+1} - 主题: {chunk['topic']}")
    print(f"内容: {chunk['content'][:100]}...")
    print("-" * 50)
```

### 方案2：基于Embedding相似度的分割

```python
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from langchain_openai import OpenAIEmbeddings
from typing import List, Tuple

class EmbeddingBasedSplitter:
    def __init__(self, embedding_model=None, similarity_threshold=0.7):
        self.embeddings = embedding_model or OpenAIEmbeddings()
        self.similarity_threshold = similarity_threshold
    
    def split_text(self, text: str, sentence_chunk_size=3) -> List[str]:
        """基于embedding相似度的语义分割"""
        
        # 1. 按句子分割
        sentences = self._split_into_sentences(text)
        
        # 2. 创建句子组（每组包含几个句子）
        sentence_groups = self._create_sentence_groups(sentences, sentence_chunk_size)
        
        # 3. 计算每组的embedding
        group_embeddings = self.embeddings.embed_documents(sentence_groups)
        
        # 4. 基于相似度确定分割点
        split_points = self._find_split_points(group_embeddings)
        
        # 5. 根据分割点创建语义块
        semantic_chunks = self._create_semantic_chunks(sentence_groups, split_points)
        
        return semantic_chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """将文本分割成句子"""
        # 简单的句子分割（可以使用更复杂的NLP工具）
        sentences = re.split(r'[。！？.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _create_sentence_groups(self, sentences: List[str], group_size: int) -> List[str]:
        """创建句子组"""
        groups = []
        for i in range(0, len(sentences), group_size):
            group = ' '.join(sentences[i:i+group_size])
            groups.append(group)
        return groups
    
    def _find_split_points(self, embeddings: List[List[float]]) -> List[int]:
        """基于相似度找到分割点"""
        split_points = [0]  # 总是从0开始
        
        for i in range(1, len(embeddings)):
            # 计算当前组与前一组的相似度
            similarity = cosine_similarity(
                [embeddings[i-1]], 
                [embeddings[i]]
            )[0][0]
            
            # 如果相似度低于阈值，认为是新的语义段落
            if similarity < self.similarity_threshold:
                split_points.append(i)
        
        split_points.append(len(embeddings))  # 添加结束点
        return split_points
    
    def _create_semantic_chunks(self, groups: List[str], split_points: List[int]) -> List[str]:
        """根据分割点创建语义块"""
        chunks = []
        for i in range(len(split_points) - 1):
            start = split_points[i]
            end = split_points[i + 1]
            chunk = ' '.join(groups[start:end])
            chunks.append(chunk)
        return chunks

# 使用示例
embedding_splitter = EmbeddingBasedSplitter(similarity_threshold=0.6)
semantic_chunks = embedding_splitter.split_text(document_text)

for i, chunk in enumerate(semantic_chunks):
    print(f"语义块 {i+1}:")
    print(chunk[:200] + "...")
    print("-" * 50)
```

### 方案3：基于文档结构的分割

```python
from langchain_text_splitters import MarkdownHeaderTextSplitter
from typing import List, Dict
import re

class StructuralSemanticSplitter:
    def __init__(self, max_chunk_size=1000):
        self.max_chunk_size = max_chunk_size
    
    def split_markdown(self, markdown_text: str) -> List[Dict[str, str]]:
        """基于Markdown结构的语义分割"""
        headers_to_split_on = [
            ("#", "Header 1"),
            ("##", "Header 2"),
            ("###", "Header 3"),
        ]
        
        markdown_splitter = MarkdownHeaderTextSplitter(
            headers_to_split_on=headers_to_split_on
        )
        
        md_header_splits = markdown_splitter.split_text(markdown_text)
        
        # 进一步处理过长的块
        final_chunks = []
        for doc in md_header_splits:
            if len(doc.page_content) > self.max_chunk_size:
                # 对过长的块进行二次分割
                sub_chunks = self._split_long_content(doc.page_content, doc.metadata)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata
                })
        
        return final_chunks
    
    def split_by_paragraphs(self, text: str) -> List[str]:
        """基于段落的语义分割"""
        # 按段落分割
        paragraphs = text.split('\n\n')
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            # 如果添加当前段落不会超过限制
            if len(current_chunk + paragraph) <= self.max_chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                # 保存当前块，开始新块
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = paragraph + "\n\n"
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def split_by_topics(self, text: str) -> List[Dict[str, str]]:
        """基于主题标识的分割"""
        # 识别主题标识符
        topic_patterns = [
            r'^第[一二三四五六七八九十\d]+章[：:]',  # 章节
            r'^第[一二三四五六七八九十\d]+节[：:]',  # 小节
            r'^\d+\.\s',                           # 数字编号
            r'^[一二三四五六七八九十]\s*[、.]',      # 中文编号
        ]
        
        lines = text.split('\n')
        chunks = []
        current_chunk = ""
        current_topic = "未知主题"
        
        for line in lines:
            # 检查是否是新主题
            is_new_topic = any(re.match(pattern, line.strip()) for pattern in topic_patterns)
            
            if is_new_topic and current_chunk:
                # 保存当前块
                chunks.append({
                    "content": current_chunk.strip(),
                    "topic": current_topic
                })
                current_chunk = ""
                current_topic = line.strip()
            
            current_chunk += line + "\n"
        
        # 添加最后一个块
        if current_chunk:
            chunks.append({
                "content": current_chunk.strip(),
                "topic": current_topic
            })
        
        return chunks
    
    def _split_long_content(self, content: str, metadata: dict) -> List[Dict[str, str]]:
        """分割过长的内容"""
        # 使用传统方法分割过长内容
        traditional_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.max_chunk_size,
            chunk_overlap=200
        )
        
        sub_chunks = traditional_splitter.split_text(content)
        return [
            {
                "content": chunk,
                "metadata": {**metadata, "sub_chunk": i}
            }
            for i, chunk in enumerate(sub_chunks)
        ]

# 使用示例
structural_splitter = StructuralSemanticSplitter(max_chunk_size=1000)

# Markdown文档分割
if document_text.startswith('#'):
    chunks = structural_splitter.split_markdown(document_text)
else:
    # 普通文档分割
    chunks = structural_splitter.split_by_topics(document_text)

for chunk in chunks:
    print(f"主题: {chunk.get('topic', '未知')}")
    print(f"内容: {chunk['content'][:100]}...")
    print("-" * 50)
```

### 方案4：混合语义分割器

```python
class HybridSemanticSplitter:
    def __init__(self, max_chunk_size=1000, use_llm=True):
        self.max_chunk_size = max_chunk_size
        self.use_llm = use_llm
        
        # 初始化各种分割器
        self.llm_splitter = SemanticTextSplitter(max_chunk_size=max_chunk_size) if use_llm else None
        self.embedding_splitter = EmbeddingBasedSplitter()
        self.structural_splitter = StructuralSemanticSplitter(max_chunk_size=max_chunk_size)
        self.traditional_splitter = RecursiveCharacterTextSplitter(
            chunk_size=max_chunk_size,
            chunk_overlap=200
        )
    
    def split_text(self, text: str, method="auto") -> List[Dict[str, str]]:
        """智能选择分割方法"""
        
        if method == "auto":
            method = self._choose_best_method(text)
        
        try:
            if method == "llm" and self.llm_splitter:
                return self.llm_splitter.split_text(text)
            elif method == "embedding":
                chunks = self.embedding_splitter.split_text(text)
                return [{"content": chunk, "method": "embedding"} for chunk in chunks]
            elif method == "structural":
                if text.startswith('#'):
                    return self.structural_splitter.split_markdown(text)
                else:
                    return self.structural_splitter.split_by_topics(text)
            else:
                # 回退到传统方法
                chunks = self.traditional_splitter.split_text(text)
                return [{"content": chunk, "method": "traditional"} for chunk in chunks]
                
        except Exception as e:
            print(f"分割方法 {method} 失败，使用传统方法: {e}")
            chunks = self.traditional_splitter.split_text(text)
            return [{"content": chunk, "method": "fallback"} for chunk in chunks]
    
    def _choose_best_method(self, text: str) -> str:
        """自动选择最佳分割方法"""
        # Markdown文档
        if text.startswith('#') or '##' in text:
            return "structural"
        
        # 有明显章节结构
        if re.search(r'第[一二三四五六七八九十\d]+章', text):
            return "structural"
        
        # 文档较短，使用LLM
        if len(text) < 5000 and self.use_llm:
            return "llm"
        
        # 文档较长，使用embedding
        if len(text) > 5000:
            return "embedding"
        
        # 默认使用传统方法
        return "traditional"

# 使用示例
hybrid_splitter = HybridSemanticSplitter(max_chunk_size=1000, use_llm=True)

# 自动选择最佳方法
chunks = hybrid_splitter.split_text(document_text, method="auto")

# 或指定特定方法
# chunks = hybrid_splitter.split_text(document_text, method="llm")

for i, chunk in enumerate(chunks):
    print(f"块 {i+1} (方法: {chunk.get('method', 'unknown')}):")
    print(f"内容: {chunk['content'][:100]}...")
    print("-" * 50)
```

## 🎯 总结与建议

### 各方案对比

| 方案 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **LLM分割** | 语义理解最好 | 成本高、速度慢 | 高质量要求、文档较短 |
| **Embedding分割** | 平衡效果和成本 | 需要调参 | 中等长度文档 |
| **结构化分割** | 快速、准确 | 依赖文档结构 | 有明确结构的文档 |
| **混合方案** | 自适应、鲁棒 | 复杂度高 | 生产环境推荐 |

### 实际项目建议

1. **开发阶段**：使用混合方案，快速验证效果
2. **生产环境**：根据文档类型选择最适合的方法
3. **成本考虑**：优先使用结构化和embedding方法
4. **质量要求高**：使用LLM方法进行精细分割

语义分割确实比简单的字符分割更复杂，但效果也更好！
