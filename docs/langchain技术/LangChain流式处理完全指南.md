# LangChain流式处理完全指南

> 深入解析LangChain流式处理的原理、实现和最佳实践

## 📋 目录

- [1. 流式处理基础概念](#1-流式处理基础概念)
- [2. 常见问题分析](#2-常见问题分析)
- [3. 基础实现方案](#3-基础实现方案)
- [4. 高级实现方案](#4-高级实现方案)
- [5. 生产级实现](#5-生产级实现)
- [6. 性能优化策略](#6-性能优化策略)
- [7. 最佳实践总结](#7-最佳实践总结)

---

## 1. 流式处理基础概念

### 1.1 什么是流式处理？

流式处理是指数据以连续的流的形式进行处理和传输，而不是等待完整的响应后一次性返回。在LangChain中，流式处理主要用于：

- **实时响应**：用户可以立即看到AI的回答开始生成
- **改善体验**：减少用户等待时间，提供更好的交互体验
- **资源优化**：避免长时间占用连接资源

### 1.2 LangChain中的流式处理机制

```python
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

# 启用流式处理的关键配置
llm = ChatOpenAI(
    model="gpt-3.5-turbo",
    streaming=True,  # 🔑 关键配置
    temperature=0.7
)

# 创建处理链
prompt = ChatPromptTemplate.from_template("请解释：{topic}")
chain = prompt | llm
```

### 1.3 同步 vs 异步流式处理

```python
# 同步流式处理
for chunk in chain.stream({"topic": "人工智能"}):
    print(chunk.content, end="", flush=True)

# 异步流式处理
async for chunk in chain.astream({"topic": "人工智能"}):
    print(chunk.content, end="", flush=True)
```

---

## 2. 常见问题分析

### 2.1 ❌ 错误示例：不必要的延迟

**问题代码**：
```python
async def async_stream():
    async for chunk in chain.astream({"topic": "机器学习"}):
        print(chunk, end="", flush=True)
        await asyncio.sleep(0.01)  # ❌ 不必要的延迟
```

**问题分析**：
1. **性能损失**：每个chunk都被人为延迟10毫秒
2. **资源浪费**：在高并发场景下浪费CPU时间
3. **概念混淆**：`asyncio.sleep()`不是`yield`的替代品
4. **用户体验差**：降低了响应的实时性

### 2.2 ✅ 正确的实现方式

```python
async def correct_async_stream():
    async for chunk in chain.astream({"topic": "机器学习"}):
        print(chunk.content, end="", flush=True)
        # 如果需要让出控制权，使用：
        await asyncio.sleep(0)  # 立即让出，无延迟
```

### 2.3 为什么会出现这种错误？

1. **概念混淆**：
   - `yield`：生成器产生值的关键字
   - `asyncio.sleep()`：异步等待，让出控制权
   - `chain.astream()`内部已经使用了异步生成器

2. **误解目的**：
   - 可能想模拟"真实"的处理时间
   - 可能想控制输出速度
   - 但这些都不应该通过人为延迟实现

---

## 3. 基础实现方案

### 3.1 优化的基础流式实现

```python
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import asyncio
from typing import AsyncGenerator, Optional
import logging

class OptimizedStreamingChain:
    def __init__(self, model: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(
            model=model,
            streaming=True,
            temperature=0.7,
            request_timeout=30,  # 添加超时
            max_retries=3        # 添加重试
        )
        self.prompt = ChatPromptTemplate.from_template("请详细解释：{topic}")
        self.chain = self.prompt | self.llm | StrOutputParser()
        self.logger = logging.getLogger(__name__)

    def sync_stream(self, topic: str):
        """同步流式处理"""
        try:
            for chunk in self.chain.stream({"topic": topic}):
                print(chunk, end="", flush=True)
        except Exception as e:
            print(f"\n❌ 处理出错: {str(e)}")
            self.logger.error(f"Sync stream error: {e}")

    async def async_stream(self, topic: str):
        """异步流式处理"""
        try:
            async for chunk in self.chain.astream({"topic": topic}):
                print(chunk, end="", flush=True)
        except Exception as e:
            print(f"\n❌ 处理出错: {str(e)}")
            self.logger.error(f"Async stream error: {e}")
```

### 3.2 带错误处理的流式实现

```python
async def stream_with_error_handling(
    self, 
    topic: str, 
    timeout: Optional[float] = None
) -> AsyncGenerator[str, None]:
    """带完整错误处理的流式实现"""
    try:
        # 使用asyncio.timeout进行超时控制
        timeout_context = asyncio.timeout(timeout) if timeout else asyncio.nullcontext()
        
        async with timeout_context:
            async for chunk in self.chain.astream({"topic": topic}):
                yield chunk
                # 只在需要时让出控制权，无延迟
                await asyncio.sleep(0)
                
    except asyncio.TimeoutError:
        yield "⚠️ 响应超时，请重试"
        self.logger.warning(f"Stream timeout for topic: {topic}")
    except Exception as e:
        yield f"❌ 处理出错: {str(e)}"
        self.logger.error(f"Stream error: {e}")
```

### 3.3 支持取消的流式实现

```python
async def stream_with_cancellation(
    self, 
    topic: str, 
    cancel_event: asyncio.Event
) -> AsyncGenerator[str, None]:
    """支持取消的流式实现"""
    try:
        async for chunk in self.chain.astream({"topic": topic}):
            # 检查取消信号
            if cancel_event.is_set():
                yield "🛑 操作已取消"
                break
            yield chunk
    except Exception as e:
        yield f"❌ 处理出错: {str(e)}"

# 使用示例
async def main():
    chain = OptimizedStreamingChain()
    cancel_event = asyncio.Event()
    
    # 创建流式处理任务
    stream_task = asyncio.create_task(
        process_stream(chain, "人工智能", cancel_event)
    )
    
    # 5秒后取消
    await asyncio.sleep(5)
    cancel_event.set()
    
    await stream_task

async def process_stream(chain, topic, cancel_event):
    async for chunk in chain.stream_with_cancellation(topic, cancel_event):
        print(chunk, end="", flush=True)
```

---

## 4. 高级实现方案

### 4.1 WebSocket流式实现

```python
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
import json
import asyncio

app = FastAPI()

class WebSocketStreamingManager:
    def __init__(self):
        self.chain = OptimizedStreamingChain()
        self.active_connections: dict = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]

    async def send_stream(self, client_id: str, topic: str):
        """发送流式响应"""
        websocket = self.active_connections.get(client_id)
        if not websocket:
            return

        try:
            # 发送开始标识
            await websocket.send_text(json.dumps({
                "type": "start",
                "content": "开始生成回答..."
            }))
            
            # 流式发送响应
            async for chunk in self.chain.stream_with_error_handling(topic):
                await websocket.send_text(json.dumps({
                    "type": "chunk",
                    "content": chunk
                }))
            
            # 发送结束标识
            await websocket.send_text(json.dumps({
                "type": "end",
                "content": "回答完成"
            }))
            
        except Exception as e:
            await websocket.send_text(json.dumps({
                "type": "error",
                "content": f"服务器错误: {str(e)}"
            }))

manager = WebSocketStreamingManager()

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            topic = message.get("topic", "")
            
            if topic:
                await manager.send_stream(client_id, topic)
            else:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "content": "请提供话题"
                }))
                
    except WebSocketDisconnect:
        manager.disconnect(client_id)
```

### 4.2 Server-Sent Events (SSE) 实现

```python
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
import json

app = FastAPI()

@app.post("/stream/sse")
async def sse_stream(topic: str):
    """Server-Sent Events流式实现"""
    
    async def event_generator():
        chain = OptimizedStreamingChain()
        
        try:
            # 发送开始事件
            yield {
                "event": "start",
                "data": json.dumps({"message": "开始生成回答"})
            }
            
            # 流式发送内容
            async for chunk in chain.stream_with_error_handling(topic):
                yield {
                    "event": "chunk",
                    "data": json.dumps({"content": chunk})
                }
            
            # 发送结束事件
            yield {
                "event": "end", 
                "data": json.dumps({"message": "回答完成"})
            }
            
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
    
    return EventSourceResponse(event_generator())

# 前端JavaScript使用示例
"""
const eventSource = new EventSource('/stream/sse?topic=人工智能');

eventSource.addEventListener('start', function(event) {
    console.log('开始接收:', JSON.parse(event.data));
});

eventSource.addEventListener('chunk', function(event) {
    const data = JSON.parse(event.data);
    document.getElementById('output').innerHTML += data.content;
});

eventSource.addEventListener('end', function(event) {
    console.log('接收完成:', JSON.parse(event.data));
    eventSource.close();
});

eventSource.addEventListener('error', function(event) {
    console.error('错误:', JSON.parse(event.data));
    eventSource.close();
});
"""
```

### 4.3 带缓冲的流式处理

```python
import asyncio
from collections import deque
from typing import AsyncGenerator

class BufferedStreamingChain:
    def __init__(self, buffer_size: int = 5, flush_interval: float = 0.1):
        self.chain = OptimizedStreamingChain()
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval

    async def stream_with_buffer(
        self, 
        topic: str
    ) -> AsyncGenerator[str, None]:
        """带缓冲的流式处理，减少网络请求次数"""
        buffer = deque()
        last_flush = asyncio.get_event_loop().time()
        
        async for chunk in self.chain.stream_with_error_handling(topic):
            buffer.append(chunk)
            current_time = asyncio.get_event_loop().time()
            
            # 缓冲区满了或者超过刷新间隔
            should_flush = (
                len(buffer) >= self.buffer_size or 
                current_time - last_flush >= self.flush_interval
            )
            
            if should_flush:
                # 批量输出
                combined_chunk = "".join(buffer)
                buffer.clear()
                last_flush = current_time
                yield combined_chunk
        
        # 输出剩余内容
        if buffer:
            yield "".join(buffer)

# 使用示例
async def demo_buffered_streaming():
    buffered_chain = BufferedStreamingChain(buffer_size=3, flush_interval=0.2)
    
    async for chunk in buffered_chain.stream_with_buffer("深度学习"):
        print(f"[批次] {chunk}")
        print("---")
```

---

## 5. 生产级实现

### 5.1 多用户并发流式处理

```python
import asyncio
from typing import Dict, Set, Callable
import uuid
from dataclasses import dataclass
import time

@dataclass
class StreamSession:
    session_id: str
    user_id: str
    topic: str
    start_time: float
    task: asyncio.Task
    callback: Callable

class ConcurrentStreamingManager:
    def __init__(self, max_concurrent_streams: int = 100):
        self.max_concurrent_streams = max_concurrent_streams
        self.active_streams: Dict[str, StreamSession] = {}
        self.user_sessions: Dict[str, Set[str]] = {}
        self.chain = OptimizedStreamingChain()

    async def start_stream(
        self, 
        user_id: str, 
        topic: str, 
        callback: Callable[[str, str], None]
    ) -> str:
        """为用户启动新的流式处理"""
        
        # 检查并发限制
        if len(self.active_streams) >= self.max_concurrent_streams:
            await callback("error", "服务器繁忙，请稍后重试")
            return None
        
        session_id = str(uuid.uuid4())
        
        # 记录用户会话
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = set()
        self.user_sessions[user_id].add(session_id)
        
        # 创建流式处理任务
        task = asyncio.create_task(
            self._process_stream(session_id, user_id, topic, callback)
        )
        
        # 记录会话信息
        session = StreamSession(
            session_id=session_id,
            user_id=user_id,
            topic=topic,
            start_time=time.time(),
            task=task,
            callback=callback
        )
        self.active_streams[session_id] = session
        
        return session_id

    async def _process_stream(
        self, 
        session_id: str, 
        user_id: str, 
        topic: str, 
        callback: Callable
    ):
        """处理单个流式请求"""
        try:
            await callback(session_id, "start")
            
            async for chunk in self.chain.stream_with_error_handling(topic):
                await callback(session_id, chunk)
                
            await callback(session_id, "end")
            
        except asyncio.CancelledError:
            await callback(session_id, "cancelled")
        except Exception as e:
            await callback(session_id, f"error: {str(e)}")
        finally:
            # 清理会话
            await self._cleanup_session(session_id)

    async def cancel_stream(self, session_id: str) -> bool:
        """取消指定的流式处理"""
        if session_id in self.active_streams:
            session = self.active_streams[session_id]
            session.task.cancel()
            return True
        return False

    async def cancel_user_streams(self, user_id: str) -> int:
        """取消用户的所有流式处理"""
        cancelled_count = 0
        if user_id in self.user_sessions:
            for session_id in self.user_sessions[user_id].copy():
                if await self.cancel_stream(session_id):
                    cancelled_count += 1
        return cancelled_count

    async def _cleanup_session(self, session_id: str):
        """清理会话信息"""
        if session_id in self.active_streams:
            session = self.active_streams[session_id]
            user_id = session.user_id
            
            # 从活跃流中移除
            del self.active_streams[session_id]
            
            # 从用户会话中移除
            if user_id in self.user_sessions:
                self.user_sessions[user_id].discard(session_id)
                if not self.user_sessions[user_id]:
                    del self.user_sessions[user_id]

    def get_status(self) -> dict:
        """获取管理器状态"""
        return {
            "active_streams": len(self.active_streams),
            "max_concurrent": self.max_concurrent_streams,
            "users_count": len(self.user_sessions),
            "sessions_by_user": {
                user_id: len(sessions) 
                for user_id, sessions in self.user_sessions.items()
            }
        }

# 使用示例
async def demo_concurrent_streaming():
    manager = ConcurrentStreamingManager(max_concurrent_streams=10)
    
    async def stream_callback(session_id: str, content: str):
        print(f"[{session_id[:8]}] {content}")
    
    # 启动多个并发流
    tasks = []
    for i in range(5):
        session_id = await manager.start_stream(
            user_id=f"user_{i}",
            topic=f"主题_{i}",
            callback=stream_callback
        )
        if session_id:
            print(f"启动会话: {session_id}")
    
    # 等待所有流完成
    await asyncio.sleep(10)
    
    # 查看状态
    print("管理器状态:", manager.get_status())
```

### 5.2 带监控的生产级实现

```python
import time
import psutil
from dataclasses import dataclass, field
from typing import Optional, Dict
import json

@dataclass
class StreamMetrics:
    session_id: str
    user_id: str
    topic: str
    start_time: float
    end_time: Optional[float] = None
    chunks_sent: int = 0
    bytes_sent: int = 0
    errors: int = 0
    cancelled: bool = False
    
    @property
    def duration(self) -> float:
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def throughput(self) -> float:
        """每秒字节数"""
        duration = self.duration
        return self.bytes_sent / duration if duration > 0 else 0

class ProductionStreamingChain:
    def __init__(self, max_concurrent_streams: int = 100):
        self.chain = OptimizedStreamingChain()
        self.max_concurrent_streams = max_concurrent_streams
        self.current_streams = 0
        self.metrics: Dict[str, StreamMetrics] = {}
        self.total_sessions = 0
        
    async def stream_with_monitoring(
        self, 
        topic: str, 
        user_id: str,
        session_id: Optional[str] = None,
        max_response_time: Optional[float] = 30.0
    ) -> AsyncGenerator[str, None]:
        """带监控的生产级流式实现"""
        
        # 检查并发限制
        if self.current_streams >= self.max_concurrent_streams:
            yield "⚠️ 服务器繁忙，请稍后重试"
            return
        
        if not session_id:
            session_id = f"{user_id}_{int(time.time())}_{self.total_sessions}"
        
        self.current_streams += 1
        self.total_sessions += 1
        
        # 初始化指标
        metrics = StreamMetrics(
            session_id=session_id,
            user_id=user_id,
            topic=topic,
            start_time=time.time()
        )
        self.metrics[session_id] = metrics
        
        try:
            start_time = time.time()
            
            async for chunk in self.chain.stream_with_error_handling(
                topic, timeout=max_response_time
            ):
                # 更新指标
                metrics.chunks_sent += 1
                chunk_bytes = len(chunk.encode('utf-8'))
                metrics.bytes_sent += chunk_bytes
                
                # 系统监控
                memory_percent = psutil.virtual_memory().percent
                if memory_percent > 90:
                    yield "⚠️ 系统内存不足，响应可能较慢"
                
                yield chunk
                
                # 检查响应时间
                if max_response_time and time.time() - start_time > max_response_time:
                    yield "⚠️ 响应时间过长，已截断"
                    break
                    
        except asyncio.CancelledError:
            metrics.cancelled = True
            yield "🛑 请求已取消"
        except Exception as e:
            metrics.errors += 1
            yield f"❌ 处理出错: {str(e)}"
        finally:
            # 完成指标记录
            metrics.end_time = time.time()
            self.current_streams -= 1
            
            # 记录会话统计
            self._log_session_stats(metrics)

    def _log_session_stats(self, metrics: StreamMetrics):
        """记录会话统计信息"""
        stats = {
            "session_id": metrics.session_id,
            "user_id": metrics.user_id,
            "duration": metrics.duration,
            "chunks_sent": metrics.chunks_sent,
            "bytes_sent": metrics.bytes_sent,
            "throughput": metrics.throughput,
            "errors": metrics.errors,
            "cancelled": metrics.cancelled
        }
        
        print(f"会话完成: {json.dumps(stats, indent=2, ensure_ascii=False)}")

    def get_system_status(self) -> dict:
        """获取系统状态"""
        # 计算平均指标
        active_metrics = [m for m in self.metrics.values() if m.end_time is None]
        completed_metrics = [m for m in self.metrics.values() if m.end_time is not None]
        
        avg_duration = 0
        avg_throughput = 0
        total_errors = 0
        
        if completed_metrics:
            avg_duration = sum(m.duration for m in completed_metrics) / len(completed_metrics)
            avg_throughput = sum(m.throughput for m in completed_metrics) / len(completed_metrics)
            total_errors = sum(m.errors for m in completed_metrics)
        
        return {
            "system": {
                "current_streams": self.current_streams,
                "max_concurrent": self.max_concurrent_streams,
                "memory_usage": psutil.virtual_memory().percent,
                "cpu_usage": psutil.cpu_percent(),
            },
            "sessions": {
                "total_sessions": self.total_sessions,
                "active_sessions": len(active_metrics),
                "completed_sessions": len(completed_metrics),
            },
            "performance": {
                "avg_duration": round(avg_duration, 2),
                "avg_throughput": round(avg_throughput, 2),
                "total_errors": total_errors,
                "error_rate": round(total_errors / max(1, len(completed_metrics)) * 100, 2)
            }
        }

    def get_user_stats(self, user_id: str) -> dict:
        """获取用户统计信息"""
        user_metrics = [m for m in self.metrics.values() if m.user_id == user_id]
        
        if not user_metrics:
            return {"error": "用户未找到"}
        
        active = [m for m in user_metrics if m.end_time is None]
        completed = [m for m in user_metrics if m.end_time is not None]
        
        return {
            "user_id": user_id,
            "total_sessions": len(user_metrics),
            "active_sessions": len(active),
            "completed_sessions": len(completed),
            "total_bytes": sum(m.bytes_sent for m in user_metrics),
            "total_chunks": sum(m.chunks_sent for m in user_metrics),
            "avg_session_duration": round(
                sum(m.duration for m in completed) / max(1, len(completed)), 2
            )
        }

# 使用示例和测试
async def demo_production_streaming():
    production_chain = ProductionStreamingChain(max_concurrent_streams=5)
    
    # 模拟多用户并发请求
    async def simulate_user_request(user_id: str, topic: str):
        print(f"\n=== 用户 {user_id} 开始请求 ===")
        async for chunk in production_chain.stream_with_monitoring(
            topic=topic,
            user_id=user_id,
            max_response_time=10.0
        ):
            print(f"[{user_id}] {chunk[:50]}{'...' if len(chunk) > 50 else ''}")
        print(f"=== 用户 {user_id} 请求完成 ===\n")
    
    # 创建并发任务
    tasks = [
        simulate_user_request("user_1", "人工智能的发展历史"),
        simulate_user_request("user_2", "机器学习算法分类"),
        simulate_user_request("user_3", "深度学习应用场景"),
    ]
    
    # 并发执行
    await asyncio.gather(*tasks)
    
    # 查看系统状态
    print("\n=== 系统状态 ===")
    status = production_chain.get_system_status()
    print(json.dumps(status, indent=2, ensure_ascii=False))
    
    # 查看用户统计
    for user_id in ["user_1", "user_2", "user_3"]:
        print(f"\n=== {user_id} 统计 ===")
        user_stats = production_chain.get_user_stats(user_id)
        print(json.dumps(user_stats, indent=2, ensure_ascii=False))

# 运行演示
if __name__ == "__main__":
    asyncio.run(demo_production_streaming())
```

---

## 6. 性能优化策略

### 6.1 连接池优化

```python
import aiohttp
import asyncio
from langchain_openai import ChatOpenAI
from typing import AsyncGenerator

class OptimizedStreamingClient:
    def __init__(self, max_connections: int = 100):
        # 配置连接池
        connector = aiohttp.TCPConnector(
            limit=max_connections,           # 总连接数限制
            limit_per_host=20,              # 每个主机连接数限制
            keepalive_timeout=30,           # 保持连接时间
            enable_cleanup_closed=True,     # 启用连接清理
            ttl_dns_cache=300,              # DNS缓存时间
            use_dns_cache=True,             # 启用DNS缓存
        )

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(
                total=60,      # 总超时时间
                connect=10,    # 连接超时时间
                sock_read=30   # 读取超时时间
            )
        )

        self.llm = ChatOpenAI(
            model="gpt-3.5-turbo",
            streaming=True,
            max_retries=3,
            request_timeout=30,
            # 使用自定义HTTP客户端
            http_client=self.session
        )

    async def stream_with_pool(self, prompt: str) -> AsyncGenerator[str, None]:
        """使用连接池的流式处理"""
        try:
            async for chunk in self.llm.astream(prompt):
                yield chunk.content
        except Exception as e:
            yield f"错误: {e}"

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()

# 使用示例
async def demo_connection_pool():
    async with OptimizedStreamingClient(max_connections=50) as client:
        # 并发处理多个请求
        tasks = []
        for i in range(10):
            task = asyncio.create_task(
                process_stream(client, f"主题 {i}")
            )
            tasks.append(task)
        
        await asyncio.gather(*tasks)

async def process_stream(client, topic):
    async for chunk in client.stream_with_pool(f"请解释: {topic}"):
        print(f"[{topic}] {chunk[:30]}...")
```

### 6.2 批处理优化

```python
import asyncio
from typing import List, Dict, Any
from dataclasses import dataclass
from collections import deque

@dataclass
class StreamRequest:
    id: str
    prompt: str
    callback: callable
    priority: int = 0

class BatchStreamProcessor:
    def __init__(self, 
                 batch_size: int = 5, 
                 batch_timeout: float = 1.0,
                 max_queue_size: int = 100):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.max_queue_size = max_queue_size
        
        self.pending_requests: deque = deque()
        self.processing = False
        self.chain = OptimizedStreamingChain()
        
        # 启动批处理循环
        self._batch_task = asyncio.create_task(self._batch_processing_loop())

    async def add_request(self, request: StreamRequest) -> bool:
        """添加流式请求到批处理队列"""
        if len(self.pending_requests) >= self.max_queue_size:
            await request.callback(request.id, "队列已满，请稍后重试")
            return False
        
        # 按优先级插入
        inserted = False
        for i, existing_req in enumerate(self.pending_requests):
            if request.priority > existing_req.priority:
                self.pending_requests.insert(i, request)
                inserted = True
                break
        
        if not inserted:
            self.pending_requests.append(request)
        
        return True

    async def _batch_processing_loop(self):
        """批处理循环"""
        while True:
            try:
                if self.pending_requests and not self.processing:
                    await self._process_batch()
                await asyncio.sleep(0.1)  # 避免忙等待
            except Exception as e:
                print(f"批处理循环错误: {e}")

    async def _process_batch(self):
        """处理一批请求"""
        if not self.pending_requests or self.processing:
            return

        self.processing = True
        
        # 获取当前批次
        current_batch = []
        batch_start_time = asyncio.get_event_loop().time()
        
        while (self.pending_requests and 
               len(current_batch) < self.batch_size):
            current_batch.append(self.pending_requests.popleft())
        
        # 如果没有达到批次大小，检查是否超时
        if (len(current_batch) < self.batch_size and 
            asyncio.get_event_loop().time() - batch_start_time < self.batch_timeout):
            # 放回队列，等待更多请求
            for req in reversed(current_batch):
                self.pending_requests.appendleft(req)
            self.processing = False
            return

        print(f"处理批次: {len(current_batch)} 个请求")

        # 并行处理批次中的请求
        tasks = [
            self._process_single_request(req)
            for req in current_batch
        ]

        await asyncio.gather(*tasks, return_exceptions=True)
        self.processing = False

    async def _process_single_request(self, request: StreamRequest):
        """处理单个流式请求"""
        try:
            await request.callback(request.id, "开始处理...")
            
            async for chunk in self.chain.stream_with_error_handling(request.prompt):
                await request.callback(request.id, chunk)
                
            await request.callback(request.id, "处理完成")
            
        except Exception as e:
            await request.callback(request.id, f"错误: {e}")

    async def shutdown(self):
        """关闭批处理器"""
        self._batch_task.cancel()
        try:
            await self._batch_task
        except asyncio.CancelledError:
            pass

# 使用示例
async def demo_batch_processing():
    processor = BatchStreamProcessor(batch_size=3, batch_timeout=2.0)
    
    async def stream_callback(request_id: str, content: str):
        print(f"[{request_id}] {content}")
    
    # 添加多个请求
    requests = [
        StreamRequest(id=f"req_{i}", prompt=f"解释主题{i}", callback=stream_callback, priority=i)
        for i in range(8)
    ]
    
    for req in requests:
        await processor.add_request(req)
        await asyncio.sleep(0.5)  # 模拟请求间隔
    
    # 等待处理完成
    await asyncio.sleep(10)
    await processor.shutdown()
```

### 6.3 缓存策略

```python
import hashlib
import json
import time
from typing import Optional, List, Dict, Any
import aioredis
from dataclasses import dataclass, asdict

@dataclass
class CacheEntry:
    content: List[str]
    timestamp: float
    hit_count: int = 0
    user_id: Optional[str] = None

class StreamingCache:
    def __init__(self, 
                 redis_url: str = "redis://localhost:6379",
                 default_ttl: int = 3600,
                 max_cache_size: int = 10000):
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self.max_cache_size = max_cache_size
        self.redis = None
        
        # 本地缓存（LRU）
        self.local_cache: Dict[str, CacheEntry] = {}
        self.cache_access_order: List[str] = []

    async def connect(self):
        """连接Redis"""
        if not self.redis:
            self.redis = await aioredis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20
            )

    def _get_cache_key(self, prompt: str, model: str, **kwargs) -> str:
        """生成缓存键"""
        # 包含所有影响结果的参数
        cache_data = {
            "prompt": prompt,
            "model": model,
            **kwargs
        }
        content = json.dumps(cache_data, sort_keys=True)
        return f"stream_cache:{hashlib.md5(content.encode()).hexdigest()}"

    async def get_cached_stream(
        self, 
        prompt: str, 
        model: str, 
        **kwargs
    ) -> Optional[List[str]]:
        """获取缓存的流式响应"""
        cache_key = self._get_cache_key(prompt, model, **kwargs)
        
        # 先检查本地缓存
        if cache_key in self.local_cache:
            entry = self.local_cache[cache_key]
            entry.hit_count += 1
            
            # 更新访问顺序
            if cache_key in self.cache_access_order:
                self.cache_access_order.remove(cache_key)
            self.cache_access_order.append(cache_key)
            
            return entry.content

        # 检查Redis缓存
        if not self.redis:
            await self.connect()

        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                entry_dict = json.loads(cached_data)
                entry = CacheEntry(**entry_dict)
                
                # 添加到本地缓存
                await self._add_to_local_cache(cache_key, entry)
                
                return entry.content
        except Exception as e:
            print(f"Redis缓存读取错误: {e}")

        return None

    async def cache_stream_response(
        self, 
        prompt: str, 
        model: str, 
        chunks: List[str],
        user_id: Optional[str] = None,
        ttl: Optional[int] = None
    ):
        """缓存流式响应"""
        cache_key = self._get_cache_key(prompt, model)
        ttl = ttl or self.default_ttl
        
        entry = CacheEntry(
            content=chunks,
            timestamp=time.time(),
            user_id=user_id
        )

        # 添加到本地缓存
        await self._add_to_local_cache(cache_key, entry)

        # 添加到Redis缓存
        if not self.redis:
            await self.connect()

        try:
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(asdict(entry), ensure_ascii=False)
            )
        except Exception as e:
            print(f"Redis缓存写入错误: {e}")

    async def _add_to_local_cache(self, cache_key: str, entry: CacheEntry):
        """添加到本地缓存（LRU策略）"""
        # 如果缓存已满，移除最少使用的条目
        if len(self.local_cache) >= self.max_cache_size:
            if self.cache_access_order:
                oldest_key = self.cache_access_order.pop(0)
                if oldest_key in self.local_cache:
                    del self.local_cache[oldest_key]

        self.local_cache[cache_key] = entry
        
        # 更新访问顺序
        if cache_key in self.cache_access_order:
            self.cache_access_order.remove(cache_key)
        self.cache_access_order.append(cache_key)

    async def stream_with_cache(
        self, 
        prompt: str, 
        model: str = "gpt-3.5-turbo",
        user_id: Optional[str] = None,
        force_refresh: bool = False
    ) -> AsyncGenerator[str, None]:
        """带缓存的流式处理"""
        
        # 检查是否强制刷新
        if not force_refresh:
            cached_chunks = await self.get_cached_stream(prompt, model)
            
            if cached_chunks:
                print("🎯 缓存命中")
                # 模拟流式输出缓存内容
                for chunk in cached_chunks:
                    yield chunk
                    await asyncio.sleep(0.02)  # 模拟流式延迟
                return

        print("🔄 缓存未命中，调用API")
        
        # 缓存未命中，实际调用API
        chain = OptimizedStreamingChain()
        chunks = []
        
        async for chunk in chain.stream_with_error_handling(prompt):
            chunks.append(chunk)
            yield chunk

        # 缓存结果
        if chunks:
            await self.cache_stream_response(prompt, model, chunks, user_id)

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        total_hits = sum(entry.hit_count for entry in self.local_cache.values())
        
        return {
            "local_cache_size": len(self.local_cache),
            "max_cache_size": self.max_cache_size,
            "total_hits": total_hits,
            "cache_utilization": len(self.local_cache) / self.max_cache_size * 100,
            "avg_hits_per_entry": total_hits / max(1, len(self.local_cache))
        }

# 使用示例
async def demo_streaming_cache():
    cache = StreamingCache(max_cache_size=100)
    
    # 第一次请求（缓存未命中）
    print("=== 第一次请求 ===")
    async for chunk in cache.stream_with_cache("什么是人工智能？", user_id="user_1"):
        print(chunk[:50], end="")
    print("\n")
    
    # 第二次相同请求（缓存命中）
    print("=== 第二次相同请求 ===")
    async for chunk in cache.stream_with_cache("什么是人工智能？", user_id="user_1"):
        print(chunk[:50], end="")
    print("\n")
    
    # 查看缓存统计
    print("=== 缓存统计 ===")
    stats = cache.get_cache_stats()
    print(json.dumps(stats, indent=2, ensure_ascii=False))
```

---

## 7. 最佳实践总结

### 7.1 ✅ 推荐做法

1. **正确的异步流式实现**：
```python
# ✅ 推荐
async def correct_stream():
    async for chunk in chain.astream({"topic": "AI"}):
        print(chunk, end="", flush=True)
        # 如需让出控制权：await asyncio.sleep(0)
```

2. **完整的错误处理**：
```python
# ✅ 推荐
async def robust_stream():
    try:
        async with asyncio.timeout(30):
            async for chunk in chain.astream({"topic": "AI"}):
                yield chunk
    except asyncio.TimeoutError:
        yield "⚠️ 响应超时"
    except Exception as e:
        yield f"❌ 错误: {e}"
```

3. **支持取消操作**：
```python
# ✅ 推荐
async def cancellable_stream(cancel_event):
    async for chunk in chain.astream({"topic": "AI"}):
        if cancel_event.is_set():
            yield "🛑 已取消"
            break
        yield chunk
```

### 7.2 ❌ 避免的做法

1. **不要添加不必要的延迟**：
```python
# ❌ 避免
async def bad_stream():
    async for chunk in chain.astream({"topic": "AI"}):
        print(chunk, end="", flush=True)
        await asyncio.sleep(0.01)  # 不必要的延迟
```

2. **不要忽略错误处理**：
```python
# ❌ 避免
async def fragile_stream():
    async for chunk in chain.astream({"topic": "AI"}):
        print(chunk, end="", flush=True)
        # 没有任何错误处理
```

3. **不要阻塞事件循环**：
```python
# ❌ 避免
def blocking_stream():
    for chunk in chain.stream({"topic": "AI"}):
        time.sleep(0.1)  # 阻塞操作
        print(chunk, end="", flush=True)
```

### 7.3 性能优化建议

1. **使用连接池**：复用HTTP连接，减少连接开销
2. **实现缓存机制**：缓存常见查询结果
3. **批处理请求**：合并多个请求减少API调用
4. **监控系统资源**：及时发现性能瓶颈
5. **设置合理超时**：避免长时间等待
6. **支持取消操作**：提高用户体验

### 7.4 生产环境部署建议

1. **负载均衡**：使用多个实例分担负载
2. **监控告警**：监控关键指标并设置告警
3. **日志记录**：记录详细的操作日志
4. **安全防护**：实现认证授权和频率限制
5. **容错机制**：实现重试和降级策略
6. **资源限制**：设置合理的并发和资源限制

---

## 📚 参考资源

- [LangChain官方文档 - Streaming](https://python.langchain.com/docs/expression_language/streaming)
- [FastAPI WebSocket文档](https://fastapi.tiangolo.com/advanced/websockets/)
- [Server-Sent Events规范](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [asyncio官方文档](https://docs.python.org/3/library/asyncio.html)

---

*本文档持续更新中，如有问题或建议，欢迎反馈。*