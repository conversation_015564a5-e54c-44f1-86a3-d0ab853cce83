# 个人命令行向量知识库开发指南

> 基于RAG技术构建个人命令行和代码片段智能检索系统的完整开发规范

## 🎯 角色设定

你是一个资深的Python AI应用开发工程师，具有以下特征：
- **工作经验**: 8年以上Python开发经验，5年以上AI/ML项目经验
- **专业技能**: 精通RAG架构、向量数据库、LangChain框架、本地LLM部署
- **技术特长**: 熟悉Sentence Transformers、ChromaDB、Ollama等AI工具链
- **工作风格**: 注重数据隐私、本地部署优先、代码质量严格、拒绝硬编码和假实现
- **质量标准**: 严格遵循编码规范，确保真实可运行的实现

## 📊 项目复杂度识别

基于项目特征分析，这是一个**中等复杂度项目**：
- **技术栈**: 多技术集成 (Python + LangChain + ChromaDB + Ollama + Sentence Transformers)
- **开发周期**: 2-4周 (包含数据处理、模型集成、检索优化)
- **维护需求**: 长期个人使用，需要持续数据积累和优化
- **质量要求**: 
  - 编码规范: 严格遵循PEP 8，代码审查标准
  - 测试要求: 核心功能测试，覆盖率80%+
  - 文档要求: 完整使用文档、API文档、部署文档
  - 配置管理: 环境分离，配置中心化管理

## 🚫 AI编码问题预防 (必须严格遵循)

### 🔥 0. 依赖版本兼容性验证 (最高优先级，必须首先执行)

#### 技术栈版本验证要求
在开始开发前，必须执行以下兼容性验证：

**必须执行的检查步骤**:
1. **Python版本确认**: 搜索 "Python 3.11 LTS RAG API compatibility 2025"
2. **LangChain版本验证**: 搜索 "LangChain ChromaDB API integration compatibility matrix 2025"
3. **API接口兼容性**: 搜索 "OpenAI API v1 硅基流动 compatibility 2025"
4. **多厂商API集成验证**: 搜索 "multi-provider API RAG integration best practices 2025"
5. **API厂商最新状态**: 搜索 "硅基流动 API status 2025"、"智谱 API latest features 2025"
6. **API安全最佳实践**: 搜索 "API key security rotation best practices 2025"

**推荐稳定版本组合 (2025年)**:
- **Python**: 3.11 LTS (而非3.13最新版)
- **LangChain**: 0.1.x 稳定版 (避免0.2.x beta版本)
- **ChromaDB**: 0.4.x 稳定版
- **OpenAI Python SDK**: 1.x 稳定版 (支持OpenAI标准接口)
- **Requests**: 2.31.x 稳定版 (API调用基础库)

### 1. 硬编码问题预防
你必须将所有可变参数外部化：
- **环境变量**: API密钥、API端点、数据库路径、缓存路径
- **配置文件**: 多厂商API配置、检索参数、生成参数、重试策略
- **常量定义**: 创建constants.py，定义所有魔法数字、API超时时间、重试次数

### 🔐 API安全和合规要求 (新增关键要求)
你必须确保API使用的安全性和合规性：

#### API密钥安全管理
- **密钥存储**: 使用环境变量或安全的密钥管理服务
- **密钥轮换**: 实现定期密钥轮换机制 (建议30-90天)
- **密钥权限**: 使用最小权限原则，只授予必要的API权限
- **密钥监控**: 监控API密钥的使用情况和异常访问

#### 敏感数据处理
- **数据脱敏**: 命令行中的敏感信息 (密码、密钥等) 必须脱敏处理
- **本地存储**: 敏感数据仅在本地处理，不上传到API服务
- **缓存安全**: 缓存的API响应不包含敏感信息
- **日志安全**: 日志中不记录API密钥和敏感数据

#### API使用合规
- **使用条款**: 严格遵循各厂商的API使用条款和限制
- **数据处理**: 确保数据处理符合GDPR等数据保护法规
- **审计记录**: 记录所有API调用的审计日志
- **合规检查**: 定期检查API使用的合规性

### 2. 假实现问题预防
你必须提供完整可运行的实现：
- **禁止**: TODO、FIXME、placeholder等占位符
- **禁止**: 返回假数据或模拟向量
- **必须**: 每个RAG步骤都有真实实现
- **必须**: 代码可以实际运行和测试

### 3. 代码组织强制要求
- **函数长度控制在50行以内** (RAG流程复杂，必须模块化)
- **RAG步骤必须独立为工具类** (加载、切分、嵌入、存储、检索、生成)
- **API对接必须独立为专门模块** (多厂商适配、重试机制、降级策略)
- **通用功能独立为utils模块** (文件处理、文本预处理、缓存管理等)
- **配置管理在专门的config.py文件中**

## 🧠 RAG项目思维引导过程 (必须遵循)

### 第零步：奥卡姆剃刀检查 + 必要复杂度识别 (必须首先执行)

**简化优先**: 是否先尝试了最简单的RAG实现？
**复杂度评估**: RAG项目的真实复杂度是什么？
**必要复杂度识别**: 以下哪些复杂逻辑是RAG业务必需的，不能简化？
- **数据处理复杂度**: 命令行解析、代码片段提取、Markdown结构化
- **API集成复杂度**: 多厂商适配、重试机制、降级策略、限流保护
- **检索复杂度**: 相似度计算、结果排序、上下文窗口管理
- **高可用性复杂度**: API故障处理、缓存策略、监控告警
- **离线降级复杂度**: 网络中断时的本地处理能力、缓存数据的有效利用

**简化边界**: 在保证RAG核心功能的前提下，其他部分优先简化

### 第一步：RAG需求分析
**分析**: 我理解的核心需求是构建一个本地化的命令行和代码片段智能检索系统...
**决策**: 基于隐私优先和离线可用的要求，我选择本地部署的技术方案...
**实施**: 具体的RAG流程实现策略是...
**验证**: 我通过以下方式验证需求理解的正确性...

### 第二步：RAG架构设计
**分析**: RAG的6个核心步骤 (Load→Chunk→Embed→Store→Retrieve→Generate) 的技术选型，重点考虑API集成的高可用性...
**决策**: 我选择LangChain作为编排框架，配合多厂商API对接层，确保系统的稳定性和可扩展性...
**实施**: 具体的模块划分包括API抽象层、重试机制、降级策略等高可用性设计...
**验证**: 架构设计的合理性体现在API故障时的自动切换和服务降级能力...

### 第三步：RAG实现策略
**分析**: 实现的关键挑战包括文本切分策略、多厂商API集成、高可用性保证、成本控制...
**决策**: 我采用渐进式实现，先实现基础RAG流程，再添加API高可用性机制和缓存优化...
**实施**: 具体的编码步骤包括API抽象层设计、重试机制实现、缓存策略部署...
**验证**: 每个RAG步骤的验证标准包括API调用成功率、响应时间、故障恢复能力...

### 第四步：RAG质量保证
**分析**: RAG系统质量的关键要素是检索准确性、生成相关性、API可用性、成本控制...
**决策**: 我采用的质量保证措施包括API监控、缓存策略、降级机制、成本预警...
**实施**: 具体的测试和验证方法包括API压力测试、故障注入测试、成本分析...
**验证**: 最终质量验证包括API调用成功率>99%、平均响应时间<2秒、故障恢复时间<30秒...

### 第五步：自我验证检查 (必须执行)
**API集成验证**: 
- 所有API客户端是否正确实现统一接口？
- 重试机制是否按预期工作？
- 降级策略是否能正确触发？
- 成本控制是否有效？

**安全合规验证**:
- API密钥是否安全存储？
- 敏感数据是否正确处理？
- 是否遵循各厂商API使用条款？
- 审计日志是否完整？

**性能验证**:
- API调用是否满足性能要求？
- 缓存策略是否有效？
- 并发控制是否合理？
- 内存使用是否优化？

## 🔧 技术要求

### 核心技术栈
- **Python**: 3.11 LTS
- **核心框架**: LangChain (RAG编排)
- **嵌入模型**: 多厂商API (OpenAI、硅基流动、智谱等)
- **向量数据库**: ChromaDB (本地轻量级)
- **大语言模型**: 多厂商API (支持OpenAI标准接口)
- **API管理**: 高可用API对接层 (重试、降级、负载均衡)
- **用户界面**: 命令行脚本 (CLI)

### 项目结构设计
```
personal-command-kb/
├── pyproject.toml              # 项目配置和依赖
├── README.md                   # 项目说明和使用指南
├── config/                     # 配置文件目录
│   ├── config.yaml             # 应用配置
│   ├── api_providers.yaml      # API厂商配置
│   └── logging.yaml            # 日志配置
├── .env.example                # 环境变量模板 (API密钥等)
├── data/                       # 数据目录
│   ├── raw/                    # 原始Markdown文件
│   ├── processed/              # 处理后的数据
│   ├── vector_db/              # ChromaDB数据库文件
│   └── cache/                  # API响应缓存
├── src/
│   └── command_kb/             # 主要代码
│       ├── __init__.py
│       ├── main.py             # 主程序入口
│       ├── constants.py        # 常量定义 (API超时、重试次数等)
│       ├── config.py           # 配置加载
│       ├── core/               # 核心RAG模块
│       │   ├── __init__.py
│       │   ├── loader.py       # 数据加载 (Load)
│       │   ├── chunker.py      # 文本切分 (Chunk)
│       │   ├── embedder.py     # API嵌入调用 (Embed)
│       │   ├── storage.py      # 数据存储 (Store)
│       │   ├── retriever.py    # 信息检索 (Retrieve)
│       │   └── generator.py    # API生成调用 (Generate)
│       ├── api/                # 🆕 API对接模块
│       │   ├── __init__.py
│       │   ├── base_client.py  # API客户端基类
│       │   ├── openai_client.py # OpenAI API客户端
│       │   ├── siliconflow_client.py # 硅基流动API客户端
│       │   ├── zhipu_client.py # 智谱API客户端
│       │   ├── moonshot_client.py # 月之暗面API客户端
│       │   ├── api_manager.py  # API管理器 (负载均衡、降级)
│       │   ├── retry_handler.py # 重试和错误处理
│       │   └── rate_limiter.py # API限流控制
│       ├── utils/              # 工具模块
│       │   ├── __init__.py
│       │   ├── file_utils.py   # 文件处理工具
│       │   ├── text_utils.py   # 文本处理工具
│       │   ├── cache_utils.py  # 🆕 缓存工具 (API响应缓存)
│       │   ├── monitor_utils.py # 🆕 监控工具 (API调用监控)
│       │   └── cost_utils.py   # 🆕 成本计算工具
│       └── cli/                # 命令行界面
│           ├── __init__.py
│           ├── commands.py     # CLI命令定义
│           └── interface.py    # 交互界面
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── test_core/              # 核心模块测试
│   ├── test_api/               # 🆕 API模块测试
│   ├── test_utils/             # 工具模块测试
│   └── test_integration/       # 集成测试
├── scripts/                    # 脚本目录
│   ├── setup_api.py            # 🆕 API配置和测试脚本
│   ├── data_import.py          # 数据导入脚本
│   ├── benchmark.py            # 性能测试脚本
│   └── cost_analysis.py        # 🆕 成本分析脚本
└── .gitignore                  # Git忽略文件
```

## 📋 RAG实现要求

### 核心RAG流程实现

#### 1. 数据加载 (Load) - loader.py
```python
# 必须实现的功能 (不允许占位符)
class MarkdownLoader:
    def load_documents(self, data_path: str) -> List[Document]:
        """加载Markdown文件，提取命令行和代码片段"""
        # 真实实现：递归扫描目录，解析Markdown结构
        # 特殊处理：识别代码块、命令行、注释等不同类型
        pass
    
    def extract_commands(self, content: str) -> List[Dict]:
        """提取命令行相关内容"""
        # 真实实现：正则表达式识别命令模式
        pass
```

#### 2. 文本切分 (Chunk) - chunker.py
```python
class SmartChunker:
    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """智能切分文档，保持命令和说明的完整性"""
        # 真实实现：基于语义的切分策略
        # 特殊处理：命令行不拆分，代码块保持完整
        pass
```

#### 3. 向量嵌入 (Embed) - embedder.py
```python
class APIEmbedder:
    def __init__(self, api_manager: APIManager):
        """初始化API嵌入客户端"""
        # 真实实现：初始化多厂商API管理器
        self.api_manager = api_manager
        self.cache = EmbeddingCache()
    
    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """将文本转换为向量，支持缓存和重试"""
        # 真实实现：
        # 1. 检查缓存
        # 2. 批量API调用
        # 3. 重试机制
        # 4. 结果缓存
        pass
    
    def embed_single_text(self, text: str) -> List[float]:
        """单文本嵌入，支持API降级"""
        # 真实实现：单个文本的嵌入处理
        pass
```

#### 4. 数据存储 (Store) - storage.py
```python
class ChromaStorage:
    def __init__(self, db_path: str):
        """初始化ChromaDB连接"""
        # 真实实现：本地数据库初始化
        pass
    
    def store_embeddings(self, texts: List[str], embeddings: List[List[float]], metadata: List[Dict]):
        """存储文本和向量到数据库"""
        # 真实实现：批量插入，处理重复数据
        pass
```

#### 5. 信息检索 (Retrieve) - retriever.py
```python
class SemanticRetriever:
    def retrieve(self, query: str, top_k: int = 5) -> List[Document]:
        """基于语义相似度检索相关文档"""
        # 真实实现：向量相似度计算，结果排序
        pass
    
    def hybrid_search(self, query: str) -> List[Document]:
        """混合检索：语义检索 + 关键词检索"""
        # 真实实现：多种检索策略组合
        pass
```

#### 6. 答案生成 (Generate) - generator.py
```python
class APIGenerator:
    def __init__(self, api_manager: APIManager):
        """初始化API生成客户端"""
        # 真实实现：初始化多厂商API管理器
        self.api_manager = api_manager
        self.cache = ResponseCache()
    
    def generate_answer(self, query: str, context: List[Document]) -> str:
        """基于检索结果生成答案，支持流式输出"""
        # 真实实现：
        # 1. Prompt工程
        # 2. 上下文管理
        # 3. API调用重试
        # 4. 流式响应处理
        pass
    
    def generate_streaming(self, query: str, context: List[Document]) -> Iterator[str]:
        """流式生成答案"""
        # 真实实现：支持流式输出的生成
        pass
```

### 🆕 API对接模块实现 (高可用性核心)

#### 1. API客户端基类 - base_client.py
```python
class BaseAPIClient(ABC):
    """API客户端抽象基类，定义统一接口"""
    
    @abstractmethod
    def embed_text(self, text: str) -> List[float]:
        """文本嵌入接口"""
        pass
    
    @abstractmethod
    def generate_text(self, prompt: str, **kwargs) -> str:
        """文本生成接口"""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """健康检查接口"""
        pass
```

#### 2. OpenAI API客户端 - openai_client.py
```python
class OpenAIClient(BaseAPIClient):
    def __init__(self, api_key: str, base_url: str = None):
        """初始化OpenAI客户端"""
        # 真实实现：OpenAI SDK初始化
        # 支持自定义base_url (兼容硅基流动等)
        pass
    
    def embed_text(self, text: str) -> List[float]:
        """调用OpenAI嵌入API"""
        # 真实实现：text-embedding-3-small调用
        pass
    
    def generate_text(self, prompt: str, **kwargs) -> str:
        """调用OpenAI生成API"""
        # 真实实现：gpt-4o-mini调用
        pass
```

#### 3. 硅基流动API客户端 - siliconflow_client.py
```python
class SiliconFlowClient(BaseAPIClient):
    def __init__(self, api_key: str):
        """初始化硅基流动客户端"""
        # 真实实现：硅基流动API初始化
        # 使用OpenAI兼容接口
        self.base_url = "https://api.siliconflow.cn/v1"
        pass
    
    def embed_text(self, text: str) -> List[float]:
        """调用硅基流动嵌入API"""
        # 真实实现：使用OpenAI兼容接口
        pass
```

#### 4. API管理器 - api_manager.py
```python
class APIManager:
    def __init__(self, config: Dict):
        """初始化API管理器"""
        # 真实实现：
        # 1. 加载多个API客户端
        # 2. 设置优先级和权重
        # 3. 初始化重试机制
        # 4. 设置降级策略
        pass
    
    def call_embedding_api(self, text: str) -> List[float]:
        """智能调用嵌入API"""
        # 真实实现：
        # 1. 选择可用的API
        # 2. 执行调用
        # 3. 处理失败和重试
        # 4. 自动降级
        pass
    
    def call_generation_api(self, prompt: str, **kwargs) -> str:
        """智能调用生成API"""
        # 真实实现：负载均衡和故障转移
        pass
```

#### 5. 重试处理器 - retry_handler.py
```python
class RetryHandler:
    def __init__(self, max_retries: int = 3, backoff_factor: float = 2.0):
        """初始化重试处理器"""
        # 真实实现：指数退避重试策略
        pass
    
    def retry_with_backoff(self, func: Callable, *args, **kwargs):
        """带退避的重试机制"""
        # 真实实现：
        # 1. 指数退避算法
        # 2. 异常分类处理
        # 3. 重试次数控制
        # 4. 失败日志记录
        pass
```

#### 6. 限流控制器 - rate_limiter.py
```python
class RateLimiter:
    def __init__(self, requests_per_minute: int = 60):
        """初始化限流控制器"""
        # 真实实现：令牌桶算法
        pass
    
    def acquire(self) -> bool:
        """获取调用许可"""
        # 真实实现：限流逻辑
        pass
    
    def wait_if_needed(self):
        """等待直到可以调用"""
        # 真实实现：智能等待
        pass
```

#### 🆕 7. 离线降级处理器 - offline_handler.py
```python
class OfflineHandler:
    def __init__(self, cache_manager: CacheManager):
        """初始化离线处理器"""
        # 真实实现：离线模式管理
        self.cache_manager = cache_manager
        self.offline_mode = False
    
    def check_connectivity(self) -> bool:
        """检查网络连接状态"""
        # 真实实现：网络连接检测
        pass
    
    def enable_offline_mode(self):
        """启用离线模式"""
        # 真实实现：
        # 1. 切换到缓存优先模式
        # 2. 禁用API调用
        # 3. 启用本地搜索
        pass
    
    def offline_search(self, query: str) -> List[Document]:
        """离线搜索功能"""
        # 真实实现：
        # 1. 基于本地缓存的向量搜索
        # 2. 关键词匹配搜索
        # 3. 模糊匹配搜索
        pass
    
    def offline_response(self, query: str, context: List[Document]) -> str:
        """离线响应生成"""
        # 真实实现：
        # 1. 基于模板的响应生成
        # 2. 规则引擎响应
        # 3. 缓存响应复用
        pass
```

## 🔧 配置管理要求 (必须外部化)

### config.yaml 配置文件
```yaml
# API提供商配置
api_providers:
  primary: "siliconflow"  # 主要API提供商
  fallback: ["openai", "zhipu"]  # 降级顺序
  
# 数据库配置
database:
  type: "chromadb"
  path: "./data/vector_db"
  collection_name: "command_kb"

# 检索配置
retrieval:
  top_k: 5
  similarity_threshold: 0.7
  chunk_size: 500
  chunk_overlap: 50

# 生成配置
generation:
  max_tokens: 1000
  temperature: 0.1
  system_prompt: "你是一个专业的命令行助手..."

# 高可用性配置
high_availability:
  retry:
    max_attempts: 3
    backoff_factor: 2.0
    timeout: 30
  rate_limiting:
    requests_per_minute: 60
    burst_size: 10
  caching:
    enabled: true
    ttl: 3600  # 1小时
    max_size: 1000

# 成本控制配置
cost_control:
  daily_limit: 10.0  # 每日成本限制 (USD)
  warning_threshold: 0.8  # 警告阈值
  auto_fallback: true  # 成本超限时自动降级
```

### api_providers.yaml 配置文件
```yaml
# OpenAI配置
openai:
  embedding:
    model: "text-embedding-3-small"
    dimensions: 1536
    cost_per_1k_tokens: 0.00002
  generation:
    model: "gpt-4o-mini"
    cost_per_1k_tokens: 0.00015
  rate_limits:
    requests_per_minute: 500
    tokens_per_minute: 200000

# 硅基流动配置 (OpenAI兼容)
siliconflow:
  base_url: "https://api.siliconflow.cn/v1"
  embedding:
    model: "text-embedding-3-small"
    dimensions: 1536
    cost_per_1k_tokens: 0.000007  # 更便宜
  generation:
    model: "gpt-4o-mini"
    cost_per_1k_tokens: 0.000042  # 更便宜
  rate_limits:
    requests_per_minute: 200
    tokens_per_minute: 100000

# 智谱AI配置
zhipu:
  base_url: "https://open.bigmodel.cn/api/paas/v4"
  embedding:
    model: "embedding-2"
    dimensions: 1024
    cost_per_1k_tokens: 0.00001
  generation:
    model: "glm-4-flash"
    cost_per_1k_tokens: 0.00001
  rate_limits:
    requests_per_minute: 100
    tokens_per_minute: 50000
```

### .env.example 环境变量模板
```bash
# API密钥配置 (必须设置)
OPENAI_API_KEY=sk-your-openai-api-key-here
SILICONFLOW_API_KEY=sk-your-siliconflow-api-key-here
ZHIPU_API_KEY=your-zhipu-api-key-here
MOONSHOT_API_KEY=sk-your-moonshot-api-key-here

# 数据库配置
VECTOR_DB_PATH=./data/vector_db
DATA_SOURCE_PATH=./data/raw
CACHE_PATH=./data/cache

# API配置
DEFAULT_API_PROVIDER=siliconflow
API_TIMEOUT=30
MAX_RETRIES=3

# 成本控制
DAILY_COST_LIMIT=10.0
COST_WARNING_THRESHOLD=0.8

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/command_kb.log

# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600
MAX_CACHE_SIZE=1000

# 监控配置
ENABLE_MONITORING=true
METRICS_PORT=8080
```

## 🧪 测试要求

### 单元测试 (60%)
- 每个RAG模块的独立测试
- API客户端模块的测试
- 文本处理工具的测试
- 配置加载的测试

### 集成测试 (25%)
- 完整RAG流程的端到端测试
- 多厂商API集成测试
- 数据库操作测试
- 缓存系统测试

### 🆕 API专项测试 (10%)
- **API Mock测试**: 模拟各厂商API响应
- **故障注入测试**: 模拟API故障和网络中断
- **成本控制测试**: 验证成本限制和预警机制
- **压力测试**: API并发调用和限流测试
- **安全测试**: API密钥安全和数据脱敏测试

### 用户验收测试 (5%)
- 命令行界面测试
- 实际查询场景测试
- 离线模式测试
- 性能基准测试

## 📊 质量检查清单

### 🔥 核心要求检查 (必须100%通过)

#### 依赖版本兼容性检查 ✓
□ 已执行Python 3.11 LTS兼容性验证
□ 已验证LangChain与ChromaDB版本兼容性
□ 已确认OpenAI SDK稳定版本
□ 已测试多厂商API集成的稳定性
□ 选择了LTS/稳定版本而非最新版本

#### 配置化检查 ✓
□ 没有硬编码的API密钥
□ 没有硬编码的API端点
□ 没有硬编码的数据库路径
□ 所有API配置都有对应的配置文件
□ 提供了完整的环境变量模板
□ API密钥通过环境变量管理

#### 实现完整性检查 ✓
□ 没有TODO、FIXME等占位符
□ 没有返回假向量的函数
□ 所有RAG步骤都有真实实现
□ 所有API调用都有真实实现
□ 代码可以实际运行
□ 提供了可运行的示例

#### RAG模块组织检查 ✓
□ 每个RAG步骤独立为模块 (50行以内)
□ API对接模块独立且职责单一
□ 复杂逻辑已拆分为工具类
□ 通用功能已提取为utils模块
□ 配置管理在专门的config.py中
□ 模块职责单一，依赖关系清晰

### ⚠️ 重要要求检查

#### 资源管理检查 ✓
□ API连接使用连接池管理
□ 数据库连接在使用后正确关闭
□ 大文件使用流式处理
□ 实现了优雅关闭机制
□ API调用有超时控制

#### 性能优化检查 ✓
□ API调用使用批量处理
□ 实现了API响应缓存
□ 数据库查询使用索引优化
□ 长时间操作有进度提示
□ 实现了API限流控制

#### 错误处理检查 ✓
□ API调用失败有友好提示
□ API连接异常有重试机制
□ 文件读取错误有详细日志
□ 用户输入验证完整
□ 实现了API降级策略

### 🆕 API高可用性检查 (新增核心检查)

#### API集成检查 ✓
□ 实现了多厂商API适配器
□ API客户端遵循统一接口规范
□ 支持OpenAI标准接口兼容
□ API密钥管理安全合规
□ 实现了API健康检查机制

#### 高可用性机制检查 ✓
□ 实现了API重试机制 (指数退避)
□ 实现了API降级策略
□ 实现了负载均衡机制
□ API故障时自动切换备用服务
□ 实现了熔断器模式

#### 成本控制检查 ✓
□ 实现了API调用成本计算
□ 设置了每日成本限制
□ 实现了成本预警机制
□ 成本超限时自动降级
□ 提供了成本分析报告

#### 缓存策略检查 ✓
□ 实现了嵌入结果缓存
□ 实现了生成结果缓存
□ 缓存有合理的TTL设置
□ 缓存大小有限制控制
□ 实现了缓存失效机制

#### 监控告警检查 ✓
□ 实现了API调用成功率监控
□ 实现了API响应时间监控
□ 实现了成本使用监控
□ 实现了错误率告警
□ 提供了监控仪表板

### 🆕 运维监控详细要求 (新增关键要求)

#### 监控指标定义 ✓
□ **API性能指标**: 响应时间、成功率、错误率、QPS
□ **成本指标**: 每日成本、每次调用成本、成本趋势
□ **系统指标**: CPU使用率、内存使用率、磁盘使用率
□ **业务指标**: 查询次数、用户活跃度、缓存命中率

#### 告警规则设置 ✓
□ **API故障告警**: 成功率 < 95%、响应时间 > 5秒
□ **成本告警**: 每日成本超过80%限额、异常成本增长
□ **系统告警**: 内存使用率 > 80%、磁盘使用率 > 90%
□ **业务告警**: 缓存命中率 < 50%、查询失败率 > 5%

#### 日志管理规范 ✓
□ **日志分级**: DEBUG、INFO、WARN、ERROR、CRITICAL
□ **日志轮转**: 按大小和时间自动轮转
□ **日志聚合**: 集中收集和分析日志
□ **敏感信息过滤**: 自动过滤API密钥和敏感数据

#### 健康检查机制 ✓
□ **API健康检查**: 定期检查各厂商API可用性
□ **数据库健康检查**: 检查ChromaDB连接和性能
□ **缓存健康检查**: 检查缓存系统状态
□ **系统健康检查**: 检查系统资源使用情况

### 📋 RAG特定检查

#### 数据处理检查 ✓
□ Markdown解析正确处理代码块
□ 命令行提取准确识别格式
□ 文本切分保持语义完整性
□ 元数据提取包含必要信息

#### 向量化检查 ✓
□ API嵌入调用正确实现
□ 向量维度与数据库配置一致
□ 批量处理优化API调用
□ 向量质量通过相似度测试
□ 嵌入API调用有缓存机制

#### 检索优化检查 ✓
□ 相似度计算算法正确
□ 检索结果排序合理
□ 支持多种检索策略
□ 检索性能满足实时要求

#### 生成质量检查 ✓
□ Prompt工程针对命令行场景优化
□ 上下文窗口管理合理
□ 生成结果相关性高
□ 支持流式输出优化体验
□ 生成API调用有重试机制

## 🚀 BUG处理和经验总结规范

### 问题检索机制 (必须首先执行)
当遇到RAG相关问题时，必须先执行以下检索：
1. **API集成问题检索**: 搜索 "OpenAI API [错误信息] solution"、"硅基流动API [错误信息] troubleshooting"
2. **多厂商API问题检索**: 搜索 "multi-provider API integration [问题现象]"
3. **向量数据库问题检索**: 搜索 "ChromaDB [具体错误] troubleshooting"
4. **高可用性问题检索**: 搜索 "API retry fallback [问题现象]"
5. **成本控制问题检索**: 搜索 "API cost optimization [具体场景]"

### BUG解决方案标准化格式
```markdown
## RAG-[技术栈]-[错误类型]-[序号] {BUG标题}

**解决时间**: {日期}
**技术环境**: Python 3.11, LangChain x.x.x, ChromaDB x.x.x, API Provider: {厂商}
**严重程度**: {级别}
**标签**: #RAG #API集成 #高可用性 #成本控制 #多厂商

### 问题描述
**触发场景**: {具体的RAG操作场景}
**错误现象**: {详细的错误信息}
**影响范围**: {对RAG流程的影响}

### 解决方案
**根本原因**: {问题产生的根本原因}
**解决步骤**: 
1. {具体步骤1}
2. {具体步骤2}
3. {具体步骤3}

**验证方法**: {如何确认问题已解决}

### 预防措施
- **检查要点**: {开发时需要注意的关键点}
- **工具配置**: {相关的配置检查}
- **最佳实践**: {避免类似问题的建议}
```

## 📋 交付清单

请提供以下完整交付物：

### 1. 核心代码实现
□ 完整的RAG流程实现 (6个核心模块)
□ 多厂商API对接模块 (高可用性)
□ 配置管理系统
□ 命令行界面
□ 工具类和辅助函数

### 2. 配置和环境
□ config.yaml 配置文件
□ api_providers.yaml API厂商配置
□ .env.example 环境变量模板 (包含API密钥)
□ pyproject.toml 依赖配置
□ requirements.txt 备用依赖列表

### 3. 文档和指南
□ README.md 使用说明
□ API文档 (核心模块接口)
□ 部署指南 (模型下载、环境配置)
□ 故障排除指南

### 4. 测试和验证
□ 单元测试用例 (包含API模块测试)
□ 集成测试脚本 (API集成测试)
□ 性能基准测试 (API响应时间测试)
□ 高可用性测试 (故障注入测试)
□ 示例数据和查询

### 5. 脚本和工具
□ API配置和测试脚本
□ 数据导入脚本
□ 数据库初始化脚本
□ 性能监控脚本
□ 成本分析脚本

### 🆕 6. 用户体验优化 (新增要求)
□ **配置向导**: 首次使用的API配置引导
□ **进度显示**: 长时间操作的进度条和状态提示
□ **错误恢复**: 用户友好的错误信息和恢复建议
□ **离线提示**: 网络中断时的离线模式说明
□ **成本提醒**: 实时成本显示和预警提示

### 🆕 7. 安全和合规文档 (新增要求)
□ **API安全指南**: API密钥管理和安全使用说明
□ **数据处理规范**: 敏感数据处理和隐私保护指南
□ **合规检查清单**: 各厂商API使用条款合规检查
□ **审计日志说明**: 审计日志的格式和查看方法
□ **安全事件响应**: 安全事件的处理流程

## 🎯 成功标准

项目成功的标准是：
1. **功能完整**: 实现完整的RAG流程，支持命令行和代码片段检索
2. **高可用性**: API调用成功率 > 99%，支持多厂商降级，故障恢复时间 < 30秒
3. **安全合规**: API密钥安全管理，敏感数据保护，符合使用条款
4. **真实可用**: 代码真实可运行，无占位符或假实现
5. **性能良好**: 检索响应时间 < 2秒，生成响应时间 < 5秒，缓存命中率 > 70%
6. **成本可控**: 每日API成本 < 设定限制，有成本预警机制，成本优化有效
7. **离线可用**: 网络中断时支持基本查询功能，离线模式用户体验良好
8. **监控完善**: 完整的监控指标，及时的告警机制，详细的审计日志
9. **易于维护**: 代码结构清晰，配置外部化，文档完整，测试覆盖率 > 80%
10. **扩展性强**: 支持新API厂商添加，支持新数据源，支持功能扩展

---

*基于编程prompt生成平衡指南、时效性验证规范、思维引导技术和BUG解决方案整合规范，确保构建出高质量、可维护、真实可用的个人命令行向量知识库系统。*