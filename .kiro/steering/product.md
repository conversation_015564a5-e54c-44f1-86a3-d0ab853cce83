# Product Overview

## Personal Command-Line Vector Knowledge Base

A privacy-first, locally-deployed RAG (Retrieval-Augmented Generation) system for intelligent retrieval of personal command-line snippets and code fragments.

### Core Value Proposition
- **Privacy-First**: All data processing happens locally, with optional cloud API integration
- **Multi-Provider API Support**: Integrates with OpenAI, SiliconFlow, Zhipu, and other providers with automatic fallback
- **High Availability**: Built-in retry mechanisms, load balancing, and graceful degradation
- **Cost-Controlled**: Intelligent cost monitoring and automatic cost-based API switching
- **Offline Capable**: Functions even when network connectivity is limited

### Target Users
- Developers who maintain extensive command-line knowledge bases
- Engineers who need quick access to code snippets and configurations
- Technical professionals prioritizing data privacy and local deployment

### Key Features
- Semantic search across Markdown-based knowledge bases
- Intelligent chunking that preserves command context
- Multi-modal retrieval (semantic + keyword search)
- Real-time cost monitoring and budget controls
- Streaming response generation for better UX
- Comprehensive audit logging and monitoring

### Success Metrics
- API availability > 99%
- Query response time < 2 seconds
- Cost efficiency through intelligent provider switching
- High user satisfaction with search relevance