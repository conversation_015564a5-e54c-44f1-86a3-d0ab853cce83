# Project Structure & Organization

## Directory Layout

```
personal-command-kb/
├── pyproject.toml              # Project configuration and dependencies
├── README.md                   # Project documentation
├── .env.example                # Environment variable template
├── .gitignore                  # Git ignore patterns
├── config/                     # Configuration files
│   ├── config.yaml             # Main application config
│   ├── api_providers.yaml      # API provider configurations
│   └── logging.yaml            # Logging configuration
├── data/                       # Data storage directory
│   ├── raw/                    # Source Markdown files
│   ├── processed/              # Processed data cache
│   ├── vector_db/              # ChromaDB database files
│   └── cache/                  # API response cache
├── src/command_kb/             # Main source code
├── tests/                      # Test suite
├── scripts/                    # Utility scripts
└── logs/                       # Application logs
```

## Source Code Organization

### Core Module Structure (`src/command_kb/`)

#### Entry Points
- **`main.py`**: CLI application entry point
- **`__init__.py`**: Package initialization and version info
- **`constants.py`**: Application-wide constants (timeouts, limits, etc.)
- **`config.py`**: Configuration loading and validation

#### RAG Core Modules (`core/`)
Each RAG step is implemented as an independent module:
- **`loader.py`**: Document loading and parsing (Load step)
- **`chunker.py`**: Intelligent text chunking (Chunk step)  
- **`embedder.py`**: API-based text embedding (Embed step)
- **`storage.py`**: Vector database operations (Store step)
- **`retriever.py`**: Semantic and hybrid search (Retrieve step)
- **`generator.py`**: API-based answer generation (Generate step)

#### API Integration Layer (`api/`)
High-availability API management:
- **`base_client.py`**: Abstract API client interface
- **`openai_client.py`**: OpenAI API implementation
- **`siliconflow_client.py`**: SiliconFlow API implementation
- **`zhipu_client.py`**: Zhipu AI API implementation
- **`moonshot_client.py`**: Moonshot API implementation
- **`api_manager.py`**: Load balancing and failover logic
- **`retry_handler.py`**: Exponential backoff retry mechanism
- **`rate_limiter.py`**: API rate limiting and throttling
- **`offline_handler.py`**: Offline mode and degradation

#### Utilities (`utils/`)
- **`file_utils.py`**: File I/O and path operations
- **`text_utils.py`**: Text processing and cleaning
- **`cache_utils.py`**: Caching mechanisms for API responses
- **`monitor_utils.py`**: Performance and health monitoring
- **`cost_utils.py`**: Cost calculation and budget tracking

#### CLI Interface (`cli/`)
- **`commands.py`**: CLI command definitions and parsing
- **`interface.py`**: Interactive user interface components

## Architectural Patterns

### Module Dependencies
```
CLI Layer (cli/) 
    ↓
Core RAG Pipeline (core/)
    ↓
API Integration (api/) ← → Utils (utils/)
    ↓
External APIs & ChromaDB
```

### Design Principles

#### Single Responsibility
- Each module handles one specific aspect of the RAG pipeline
- API clients are isolated and interchangeable
- Utilities are stateless and reusable

#### Dependency Injection
- API clients injected into core modules
- Configuration passed through constructors
- Easy testing with mock implementations

#### Error Boundaries
- Each RAG step handles its own errors gracefully
- API failures don't cascade through the system
- Comprehensive logging at module boundaries

## Configuration Architecture

### Configuration Hierarchy
1. **Environment Variables** (highest priority)
2. **config.yaml** (application defaults)
3. **api_providers.yaml** (provider-specific settings)
4. **Constants** (hardcoded fallbacks)

### Configuration Sections
- **`api_providers`**: Multi-provider API configuration
- **`database`**: ChromaDB settings and paths
- **`retrieval`**: Search and ranking parameters
- **`generation`**: LLM generation settings
- **`high_availability`**: Retry, fallback, and monitoring
- **`cost_control`**: Budget limits and cost optimization

## Data Flow Architecture

### RAG Pipeline Flow
```
Markdown Files → Loader → Chunker → Embedder → Storage
                                                   ↓
CLI Query → Retriever ← Storage    Generator ← API Manager
              ↓                      ↓
           Context → Generator → Formatted Response
```

### API Management Flow
```
Core Module Request → API Manager → Provider Selection
                                        ↓
                    Rate Limiter → Retry Handler → API Client
                                        ↓
                    Response Cache ← API Response
                                        ↓
                    Cost Tracker → Core Module Response
```

## Testing Structure

### Test Organization (`tests/`)
- **`test_core/`**: Unit tests for RAG pipeline modules
- **`test_api/`**: API integration and mock tests
- **`test_utils/`**: Utility function tests
- **`test_integration/`**: End-to-end pipeline tests
- **`test_cli/`**: Command-line interface tests

### Test Categories
- **Unit Tests**: Individual module functionality
- **Integration Tests**: Multi-module interactions
- **API Tests**: External service integration
- **Performance Tests**: Response time and throughput
- **Resilience Tests**: Failure scenarios and recovery

## File Naming Conventions

### Python Modules
- **Snake case**: `api_manager.py`, `cost_utils.py`
- **Descriptive names**: Clearly indicate module purpose
- **Consistent suffixes**: `_client.py` for API clients, `_utils.py` for utilities

### Configuration Files
- **Lowercase with extensions**: `config.yaml`, `logging.yaml`
- **Descriptive names**: `api_providers.yaml` vs generic `settings.yaml`

### Data Directories
- **Lowercase**: `vector_db/`, `cache/`
- **Purpose-driven**: `raw/` vs `processed/` for data stages

## Import Conventions

### Internal Imports
```python
# Absolute imports from project root
from command_kb.core.embedder import APIEmbedder
from command_kb.api.api_manager import APIManager
from command_kb.utils.cache_utils import ResponseCache
```

### External Dependencies
```python
# Standard library first
import os
import logging
from typing import List, Dict, Optional

# Third-party packages
import yaml
import chromadb
from langchain.schema import Document

# Local imports last
from command_kb.config import load_config
```

This structure ensures maintainability, testability, and clear separation of concerns while supporting the high-availability and multi-provider requirements of the RAG system.