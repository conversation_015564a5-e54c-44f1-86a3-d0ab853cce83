# Technology Stack & Build System

## Core Technology Stack

### Runtime Environment
- **Python**: 3.11 LTS (prioritize stability over latest features)
- **Package Manager**: pip with pyproject.toml configuration
- **Virtual Environment**: venv or conda for isolation

### AI/ML Framework Stack
- **RAG Orchestration**: LangChain 0.1.x (stable branch, avoid 0.2.x beta)
- **Vector Database**: ChromaDB 0.4.x (local, lightweight)
- **Text Processing**: Sentence Transformers for embeddings
- **API Integration**: OpenAI Python SDK 1.x (supports multiple providers)

### Multi-Provider API Integration
- **Primary Providers**: OpenAI, SiliconFlow, Zhipu, Moonshot
- **API Standard**: OpenAI-compatible interfaces where possible
- **Authentication**: Environment variable-based API key management
- **High Availability**: Built-in retry, fallback, and load balancing

### Data & Storage
- **Vector Storage**: ChromaDB (local file-based)
- **Configuration**: YAML-based config files
- **Caching**: In-memory and file-based caching for API responses
- **Data Format**: Markdown source files with structured metadata

## Development Standards

### Code Quality Requirements
- **Style Guide**: Strict PEP 8 compliance
- **Function Length**: Maximum 50 lines per function
- **Module Organization**: Single responsibility principle
- **Type Hints**: Required for all public interfaces
- **Documentation**: Docstrings for all classes and public methods

### Configuration Management
- **No Hard-coding**: All configurable values externalized
- **Environment Variables**: API keys, paths, and sensitive data
- **Config Files**: YAML for structured configuration
- **Constants**: Centralized in constants.py module

### Error Handling & Resilience
- **API Failures**: Exponential backoff retry with circuit breaker
- **Graceful Degradation**: Offline mode when APIs unavailable
- **Comprehensive Logging**: Structured logging with appropriate levels
- **Cost Controls**: Automatic budget limits and provider switching

## Common Commands

### Development Setup
```bash
# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate  # macOS/Linux

# Install dependencies
pip install -e .

# Install development dependencies
pip install -e ".[dev]"
```

### Testing
```bash
# Run unit tests
pytest tests/

# Run with coverage
pytest --cov=src/command_kb tests/

# Run integration tests
pytest tests/test_integration/ -v

# Run API-specific tests
pytest tests/test_api/ -v
```

### Quality Checks
```bash
# Code formatting
black src/ tests/
isort src/ tests/

# Linting
flake8 src/ tests/
mypy src/

# Security scan
bandit -r src/
```

### Application Usage
```bash
# Initialize database
python -m command_kb.scripts.setup_api

# Import data
python -m command_kb.scripts.data_import --source ./data/raw

# Run CLI
python -m command_kb.main query "docker commands"

# Monitor costs
python -m command_kb.scripts.cost_analysis
```

### Configuration Management
```bash
# Copy environment template
cp .env.example .env

# Edit API keys (required)
nano .env

# Validate configuration
python -m command_kb.config --validate

# Test API connectivity
python -m command_kb.scripts.setup_api --test-all
```

## Performance & Monitoring

### Key Performance Targets
- **API Response Time**: < 2 seconds average
- **Search Latency**: < 500ms for cached queries
- **API Success Rate**: > 99% with fallback
- **Memory Usage**: < 512MB for typical workloads

### Monitoring Commands
```bash
# Check API health
python -m command_kb.api.health_check

# View usage statistics
python -m command_kb.scripts.usage_stats

# Monitor costs in real-time
python -m command_kb.scripts.cost_monitor --live
```