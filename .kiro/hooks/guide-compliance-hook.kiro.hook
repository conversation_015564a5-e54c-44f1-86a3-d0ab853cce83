{"enabled": true, "name": "Development Guide Compliance", "description": "Monitors changes to ensure development follows the personal command-line vector knowledge base development guide and handles compatibility issues flexibly", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.py", "pyproject.toml", "config/*.yaml", "tests/**/*.py", "real_prompt.md"]}, "then": {"type": "askAgent", "prompt": "Review the file changes and ensure they strictly follow the guidelines in \"个人命令行向量知识库开发指南.md\". Check for:\n\n1. Compliance with the development guide specifications\n2. Proper adherence to the RAG pipeline architecture\n3. Correct API integration patterns\n4. Appropriate error handling and resilience measures\n5. Plugin/dependency compatibility issues\n\nIf you encounter incompatible plugins or dependency versions, provide flexible solutions that maintain functionality while following the guide. Suggest alternative approaches, version adjustments, or workarounds that align with the guide's principles.\n\nFocus on maintaining the integrity of the personal command-line vector knowledge base system while being adaptable to compatibility challenges."}}