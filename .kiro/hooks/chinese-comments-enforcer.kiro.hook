{"name": "Chinese Comments Enforcer", "description": "监控所有Python文件的注释，将任何英文注释转换为中文，并确保后续开发中只允许使用中文注释。", "trigger": {"event": "file_saved", "filePatterns": ["**/*.py", "**/*.md", "**/*.yaml", "**/*.yml", "**/*.sh"]}, "action": {"type": "agent_request", "request": "检查文件中的所有注释，将任何英文注释转换为中文注释，确保所有新增的注释都使用中文。对于代码注释，请保持技术准确性的同时使用清晰的中文表达。请检查以下类型的注释：\n1. Python文件中的 # 单行注释和 \"\"\"多行注释\"\"\"\n2. Markdown文件中的注释\n3. YAML文件中的注释\n4. Shell脚本中的注释\n\n请将所有英文注释改为对应的中文注释，并确保后续开发中只允许使用中文注释。"}, "enabled": true}