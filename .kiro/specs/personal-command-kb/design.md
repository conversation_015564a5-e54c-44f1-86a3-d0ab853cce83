# Design Document

## Overview

The Personal Command-Line Vector Knowledge Base is a privacy-first RAG (Retrieval-Augmented Generation) system designed to provide intelligent retrieval of command-line snippets and code fragments from local Markdown files. The system emphasizes local data processing while offering optional cloud API integration with multiple providers, high availability features, and comprehensive cost controls.

### Key Design Principles

- **Privacy-First Architecture**: All document processing and vector storage occurs locally
- **High Availability**: Multi-provider API integration with automatic failover and circuit breaker patterns
- **Cost Optimization**: Real-time cost monitoring with intelligent provider switching
- **Modular Design**: Clean separation of concerns with pluggable components
- **Offline Capability**: Graceful degradation when network connectivity is limited

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "CLI Interface"
        CLI[Command Line Interface]
        REPL[Interactive REPL]
    end
    
    subgraph "Core RAG Pipeline"
        LOAD[Document Loader]
        CHUNK[Intelligent Chunker]
        EMBED[Embedder]
        STORE[Vector Storage]
        RETRIEVE[Retriever]
        GENERATE[Generator]
    end
    
    subgraph "API Management Layer"
        MGR[API Manager]
        RETRY[<PERSON>try Handler]
        RATE[Rate Limiter]
        CIRCUIT[Circuit Breaker]
    end
    
    subgraph "External APIs"
        OPENAI[OpenAI API]
        SILICON[SiliconFlow API]
        ZHIPU[Zhipu AI API]
        MOONSHOT[Moonshot API]
    end
    
    subgraph "Local Storage"
        CHROMA[(ChromaDB)]
        CACHE[(Response Cache)]
        FILES[(Markdown Files)]
    end
    
    CLI --> LOAD
    LOAD --> CHUNK
    CHUNK --> EMBED
    EMBED --> STORE
    STORE --> CHROMA
    
    CLI --> RETRIEVE
    RETRIEVE --> CHROMA
    RETRIEVE --> GENERATE
    GENERATE --> MGR
    MGR --> RETRY
    MGR --> RATE
    MGR --> CIRCUIT
    
    RETRY --> OPENAI
    RETRY --> SILICON
    RETRY --> ZHIPU
    RETRY --> MOONSHOT
    
    LOAD --> FILES
    MGR --> CACHE
```

### Data Flow Architecture

The system follows a traditional RAG pipeline with enhanced reliability and cost optimization:

1. **Document Processing Flow**: Markdown files → Loader → Chunker → Embedder → ChromaDB
2. **Query Processing Flow**: User Query → Retriever → Context Assembly → Generator → API Manager → Response
3. **API Management Flow**: Request → Rate Limiter → Circuit Breaker → Provider Selection → Retry Handler → External API

## Components and Interfaces

### Core RAG Pipeline Components

#### Document Loader (`core/loader.py`)
**Purpose**: Load and parse Markdown documents with metadata extraction

**Key Interfaces**:
```python
class DocumentLoader:
    def load_documents(self, source_path: str) -> List[Document]
    def load_single_document(self, file_path: str) -> Document
    def extract_metadata(self, content: str, file_path: str) -> Dict[str, Any]
    def detect_file_changes(self, source_path: str) -> List[str]
```

**Implementation Details**:
- Support for recursive directory scanning
- Markdown metadata extraction from frontmatter
- File modification detection for incremental updates
- Structured content parsing (headers, code blocks, commands)

#### Intelligent Chunker (`core/chunker.py`)
**Purpose**: Split documents into semantically meaningful chunks while preserving command context

**Key Interfaces**:
```python
class IntelligentChunker:
    def chunk_documents(self, documents: List[Document]) -> List[DocumentChunk]
    def preserve_command_context(self, content: str) -> List[str]
    def optimize_chunk_size(self, content: str, target_size: int) -> List[str]
```

**Implementation Details**:
- Command-aware chunking that keeps related commands together
- Adaptive chunk sizing based on content type
- Overlap management for context preservation
- Special handling for code blocks and command sequences

#### Embedder (`core/embedder.py`)
**Purpose**: Generate vector embeddings using local Sentence Transformers

**Key Interfaces**:
```python
class APIEmbedder:
    def embed_documents(self, chunks: List[DocumentChunk]) -> List[Embedding]
    def embed_query(self, query: str) -> Embedding
    def get_embedding_dimension(self) -> int
```

**Implementation Details**:
- Local Sentence Transformers model for privacy
- Batch processing for efficiency
- Caching of embeddings to avoid recomputation
- Support for different embedding models

#### Vector Storage (`core/storage.py`)
**Purpose**: Manage ChromaDB operations for vector storage and retrieval

**Key Interfaces**:
```python
class VectorStorage:
    def store_embeddings(self, chunks: List[DocumentChunk], embeddings: List[Embedding])
    def search_similar(self, query_embedding: Embedding, k: int) -> List[SearchResult]
    def hybrid_search(self, query: str, query_embedding: Embedding, k: int) -> List[SearchResult]
    def update_document(self, document_id: str, chunks: List[DocumentChunk])
```

**Implementation Details**:
- ChromaDB integration with persistent storage
- Hybrid search combining semantic and keyword matching
- Metadata filtering capabilities
- Efficient batch operations

#### Retriever (`core/retriever.py`)
**Purpose**: Orchestrate search and ranking of relevant content

**Key Interfaces**:
```python
class HybridRetriever:
    def retrieve(self, query: str, k: int) -> List[RetrievalResult]
    def rerank_results(self, results: List[SearchResult], query: str) -> List[RetrievalResult]
    def filter_by_metadata(self, results: List[SearchResult], filters: Dict) -> List[SearchResult]
```

**Implementation Details**:
- Multi-stage retrieval with semantic and keyword search
- Result reranking based on relevance scores
- Context window optimization
- Source attribution and metadata preservation

#### Generator (`core/generator.py`)
**Purpose**: Generate responses using the API management layer

**Key Interfaces**:
```python
class ResponseGenerator:
    def generate_response(self, query: str, context: List[RetrievalResult]) -> GeneratedResponse
    def stream_response(self, query: str, context: List[RetrievalResult]) -> Iterator[str]
    def format_response(self, raw_response: str, sources: List[str]) -> FormattedResponse
```

**Implementation Details**:
- Integration with API Manager for provider abstraction
- Streaming response support for better UX
- Context optimization and prompt engineering
- Response formatting with source citations

### API Integration Layer

#### API Manager (`api/api_manager.py`)
**Purpose**: Orchestrate multi-provider API access with high availability

**Key Interfaces**:
```python
class APIManager:
    def generate_completion(self, prompt: str, **kwargs) -> APIResponse
    def generate_embedding(self, text: str) -> List[float]
    def get_available_providers(self) -> List[str]
    def switch_provider(self, provider_name: str) -> bool
```

**Implementation Details**:
- Provider abstraction with unified interface
- Load balancing across multiple providers
- Automatic failover on provider failures
- Cost tracking and budget enforcement

#### Provider Clients (`api/*_client.py`)
**Purpose**: Individual API client implementations

**Key Interfaces**:
```python
class BaseAPIClient:
    def generate_completion(self, prompt: str, **kwargs) -> APIResponse
    def generate_embedding(self, text: str) -> List[float]
    def check_health(self) -> bool
    def get_cost_per_token(self) -> float
```

**Implementation Details**:
- OpenAI, SiliconFlow, Zhipu, and Moonshot implementations
- Standardized error handling and response formats
- Health check endpoints for availability monitoring
- Cost calculation per provider

#### Retry Handler (`api/retry_handler.py`)
**Purpose**: Implement exponential backoff and circuit breaker patterns

**Key Interfaces**:
```python
class RetryHandler:
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any
    def should_retry(self, exception: Exception) -> bool
    def calculate_backoff(self, attempt: int) -> float
```

**Implementation Details**:
- Exponential backoff with jitter
- Circuit breaker pattern for failing services
- Retry policy configuration per provider
- Comprehensive error classification

#### Rate Limiter (`api/rate_limiter.py`)
**Purpose**: Enforce API rate limits and prevent quota exhaustion

**Key Interfaces**:
```python
class RateLimiter:
    def acquire_permit(self, provider: str) -> bool
    def update_limits(self, provider: str, limits: Dict[str, int])
    def get_remaining_quota(self, provider: str) -> Dict[str, int]
```

**Implementation Details**:
- Token bucket algorithm for rate limiting
- Per-provider quota management
- Dynamic limit adjustment based on API responses
- Quota monitoring and alerting

### Utility Components

#### Configuration Management (`config.py`)
**Purpose**: Hierarchical configuration loading and validation

**Key Features**:
- Environment variable override support
- YAML configuration file parsing
- Configuration validation and type checking
- Hot-reload capability for development

#### Caching System (`utils/cache_utils.py`)
**Purpose**: Multi-level caching for API responses and embeddings

**Key Features**:
- In-memory LRU cache for frequent queries
- Persistent disk cache for embeddings
- Cache invalidation strategies
- Configurable TTL and size limits

#### Monitoring and Logging (`utils/monitor_utils.py`)
**Purpose**: Comprehensive observability and performance tracking

**Key Features**:
- Structured logging with configurable levels
- Performance metrics collection
- API usage and cost tracking
- Health check endpoints

## Data Models

### Core Data Structures

```python
@dataclass
class Document:
    id: str
    content: str
    metadata: Dict[str, Any]
    file_path: str
    last_modified: datetime

@dataclass
class DocumentChunk:
    id: str
    document_id: str
    content: str
    metadata: Dict[str, Any]
    chunk_index: int
    start_char: int
    end_char: int

@dataclass
class SearchResult:
    chunk_id: str
    content: str
    score: float
    metadata: Dict[str, Any]
    source_file: str

@dataclass
class APIResponse:
    content: str
    provider: str
    model: str
    tokens_used: int
    cost: float
    response_time: float
```

### Configuration Schema

```yaml
# config/config.yaml
api_providers:
  openai:
    api_key: ${OPENAI_API_KEY}
    model: "gpt-3.5-turbo"
    max_tokens: 2000
    temperature: 0.7
  siliconflow:
    api_key: ${SILICONFLOW_API_KEY}
    model: "deepseek-chat"
    endpoint: "https://api.siliconflow.cn/v1"

database:
  path: "./data/vector_db"
  collection_name: "command_kb"
  embedding_dimension: 384

retrieval:
  chunk_size: 1000
  chunk_overlap: 200
  max_results: 10
  rerank_threshold: 0.7

cost_control:
  daily_budget: 10.0
  cost_alert_threshold: 0.8
  auto_switch_providers: true
```

## Error Handling

### Error Classification and Recovery

#### API Errors
- **Rate Limit Exceeded**: Automatic backoff and provider switching
- **Authentication Failures**: Alert user and disable provider
- **Service Unavailable**: Circuit breaker activation and fallback
- **Quota Exhausted**: Provider switching based on budget

#### Data Errors
- **File Not Found**: Graceful skip with logging
- **Parsing Errors**: Content sanitization and retry
- **Embedding Failures**: Fallback to cached embeddings
- **Database Errors**: Automatic retry with exponential backoff

#### System Errors
- **Memory Exhaustion**: Batch size reduction and garbage collection
- **Disk Space**: Cache cleanup and user notification
- **Network Connectivity**: Offline mode activation
- **Configuration Errors**: Validation with helpful error messages

### Graceful Degradation Strategies

1. **API Unavailability**: Switch to cached responses and offline search
2. **Embedding Service Down**: Use pre-computed embeddings only
3. **Database Issues**: Fall back to file-based search
4. **Memory Constraints**: Reduce batch sizes and enable streaming

## Testing Strategy

### Unit Testing
- **Component Isolation**: Mock external dependencies
- **Edge Case Coverage**: Error conditions and boundary values
- **Performance Testing**: Memory usage and response times
- **Configuration Testing**: Various config combinations

### Integration Testing
- **API Integration**: Real API calls with test accounts
- **Database Operations**: ChromaDB CRUD operations
- **End-to-End Flows**: Complete RAG pipeline testing
- **Multi-Provider Scenarios**: Failover and switching logic

### Performance Testing
- **Load Testing**: High query volumes and concurrent users
- **Memory Profiling**: Memory usage patterns and leaks
- **Response Time**: Latency under various conditions
- **Scalability**: Performance with large knowledge bases

### Resilience Testing
- **Chaos Engineering**: Random API failures and network issues
- **Resource Exhaustion**: Memory and disk space limits
- **Provider Outages**: Extended service unavailability
- **Configuration Changes**: Hot-reload and validation

## Security Considerations

### Data Privacy
- **Local Processing**: All document content stays on local machine
- **API Key Management**: Secure storage and rotation
- **Query Logging**: Configurable logging levels for sensitive data
- **Cache Security**: Encrypted cache storage for sensitive content

### Network Security
- **TLS Encryption**: All API communications over HTTPS
- **Certificate Validation**: Proper SSL certificate verification
- **Request Signing**: API request authentication and integrity
- **Rate Limiting**: Protection against abuse and DoS

### Access Control
- **File Permissions**: Proper file system permissions for data directories
- **Configuration Security**: Secure storage of sensitive configuration
- **Audit Logging**: Comprehensive access and operation logging
- **User Authentication**: Optional authentication for multi-user scenarios

This design provides a robust, scalable, and privacy-focused foundation for the personal command-line vector knowledge base, with comprehensive error handling, monitoring, and security considerations built into every component.