{"version": "0.2.0", "configurations": [{"name": "Debug Interactive Mode", "type": "debugpy", "request": "launch", "module": "command_kb.cli.main", "args": ["--interactive", "--verbose"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/personal-command-kb", "env": {"PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"}, "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Single Query", "type": "debugpy", "request": "launch", "module": "command_kb.cli.commands", "args": ["query", "docker logs", "--verbose"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/personal-command-kb", "env": {"PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"}, "justMyCode": false}, {"name": "Debug Data Import", "type": "debugpy", "request": "launch", "module": "command_kb.cli.commands", "args": ["import-data", "./data/raw/samples"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/personal-command-kb", "env": {"PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"}, "justMyCode": false}, {"name": "Debug Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}/personal-command-kb", "env": {"PYTHONPATH": "${workspaceFolder}/personal-command-kb/src"}, "justMyCode": true}]}