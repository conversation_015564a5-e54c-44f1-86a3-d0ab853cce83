# MLH Pro - AI工程师知识库项目

> 专业的AI工程师技术知识库，涵盖LangChain、RAG系统、流式处理等核心技术

## 📁 项目结构

```
mlh_pro/
├── README.md                                    # 项目总览（本文件）
├── real_prompt.md                              # 项目核心提示词
├── docs/                                       # 📚 分类文档目录
│   ├── README.md                               # 文档目录说明
│   ├── langchain技术/                          # LangChain相关技术文档
│   ├── rag系统/                                # RAG系统相关文档
│   ├── 系统架构/                               # 系统架构相关文档
│   ├── 文档处理/                               # 文档处理和分割相关
│   ├── 搜索检索/                               # 搜索和检索算法相关
│   ├── 开发指南/                               # 开发相关文档和指南
│   ├── 面试题集/                               # 面试相关文档
│   └── 学习教程/                               # 教程和实践指南
└── personal-command-kb/                        # 个人命令知识库
```

## 🎯 项目特色

### 核心技术文档

#### LangChain技术 (`langchain技术/`)
- **LangChain流式处理完全指南**: 深入解析流式处理原理、实现和最佳实践
- **LangChain使用模式判断指南**: 不同场景下的LangChain使用策略
- **LangChain版本升级分析与建议**: 版本迁移和功能对比
- **LangChain向量数据库集成详解**: 向量数据库选择和集成方案
- **LangChain语义分割实现指南**: 语义级别的文档分割
- **LangChain_LangServe_SSE完整架构指南**: LangServe和SSE架构实现
- **直接使用OpenAI库vs LangChain对比分析**: 技术选型对比分析

#### RAG系统 (`rag系统/`)
- **RAG系统数据流与向量化详解**: RAG系统核心流程分析
- **RAG答案生成流程深度解析**: 答案生成机制详解
- **企业级RAG查询路由策略完全指南**: 查询路由和意图识别

#### 系统架构 (`系统架构/`)
- **APIManager多供应商代理机制详解**: 多LLM供应商管理
- **APIManager技术知识点分析**: API管理器技术要点分析
- **CLI模块架构与启动流程详解**: CLI模块的架构设计
- **流式处理框架对比与实现指南**: 不同流式处理方案对比
- **意图识别系统技术实现指南**: 用户意图识别技术
- **当前项目技术实现现状分析**: 项目技术现状和改进建议

#### 文档处理 (`文档处理/`)
- **Chunker分片策略完全解析**: 文档分割策略和实现
- **PDF和Excel文档智能分割方案**: 特定格式文档处理
- **LLM模型规模与文档分割效果分析**: 模型规模对分割效果的影响

#### 搜索检索 (`搜索检索/`)
- **混合搜索结果合并算法详解**: 多种搜索算法的结合
- **检索系统中的重排序与BM25详解**: 检索结果优化
- **关键词搜索算法深度解析**: 关键词搜索实现

#### 开发指南 (`开发指南/`)
- **Python项目VSCode调试完全指南**: 开发环境配置和调试技巧
- **Prompt管理核心概念通俗解释**: Prompt工程和管理的核心概念
- **personal-command-kb-execution-flow**: 个人命令知识库的执行流程

#### 面试题集 (`面试题集/`)
- **二线城市AI工程师LangChain面试题集**: 40道精选面试题，涵盖初中高级

#### 学习教程 (`学习教程/`)
- **Java工程师转AI工程师完整指南**: 转型路径和技能要求

## 🚀 快速开始

### 1. 学习路径建议

**初学者路径**：
1. 阅读 `docs/学习教程/Java工程师转AI工程师完整指南.md`
2. 学习 `docs/面试题集/二线城市AI工程师LangChain面试题集.md`
3. 实践 `docs/langchain技术/LangChain使用模式判断指南.md`

**进阶开发者路径**：
1. 深入 `docs/langchain技术/LangChain流式处理完全指南.md`
2. 研究 `docs/rag系统/RAG系统数据流与向量化详解.md`
3. 实现 `docs/rag系统/企业级RAG查询路由策略完全指南.md`

**架构师路径**：
1. 分析 `docs/系统架构/APIManager多供应商代理机制详解.md`
2. 设计 `docs/搜索检索/混合搜索结果合并算法详解.md`
3. 优化 `docs/系统架构/流式处理框架对比与实现指南.md`

### 2. 项目特点

- **实战导向**: 所有文档都基于实际项目经验
- **代码完整**: 提供可运行的完整代码示例
- **深度分析**: 不仅讲how，更讲why
- **持续更新**: 跟踪最新技术发展

### 3. 使用建议

- **按需学习**: 根据当前项目需求选择相关文档
- **实践验证**: 建议结合实际项目验证文档中的方案
- **版本注意**: 注意LangChain等框架的版本兼容性

## 📖 重点推荐文档

### 🔥 必读文档
1. **LangChain流式处理完全指南** - 解决流式处理中的常见问题
2. **RAG系统数据流与向量化详解** - 理解RAG系统核心原理
3. **二线城市AI工程师LangChain面试题集** - 面试必备

### 💡 实用工具
1. **系统架构/APIManager多供应商代理机制详解** - 多LLM管理方案
2. **文档处理/Chunker分片策略完全解析** - 文档分割最佳实践
3. **开发指南/Python项目VSCode调试完全指南** - 开发环境优化

### 🎯 高级主题
1. **rag系统/企业级RAG查询路由策略完全指南** - 企业级系统设计
2. **搜索检索/混合搜索结果合并算法详解** - 搜索算法优化
3. **系统架构/意图识别系统技术实现指南** - 智能交互设计

## 🤝 贡献指南

欢迎贡献内容！请遵循以下规范：

1. **文档格式**: 使用Markdown格式
2. **代码示例**: 提供完整可运行的代码
3. **实践验证**: 确保方案在实际项目中可行
4. **版本说明**: 标注相关框架的版本要求

## 📝 更新日志

- **2024-01-XX**: 创建项目结构，整理核心文档
- **2024-01-XX**: 添加LangChain流式处理完全指南
- **2024-01-XX**: 完善面试题集和学习路径

## 📞 联系方式

如有问题或建议，欢迎通过以下方式联系：
- 项目Issues
- 技术讨论
- 文档改进建议

---

*持续更新中，致力于成为最实用的AI工程师技术知识库！*