# 📚 Personal Command-Line Vector Knowledge Base 使用指南

## 🎯 项目简介

这是一个基于RAG（检索增强生成）技术的个人命令行知识库系统，支持：
- 🔍 智能语义搜索你的命令文档
- 🤖 AI驱动的问答系统
- 📝 Markdown文档自动处理
- 🚀 多API提供商支持（SiliconFlow、OpenAI等）
- 💰 成本控制和性能监控

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保你有Python 3.11+
python --version

# 进入项目目录
cd personal-command-kb

# 创建虚拟环境（使用UV，更快更好）
uv venv
source .venv/bin/activate  # macOS/Linux

# 安装依赖
uv pip install -e .
```

### 2. 配置API密钥

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑.env文件，添加你的API密钥
nano .env
```

**重要配置项：**
```bash
# SiliconFlow API密钥（推荐，性价比高）
SILICONFLOW_API_KEY=sk-your-api-key-here

# 模型配置（已优化）
EMBEDDING_MODEL=Qwen/Qwen3-Embedding-8B
GENERATION_MODEL=Qwen/Qwen2.5-72B-Instruct

# 性能优化配置
API_TIMEOUT=10
MAX_RETRIES=2
ENABLE_CACHE=true
```

### 3. 启动项目

```bash
# 交互式启动（推荐）
python -m command_kb.cli.main --interactive

# 或者使用UV运行
uv run python -m command_kb.cli.main --interactive
```

## 📖 基本使用

### 导入文档

```bash
# 在交互模式中
kb> import ./data/raw/samples

# 或者导入你自己的文档目录
kb> import ./your-docs-folder
```

### 查询命令

```bash
# 基本查询
kb> query docker logs

# 复杂查询
kb> query "how to list files in linux with details"

# Git相关查询
kb> query "git branch operations"
```

### 系统状态

```bash
# 查看系统状态
kb> status

# 详细状态
kb> status --detailed
```

## 📁 文档组织

### 推荐的目录结构

```
personal-command-kb/
├── data/
│   └── raw/
│       ├── docker/
│       │   ├── docker-commands.md
│       │   └── docker-compose.md
│       ├── git/
│       │   ├── git-basics.md
│       │   └── git-advanced.md
│       ├── linux/
│       │   ├── file-operations.md
│       │   └── system-admin.md
│       └── python/
│           ├── python-scripts.md
│           └── python-packages.md
```

### Markdown文档格式

```markdown
# 命令分类标题

## 子分类

### 具体命令

```bash
# 命令示例
docker ps -a
docker logs container_name
```

**说明：**
- 参数解释
- 使用场景
- 注意事项
```

## ⚡ 性能优化 - 解决7.77秒响应慢问题

### 🚨 快速解决方案（推荐）

如果你的查询响应时间很慢（7.77秒），使用一键优化脚本：

```bash
# 一键性能优化
chmod +x optimize_performance.sh
./optimize_performance.sh

# 选择优化模式：
# 1) 平衡模式 (推荐) - 2-3秒响应
# 2) 极速模式 - 1-1.5秒响应  
# 3) 自定义优化
```

### 📊 性能问题分析

**7.77秒响应时间分解：**
- 🐌 **主要瓶颈 (80-90%)**：Qwen2.5-72B大模型处理时间 (5-7秒)
- 🔍 **次要瓶颈 (10-15%)**：向量检索和8192维嵌入计算 (0.5-1秒)
- 🌐 **网络延迟 (5%)**：API调用延迟 (0.2-0.5秒)

### 🎯 优化效果对比

| 配置方案 | 响应时间 | 质量 | 成本 | 推荐度 |
|---------|---------|------|------|--------|
| 当前(72B) | 7.77秒 | 最高 | 最高 | ❌ |
| 平衡(7B) | 2-3秒 | 高 | 中等 | ✅ 推荐 |
| 极速(1.5B) | 1-1.5秒 | 中等 | 最低 | ⚡ 特殊场景 |

### 🛠️ 手动优化配置

如果不使用脚本，可以手动编辑`.env`文件：

```bash
# 核心优化 - 使用更快的模型
GENERATION_MODEL=Qwen/Qwen2.5-7B-Instruct  # 从72B改为7B
EMBEDDING_MODEL=text-embedding-3-small      # 更小的嵌入模型
EMBEDDING_DIMENSIONS=1536                   # 从8192降到1536

# 检索优化
RETRIEVAL_TOP_K=3              # 从5降到3
SIMILARITY_THRESHOLD=0.75      # 提高阈值
MAX_TOKENS=600                 # 从1000降到600

# 性能优化
API_TIMEOUT=8                  # 从10秒降到8秒
RETRY_BACKOFF_FACTOR=1.2       # 更快的重试

# 缓存优化
CACHE_TTL=3600                 # 1小时缓存
ENABLE_EMBEDDING_CACHE=true    # 嵌入缓存
ENABLE_GENERATION_CACHE=true   # 生成缓存
```

### 📈 详细优化指南

查看完整的性能分析和优化方案：
```bash
# 查看详细优化指南
cat PERFORMANCE_OPTIMIZATION.md

# 或在浏览器中查看
open PERFORMANCE_OPTIMIZATION.md
```

## 🛠️ 常用命令

### 项目管理

```bash
# 启动项目
python -m command_kb.cli.main --interactive

# 直接查询（非交互模式）
python -m command_kb.cli.main query "your question"

# 导入数据
python -m command_kb.cli.main import-data ./your-docs

# 查看帮助
python -m command_kb.cli.main --help
```

### 数据管理

```bash
# 清理缓存
rm -rf ./data/cache/*

# 重建向量数据库
rm -rf ./data/vector_db/*
# 然后重新导入数据

# 查看日志
tail -f ./logs/command_kb.log
```

### 依赖管理

```bash
# 使用UV管理依赖（推荐）
uv pip install -e .
uv pip compile --python-version 3.11 pyproject.toml -o requirements.lock
uv pip sync requirements.lock

# 开发环境
uv pip compile --python-version 3.11 --extra dev pyproject.toml -o dev-requirements.lock
uv pip sync dev-requirements.lock
```

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   # 检查.env文件中的API密钥是否正确
   grep SILICONFLOW_API_KEY .env
   ```

2. **响应太慢**
   ```bash
   # 检查网络连接
   curl -I https://api.siliconflow.cn/v1/models
   
   # 尝试更小的模型
   GENERATION_MODEL=Qwen/Qwen2.5-7B-Instruct
   ```

3. **导入失败**
   ```bash
   # 检查文档格式
   file ./data/raw/samples/*.md
   
   # 检查权限
   ls -la ./data/raw/samples/
   ```

4. **缓存问题**
   ```bash
   # 清理缓存
   rm -rf ./data/cache/*
   rm -rf ./data/vector_db/*
   ```

### 性能监控

```bash
# 查看系统状态
kb> status

# 查看成本统计
grep "cost" ./logs/command_kb.log

# 监控响应时间
grep "Response Time" ./logs/command_kb.log
```

## 📊 最佳实践

### 1. 文档编写

- ✅ 使用清晰的标题结构
- ✅ 包含具体的命令示例
- ✅ 添加参数说明和使用场景
- ✅ 使用代码块格式化命令

### 2. 查询技巧

- ✅ 使用具体的关键词
- ✅ 描述你想要的操作
- ✅ 包含上下文信息
- ❌ 避免过于宽泛的查询

### 3. 性能优化

- ✅ 定期清理缓存
- ✅ 使用合适的模型大小
- ✅ 优化文档结构
- ✅ 监控API使用成本

## 🆘 获取帮助

### 交互模式命令

```bash
kb> help          # 显示帮助信息
kb> status         # 系统状态
kb> exit           # 退出程序
```

### 日志查看

```bash
# 实时查看日志
tail -f ./logs/command_kb.log

# 查看错误日志
grep ERROR ./logs/command_kb.log

# 查看性能日志
grep "Response Time" ./logs/command_kb.log
```

---

## 🎉 开始使用

现在你已经了解了如何使用这个知识库系统！

1. **配置API密钥** → 编辑`.env`文件
2. **启动项目** → `python -m command_kb.cli.main --interactive`
3. **导入文档** → `import ./your-docs`
4. **开始查询** → `query "your question"`

享受智能命令搜索的便利吧！ 🚀