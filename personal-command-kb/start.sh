#!/bin/bash

# Personal Command-Line Vector Knowledge Base 启动脚本
# 使用方法: ./start.sh

echo "🚀 启动 Personal Command-Line Vector Knowledge Base"
echo "=================================================="

# 检查Python版本
echo "📋 检查Python版本..."
python_version=$(python --version 2>&1)
echo "   $python_version"

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"
else
    echo "⚠️  虚拟环境未激活，尝试激活..."
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
        echo "✅ 虚拟环境已激活"
    else
        echo "❌ 虚拟环境不存在，请先运行: uv venv && source .venv/bin/activate"
        exit 1
    fi
fi

# 检查.env文件
if [ -f ".env" ]; then
    echo "✅ 配置文件 .env 存在"
    # 检查API密钥
    if grep -q "sk-" .env; then
        echo "✅ API密钥已配置"
    else
        echo "⚠️  请在 .env 文件中配置API密钥"
    fi
else
    echo "❌ 配置文件 .env 不存在，请先复制: cp .env.example .env"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖安装..."
if python -c "import command_kb" 2>/dev/null; then
    echo "✅ 项目依赖已安装"
else
    echo "⚠️  依赖未安装，正在安装..."
    uv pip install -e .
fi

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p data/raw/samples
mkdir -p data/processed
mkdir -p data/cache
mkdir -p logs
echo "✅ 目录创建完成"

# 启动项目
echo ""
echo "🎯 启动交互式知识库..."
echo "=================================================="
echo "💡 使用提示:"
echo "   • import ./data/raw/samples  - 导入示例数据"
echo "   • query 'your question'     - 查询命令"
echo "   • status                    - 查看系统状态"
echo "   • help                      - 显示帮助"
echo "   • exit                      - 退出程序"
echo "=================================================="
echo ""

# 启动主程序
python -m command_kb.cli.main --interactive