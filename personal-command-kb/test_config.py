#!/usr/bin/env python3
"""
Simple configuration test script.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from command_kb.config import load_config
    print("✅ Successfully imported config module")
    
    # Load configuration
    config = load_config()
    print("✅ Configuration loaded successfully")
    
    # Check API providers
    print(f"📊 Found {len(config.api_providers)} API providers:")
    for provider_name, provider_config in config.api_providers.items():
        print(f"  • {provider_name}: {provider_config.enabled}")
    
    # Check primary provider
    print(f"🎯 Primary provider: {config.primary_provider}")
    
    # Check models
    primary_config = config.api_providers.get(config.primary_provider)
    if primary_config:
        print(f"🔤 Embedding model: {primary_config.embedding_model}")
        print(f"🤖 Generation model: {primary_config.generation_model}")
    
    print("✅ Configuration test completed successfully!")
    
except Exception as e:
    print(f"❌ Configuration test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)