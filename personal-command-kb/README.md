# Personal Command-Line Vector Knowledge Base

🤖 **RAG-powered intelligent command search and retrieval system**

A privacy-first, locally-deployed vector knowledge base that uses Retrieval-Augmented Generation (RAG) technology to provide intelligent search and AI-powered answers for your personal command-line snippets and code documentation.

## ✨ Key Features

- **🔒 Privacy-First**: All data processing happens locally with ChromaDB
- **🌐 Multi-Provider API Support**: OpenAI, SiliconFlow, Zhipu AI, Moonshot with automatic fallback
- **⚡ High Availability**: Built-in retry mechanisms, load balancing, and graceful degradation
- **💰 Cost-Controlled**: Intelligent cost monitoring and automatic provider switching
- **📱 Offline Capable**: Functions even with limited network connectivity
- **🎯 Semantic Search**: Advanced vector search across your knowledge base
- **🤖 AI-Powered Answers**: Get contextual answers to your command-line questions
- **📊 Rich CLI Interface**: Beautiful terminal UI with progress indicators and formatting

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CLI Interface │    │   RAG Pipeline   │    │  API Providers  │
│                 │    │                  │    │                 │
│ • Interactive   │───▶│ Load → Chunk     │───▶│ • OpenAI        │
│ • Commands      │    │   ↓      ↓       │    │ • SiliconFlow   │
│ • Rich UI       │    │ Embed → Store    │    │ • Zhipu AI      │
│                 │    │   ↓      ↓       │    │ • Moonshot      │
│                 │◀───│ Retrieve ← Generate │◀──│                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  ChromaDB       │
                    │  (Local Vector  │
                    │   Database)     │
                    └─────────────────┘
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd personal-command-kb

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e .
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file and add your API keys
nano .env
```

**Required API Keys** (get at least one):
- **OpenAI**: https://platform.openai.com/api-keys
- **SiliconFlow**: https://cloud.siliconflow.cn/ (Recommended - cost-effective)
- **Zhipu AI**: https://open.bigmodel.cn/
- **Moonshot**: https://platform.moonshot.cn/

### 3. Setup

```bash
# Run setup script
python scripts/setup.py --with-samples

# Or manual setup
python -m command_kb.cli.main import-data ./docs/
```

### 4. Usage

```bash
# Interactive mode (recommended)
python -m command_kb.cli.main --interactive

# Direct queries
python -m command_kb.cli.main query "how to list files in linux"

# Import your documentation
python -m command_kb.cli.main import-data ./my-docs/

# Check system status
python -m command_kb.cli.main status --detailed
```

## 📖 Usage Examples

### Interactive Mode
```bash
$ python -m command_kb.cli.main --interactive

🤖 Personal Command-Line Vector Knowledge Base
RAG-powered intelligent command search

kb> query docker container logs
🔍 Searching for: docker container logs
📄 Found 3 relevant documents
🤖 Answer:
─────────────────────────────────────────────────
To view Docker container logs, you can use several commands:

**View logs for a running container:**
```bash
docker logs container_name
docker logs container_id
```

**Follow logs in real-time:**
```bash
docker logs -f container_name
```

**View last N lines:**
```bash
docker logs --tail 50 container_name
```

**View logs with timestamps:**
```bash
docker logs -t container_name
```
─────────────────────────────────────────────────
💰 Cost: $0.000123 | Provider: siliconflow

kb> status
📊 System Status
════════════════════════════════════════════════
🗄️ Database: 156 documents
🔧 API Providers: 4 configured
📡 Total API Requests: 23
💰 Total Cost: $0.002341
```

### Command Line Usage
```bash
# Search for specific commands
python -m command_kb.cli.main query "git merge conflicts"

# Import documentation with custom settings
python -m command_kb.cli.main import-data ./docs/ --chunk-size 600 --batch-size 25

# Test API connectivity
python -m command_kb.cli.main test-api --provider siliconflow

# Get detailed system status
python -m command_kb.cli.main status --detailed --health-check --cost-report
```

## 📁 Project Structure

```
personal-command-kb/
├── src/command_kb/           # Main source code
│   ├── api/                  # API integration layer
│   │   ├── api_manager.py    # Multi-provider management
│   │   ├── openai_client.py  # OpenAI integration
│   │   ├── siliconflow_client.py # SiliconFlow integration
│   │   └── ...               # Other providers
│   ├── core/                 # RAG pipeline components
│   │   ├── loader.py         # Document loading
│   │   ├── chunker.py        # Text chunking
│   │   ├── embedder.py       # Text embedding
│   │   ├── storage.py        # Vector storage
│   │   ├── retriever.py      # Semantic search
│   │   └── generator.py      # Answer generation
│   ├── cli/                  # Command-line interface
│   ├── utils/                # Utilities and helpers
│   ├── config.py             # Configuration management
│   └── constants.py          # Application constants
├── config/                   # Configuration files
│   ├── config.yaml           # Main configuration
│   └── api_providers.yaml    # API provider settings
├── data/                     # Data storage
│   ├── raw/                  # Source documents
│   ├── processed/            # Processed data
│   ├── cache/                # API response cache
│   └── vector_db/            # ChromaDB database
├── scripts/                  # Utility scripts
└── tests/                    # Test suite
```

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# API Keys (at least one required)
OPENAI_API_KEY=sk-your-openai-key
SILICONFLOW_API_KEY=sk-your-siliconflow-key
ZHIPU_API_KEY=your-zhipu-key
MOONSHOT_API_KEY=sk-your-moonshot-key

# Provider Selection
DEFAULT_API_PROVIDER=siliconflow

# Cost Control
DAILY_COST_LIMIT=10.0
COST_WARNING_THRESHOLD=0.8

# Performance Tuning
CHUNK_SIZE=500
RETRIEVAL_TOP_K=5
ENABLE_CACHE=true
```

### YAML Configuration
The system uses YAML files for detailed configuration:
- `config/config.yaml` - Main application settings
- `config/api_providers.yaml` - Provider-specific configurations

## 🔧 Advanced Usage

### Custom Document Import
```bash
# Import with custom chunking
python -m command_kb.cli.main import-data ./docs/ \
  --chunk-size 600 \
  --chunk-overlap 100 \
  --batch-size 25

# Force reimport existing documents
python -m command_kb.cli.main import-data ./docs/ --force
```

### API Provider Management
```bash
# Test specific provider
python -m command_kb.cli.main test-api --provider openai

# Test only embedding functionality
python -m command_kb.cli.main test-api --operation embedding

# Check provider health
python -m command_kb.cli.main status --health-check
```

### Cost Monitoring
```bash
# View cost breakdown
python -m command_kb.cli.main status --cost-report

# Monitor costs in real-time
python -m command_kb.scripts.cost_monitor --live
```

## 🛠️ Development

### Setup Development Environment
```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest tests/ --cov=src/command_kb

# Code formatting
black src/ tests/
isort src/ tests/

# Type checking
mypy src/

# Security scan
bandit -r src/
```

### Adding New API Providers
1. Create provider client in `src/command_kb/api/`
2. Implement `BaseAPIClient` interface
3. Add provider configuration to `constants.py`
4. Update `api_providers.yaml` template
5. Add tests in `tests/test_api/`

### Extending RAG Pipeline
1. Core components are in `src/command_kb/core/`
2. Each step (Load→Chunk→Embed→Store→Retrieve→Generate) is modular
3. Implement new chunking strategies in `chunker.py`
4. Add custom retrievers in `retriever.py`
5. Extend generation logic in `generator.py`

## 📊 Performance & Monitoring

### Key Metrics
- **API Response Time**: < 2 seconds average
- **Search Latency**: < 500ms for cached queries  
- **API Success Rate**: > 99% with fallback
- **Memory Usage**: < 512MB for typical workloads

### Monitoring Commands
```bash
# System health check
python -m command_kb.cli.main status --health-check

# Performance monitoring
python -m command_kb.scripts.performance_monitor

# Usage statistics
python -m command_kb.scripts.usage_stats
```

## 🔒 Security & Privacy

- **Local Data Storage**: All documents stored locally in ChromaDB
- **API Key Security**: Environment variable-based key management
- **Data Anonymization**: Automatic filtering of sensitive patterns
- **Audit Logging**: Comprehensive activity logging
- **No Data Upload**: Source documents never leave your machine

## 💰 Cost Optimization

- **Provider Selection**: Automatic cost-based provider switching
- **Intelligent Caching**: Multi-layer caching reduces API calls
- **Batch Processing**: Efficient batch embedding operations
- **Cost Monitoring**: Real-time cost tracking and alerts
- **Budget Controls**: Automatic limits and warnings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain** - RAG framework foundation
- **ChromaDB** - Local vector database
- **Rich** - Beautiful terminal formatting
- **Click** - Command-line interface framework
- **OpenAI** - API standards and models

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/example/personal-command-kb/issues)
- **Discussions**: [GitHub Discussions](https://github.com/example/personal-command-kb/discussions)
- **Documentation**: [Read the Docs](https://personal-command-kb.readthedocs.io)

---

**Built with ❤️ for developers who value privacy and efficiency**