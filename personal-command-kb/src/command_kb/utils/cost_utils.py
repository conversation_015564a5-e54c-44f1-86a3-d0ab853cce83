"""
Cost calculation and tracking utilities for the Personal Command-Line Vector Knowledge Base.

This module provides cost management utilities:
- API cost calculation and tracking
- Budget management and alerts
- Cost optimization recommendations
- Usage analytics and reporting
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict

from ..constants import DEFAULT_EMBEDDING_COSTS, DEFAULT_GENERATION_COSTS

logger = logging.getLogger(__name__)


@dataclass
class CostEntry:
    """Individual cost entry record."""
    timestamp: datetime
    provider: str
    operation: str  # 'embedding' or 'generation'
    model: str
    tokens_used: int
    cost_usd: float
    request_id: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CostSummary:
    """Cost summary for a time period."""
    period_start: datetime
    period_end: datetime
    total_cost_usd: float
    total_tokens: int
    total_requests: int
    cost_by_provider: Dict[str, float] = field(default_factory=dict)
    cost_by_operation: Dict[str, float] = field(default_factory=dict)
    cost_by_model: Dict[str, float] = field(default_factory=dict)


class CostCalculator:
    """Calculate costs for API operations."""
    
    def __init__(self):
        """Initialize cost calculator."""
        self.embedding_costs = DEFAULT_EMBEDDING_COSTS.copy()
        self.generation_costs = DEFAULT_GENERATION_COSTS.copy()
    
    def calculate_embedding_cost(self, provider: str, model: str, tokens: int) -> float:
        """Calculate cost for embedding operation.
        
        Args:
            provider: API provider name
            model: Model name
            tokens: Number of tokens used
            
        Returns:
            Cost in USD
        """
        provider_costs = self.embedding_costs.get(provider, {})
        cost_per_1k = provider_costs.get(model, 0.0)
        
        if cost_per_1k == 0.0:
            logger.warning(f"No cost data for embedding: {provider}/{model}")
        
        return (tokens / 1000.0) * cost_per_1k
    
    def calculate_generation_cost(self, provider: str, model: str, tokens: int) -> float:
        """Calculate cost for text generation operation.
        
        Args:
            provider: API provider name
            model: Model name
            tokens: Number of tokens used
            
        Returns:
            Cost in USD
        """
        provider_costs = self.generation_costs.get(provider, {})
        cost_per_1k = provider_costs.get(model, 0.0)
        
        if cost_per_1k == 0.0:
            logger.warning(f"No cost data for generation: {provider}/{model}")
        
        return (tokens / 1000.0) * cost_per_1k
    
    def update_costs(self, provider: str, embedding_costs: Dict[str, float] = None,
                    generation_costs: Dict[str, float] = None) -> None:
        """Update cost information for a provider.
        
        Args:
            provider: Provider name
            embedding_costs: Dictionary of model -> cost_per_1k_tokens
            generation_costs: Dictionary of model -> cost_per_1k_tokens
        """
        if embedding_costs:
            if provider not in self.embedding_costs:
                self.embedding_costs[provider] = {}
            self.embedding_costs[provider].update(embedding_costs)
            logger.info(f"Updated embedding costs for {provider}")
        
        if generation_costs:
            if provider not in self.generation_costs:
                self.generation_costs[provider] = {}
            self.generation_costs[provider].update(generation_costs)
            logger.info(f"Updated generation costs for {provider}")
    
    def get_cost_per_token(self, provider: str, operation: str, model: str) -> float:
        """Get cost per token for specific operation.
        
        Args:
            provider: Provider name
            operation: 'embedding' or 'generation'
            model: Model name
            
        Returns:
            Cost per token in USD
        """
        if operation == 'embedding':
            provider_costs = self.embedding_costs.get(provider, {})
        elif operation == 'generation':
            provider_costs = self.generation_costs.get(provider, {})
        else:
            logger.error(f"Unknown operation: {operation}")
            return 0.0
        
        cost_per_1k = provider_costs.get(model, 0.0)
        return cost_per_1k / 1000.0


class CostTracker:
    """Track and analyze API costs over time."""
    
    def __init__(self, max_entries: int = 10000):
        """Initialize cost tracker.
        
        Args:
            max_entries: Maximum number of cost entries to keep
        """
        self.max_entries = max_entries
        self.cost_entries: List[CostEntry] = []
        self.calculator = CostCalculator()
    
    def record_cost(self, provider: str, operation: str, model: str,
                   tokens_used: int, request_id: str = "",
                   metadata: Dict[str, Any] = None) -> float:
        """Record a cost entry.
        
        Args:
            provider: API provider name
            operation: Operation type ('embedding' or 'generation')
            model: Model name
            tokens_used: Number of tokens used
            request_id: Optional request identifier
            metadata: Optional additional metadata
            
        Returns:
            Calculated cost in USD
        """
        if operation == 'embedding':
            cost = self.calculator.calculate_embedding_cost(provider, model, tokens_used)
        elif operation == 'generation':
            cost = self.calculator.calculate_generation_cost(provider, model, tokens_used)
        else:
            logger.error(f"Unknown operation: {operation}")
            return 0.0
        
        entry = CostEntry(
            timestamp=datetime.now(),
            provider=provider,
            operation=operation,
            model=model,
            tokens_used=tokens_used,
            cost_usd=cost,
            request_id=request_id,
            metadata=metadata or {}
        )
        
        self.cost_entries.append(entry)
        
        # Trim old entries if needed
        if len(self.cost_entries) > self.max_entries:
            self.cost_entries = self.cost_entries[-self.max_entries:]
        
        logger.debug(f"Recorded cost: {provider}/{model} - ${cost:.6f}")
        return cost
    
    def get_total_cost(self, hours: int = 24) -> float:
        """Get total cost for specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Total cost in USD
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        total = sum(
            entry.cost_usd for entry in self.cost_entries
            if entry.timestamp >= cutoff_time
        )
        
        return total
    
    def get_cost_summary(self, hours: int = 24) -> CostSummary:
        """Get detailed cost summary for time period.
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            CostSummary object
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_entries = [
            entry for entry in self.cost_entries
            if entry.timestamp >= cutoff_time
        ]
        
        if not recent_entries:
            return CostSummary(
                period_start=cutoff_time,
                period_end=datetime.now(),
                total_cost_usd=0.0,
                total_tokens=0,
                total_requests=0
            )
        
        # Calculate totals
        total_cost = sum(entry.cost_usd for entry in recent_entries)
        total_tokens = sum(entry.tokens_used for entry in recent_entries)
        total_requests = len(recent_entries)
        
        # Group by provider
        cost_by_provider = defaultdict(float)
        for entry in recent_entries:
            cost_by_provider[entry.provider] += entry.cost_usd
        
        # Group by operation
        cost_by_operation = defaultdict(float)
        for entry in recent_entries:
            cost_by_operation[entry.operation] += entry.cost_usd
        
        # Group by model
        cost_by_model = defaultdict(float)
        for entry in recent_entries:
            model_key = f"{entry.provider}/{entry.model}"
            cost_by_model[model_key] += entry.cost_usd
        
        return CostSummary(
            period_start=cutoff_time,
            period_end=datetime.now(),
            total_cost_usd=total_cost,
            total_tokens=total_tokens,
            total_requests=total_requests,
            cost_by_provider=dict(cost_by_provider),
            cost_by_operation=dict(cost_by_operation),
            cost_by_model=dict(cost_by_model)
        )
    
    def get_usage_trends(self, days: int = 7) -> Dict[str, Any]:
        """Get usage trends over multiple days.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with trend analysis
        """
        daily_costs = []
        daily_tokens = []
        daily_requests = []
        
        for day in range(days):
            day_start = datetime.now() - timedelta(days=day+1)
            day_end = datetime.now() - timedelta(days=day)
            
            day_entries = [
                entry for entry in self.cost_entries
                if day_start <= entry.timestamp < day_end
            ]
            
            day_cost = sum(entry.cost_usd for entry in day_entries)
            day_tokens = sum(entry.tokens_used for entry in day_entries)
            day_requests = len(day_entries)
            
            daily_costs.append(day_cost)
            daily_tokens.append(day_tokens)
            daily_requests.append(day_requests)
        
        # Calculate trends (simple linear trend)
        def calculate_trend(values):
            if len(values) < 2:
                return 0.0
            return (values[0] - values[-1]) / len(values)
        
        return {
            'period_days': days,
            'daily_costs': daily_costs,
            'daily_tokens': daily_tokens,
            'daily_requests': daily_requests,
            'cost_trend': calculate_trend(daily_costs),
            'token_trend': calculate_trend(daily_tokens),
            'request_trend': calculate_trend(daily_requests),
            'avg_daily_cost': sum(daily_costs) / len(daily_costs) if daily_costs else 0,
            'avg_daily_tokens': sum(daily_tokens) / len(daily_tokens) if daily_tokens else 0,
            'avg_daily_requests': sum(daily_requests) / len(daily_requests) if daily_requests else 0
        }


class BudgetManager:
    """Manage budgets and cost alerts."""
    
    def __init__(self, daily_limit: float = 10.0, warning_threshold: float = 0.8):
        """Initialize budget manager.
        
        Args:
            daily_limit: Daily spending limit in USD
            warning_threshold: Warning threshold as fraction of limit
        """
        self.daily_limit = daily_limit
        self.warning_threshold = warning_threshold
        self.cost_tracker: Optional[CostTracker] = None
        self.alert_callbacks: List[callable] = []
        
        # Alert state tracking
        self.warning_sent = False
        self.limit_exceeded = False
        self.last_check_date = datetime.now().date()
    
    def set_cost_tracker(self, cost_tracker: CostTracker) -> None:
        """Set the cost tracker to monitor.
        
        Args:
            cost_tracker: CostTracker instance
        """
        self.cost_tracker = cost_tracker
    
    def add_alert_callback(self, callback: callable) -> None:
        """Add callback for budget alerts.
        
        Args:
            callback: Function to call when alerts are triggered
        """
        self.alert_callbacks.append(callback)
    
    def check_budget(self) -> Dict[str, Any]:
        """Check current budget status.
        
        Returns:
            Dictionary with budget status information
        """
        if not self.cost_tracker:
            return {'error': 'No cost tracker configured'}
        
        # Reset daily flags if new day
        current_date = datetime.now().date()
        if current_date != self.last_check_date:
            self.warning_sent = False
            self.limit_exceeded = False
            self.last_check_date = current_date
        
        # Get today's costs
        daily_cost = self.cost_tracker.get_total_cost(hours=24)
        
        # Calculate status
        usage_percent = (daily_cost / self.daily_limit) * 100
        warning_threshold_amount = self.daily_limit * self.warning_threshold
        remaining_budget = max(0, self.daily_limit - daily_cost)
        
        status = {
            'daily_limit': self.daily_limit,
            'daily_cost': daily_cost,
            'remaining_budget': remaining_budget,
            'usage_percent': usage_percent,
            'warning_threshold': warning_threshold_amount,
            'is_over_warning': daily_cost >= warning_threshold_amount,
            'is_over_limit': daily_cost >= self.daily_limit,
            'status': 'ok'
        }
        
        # Determine status
        if daily_cost >= self.daily_limit:
            status['status'] = 'over_limit'
            if not self.limit_exceeded:
                self._send_alert('budget_exceeded', status)
                self.limit_exceeded = True
        elif daily_cost >= warning_threshold_amount:
            status['status'] = 'warning'
            if not self.warning_sent:
                self._send_alert('budget_warning', status)
                self.warning_sent = True
        
        return status
    
    def is_budget_available(self, estimated_cost: float = 0.0) -> bool:
        """Check if budget is available for additional spending.
        
        Args:
            estimated_cost: Estimated cost of upcoming operation
            
        Returns:
            True if budget allows the operation
        """
        if not self.cost_tracker:
            return True  # Allow if no tracking
        
        daily_cost = self.cost_tracker.get_total_cost(hours=24)
        projected_cost = daily_cost + estimated_cost
        
        return projected_cost <= self.daily_limit
    
    def get_budget_recommendations(self) -> List[str]:
        """Get cost optimization recommendations.
        
        Returns:
            List of recommendation strings
        """
        if not self.cost_tracker:
            return ["Configure cost tracking for recommendations"]
        
        recommendations = []
        summary = self.cost_tracker.get_cost_summary(hours=24)
        
        # Analyze provider costs
        if summary.cost_by_provider:
            most_expensive_provider = max(
                summary.cost_by_provider.items(),
                key=lambda x: x[1]
            )
            
            if most_expensive_provider[1] > summary.total_cost_usd * 0.6:
                recommendations.append(
                    f"Consider using alternative to {most_expensive_provider[0]} "
                    f"(${most_expensive_provider[1]:.4f} of ${summary.total_cost_usd:.4f})"
                )
        
        # Analyze operation costs
        if summary.cost_by_operation:
            embedding_cost = summary.cost_by_operation.get('embedding', 0)
            generation_cost = summary.cost_by_operation.get('generation', 0)
            
            if embedding_cost > generation_cost * 2:
                recommendations.append(
                    "High embedding costs detected. Consider enabling embedding cache "
                    "or using cheaper embedding models"
                )
            
            if generation_cost > embedding_cost * 3:
                recommendations.append(
                    "High generation costs detected. Consider enabling response cache "
                    "or using cheaper generation models"
                )
        
        # Check usage patterns
        trends = self.cost_tracker.get_usage_trends(days=7)
        if trends['cost_trend'] > 0:
            recommendations.append(
                f"Costs are trending upward (${trends['cost_trend']:.4f}/day). "
                "Monitor usage patterns and consider optimization"
            )
        
        # General recommendations
        if summary.total_cost_usd > self.daily_limit * 0.5:
            recommendations.extend([
                "Enable caching to reduce API calls",
                "Use batch processing for multiple operations",
                "Consider using cheaper API providers for non-critical operations"
            ])
        
        return recommendations or ["No specific recommendations at this time"]
    
    def _send_alert(self, alert_type: str, status: Dict[str, Any]) -> None:
        """Send budget alert to registered callbacks.
        
        Args:
            alert_type: Type of alert ('budget_warning' or 'budget_exceeded')
            status: Current budget status
        """
        alert_data = {
            'type': alert_type,
            'timestamp': datetime.now().isoformat(),
            'daily_limit': self.daily_limit,
            'daily_cost': status['daily_cost'],
            'usage_percent': status['usage_percent'],
            'remaining_budget': status['remaining_budget']
        }
        
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                logger.error(f"Budget alert callback failed: {e}")
        
        logger.warning(f"Budget alert: {alert_type} - ${status['daily_cost']:.4f}/${self.daily_limit}")


def create_cost_report(cost_tracker: CostTracker, days: int = 7) -> str:
    """Create a formatted cost report.
    
    Args:
        cost_tracker: CostTracker instance
        days: Number of days to include in report
        
    Returns:
        Formatted cost report string
    """
    summary = cost_tracker.get_cost_summary(hours=24)
    trends = cost_tracker.get_usage_trends(days=days)
    
    report = []
    report.append("=" * 60)
    report.append("COST ANALYSIS REPORT")
    report.append("=" * 60)
    
    # Daily summary
    report.append(f"\nDaily Summary (Last 24 Hours):")
    report.append(f"  Total Cost: ${summary.total_cost_usd:.4f}")
    report.append(f"  Total Tokens: {summary.total_tokens:,}")
    report.append(f"  Total Requests: {summary.total_requests}")
    
    if summary.total_tokens > 0:
        avg_cost_per_token = summary.total_cost_usd / summary.total_tokens
        report.append(f"  Avg Cost/Token: ${avg_cost_per_token:.8f}")
    
    # Provider breakdown
    if summary.cost_by_provider:
        report.append(f"\nCost by Provider:")
        for provider, cost in sorted(summary.cost_by_provider.items(), 
                                   key=lambda x: x[1], reverse=True):
            percentage = (cost / summary.total_cost_usd) * 100 if summary.total_cost_usd > 0 else 0
            report.append(f"  {provider}: ${cost:.4f} ({percentage:.1f}%)")
    
    # Operation breakdown
    if summary.cost_by_operation:
        report.append(f"\nCost by Operation:")
        for operation, cost in sorted(summary.cost_by_operation.items(),
                                    key=lambda x: x[1], reverse=True):
            percentage = (cost / summary.total_cost_usd) * 100 if summary.total_cost_usd > 0 else 0
            report.append(f"  {operation}: ${cost:.4f} ({percentage:.1f}%)")
    
    # Weekly trends
    report.append(f"\nWeekly Trends ({days} days):")
    report.append(f"  Avg Daily Cost: ${trends['avg_daily_cost']:.4f}")
    report.append(f"  Avg Daily Tokens: {trends['avg_daily_tokens']:,.0f}")
    report.append(f"  Avg Daily Requests: {trends['avg_daily_requests']:.0f}")
    
    if trends['cost_trend'] != 0:
        trend_direction = "increasing" if trends['cost_trend'] > 0 else "decreasing"
        report.append(f"  Cost Trend: {trend_direction} by ${abs(trends['cost_trend']):.4f}/day")
    
    report.append("\n" + "=" * 60)
    
    return "\n".join(report)


def estimate_operation_cost(calculator: CostCalculator, provider: str,
                          operation: str, model: str, estimated_tokens: int) -> float:
    """Estimate cost for a planned operation.
    
    Args:
        calculator: CostCalculator instance
        provider: API provider name
        operation: Operation type ('embedding' or 'generation')
        model: Model name
        estimated_tokens: Estimated number of tokens
        
    Returns:
        Estimated cost in USD
    """
    if operation == 'embedding':
        return calculator.calculate_embedding_cost(provider, model, estimated_tokens)
    elif operation == 'generation':
        return calculator.calculate_generation_cost(provider, model, estimated_tokens)
    else:
        logger.error(f"Unknown operation for cost estimation: {operation}")
        return 0.0


def compare_provider_costs(calculator: CostCalculator, operation: str,
                          tokens: int) -> List[Tuple[str, str, float]]:
    """Compare costs across different providers for an operation.
    
    Args:
        calculator: CostCalculator instance
        operation: Operation type ('embedding' or 'generation')
        tokens: Number of tokens
        
    Returns:
        List of (provider, model, cost) tuples sorted by cost
    """
    costs = []
    
    if operation == 'embedding':
        cost_data = calculator.embedding_costs
    elif operation == 'generation':
        cost_data = calculator.generation_costs
    else:
        logger.error(f"Unknown operation: {operation}")
        return []
    
    for provider, models in cost_data.items():
        for model, cost_per_1k in models.items():
            cost = (tokens / 1000.0) * cost_per_1k
            costs.append((provider, model, cost))
    
    # Sort by cost (ascending)
    costs.sort(key=lambda x: x[2])
    
    return costs