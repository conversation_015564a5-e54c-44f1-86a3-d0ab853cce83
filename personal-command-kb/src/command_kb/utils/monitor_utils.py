"""
Monitoring utilities for the Personal Command-Line Vector Knowledge Base.

This module provides monitoring and health check utilities:
- System health monitoring
- Performance tracking and metrics
- API health checks
- Resource usage monitoring
"""

import time
import psutil
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from threading import Lock
from collections import deque

logger = logging.getLogger(__name__)


@dataclass
class HealthStatus:
    """Health status container."""
    is_healthy: bool
    status: str
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """Performance metrics container."""
    timestamp: datetime
    response_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    disk_usage_percent: float
    active_connections: int = 0
    cache_hit_rate: float = 0.0
    error_rate: float = 0.0


class HealthMonitor:
    """System health monitoring with configurable checks."""
    
    def __init__(self, check_interval: int = 300):
        """Initialize health monitor.
        
        Args:
            check_interval: Health check interval in seconds
        """
        self.check_interval = check_interval
        self.health_checks: Dict[str, Callable[[], HealthStatus]] = {}
        self.last_check_time = 0
        self.health_status: Dict[str, HealthStatus] = {}
        self._lock = Lock()
    
    def register_check(self, name: str, check_func: Callable[[], HealthStatus]) -> None:
        """Register a health check function.
        
        Args:
            name: Name of the health check
            check_func: Function that returns HealthStatus
        """
        self.health_checks[name] = check_func
        logger.info(f"Registered health check: {name}")
    
    def run_checks(self, force: bool = False) -> Dict[str, HealthStatus]:
        """Run all registered health checks.
        
        Args:
            force: Force checks even if interval hasn't elapsed
            
        Returns:
            Dictionary of health check results
        """
        current_time = time.time()
        
        if not force and (current_time - self.last_check_time) < self.check_interval:
            return self.health_status.copy()
        
        with self._lock:
            self.health_status.clear()
            
            for name, check_func in self.health_checks.items():
                try:
                    status = check_func()
                    self.health_status[name] = status
                    
                    if not status.is_healthy:
                        logger.warning(f"Health check failed: {name} - {status.message}")
                    else:
                        logger.debug(f"Health check passed: {name}")
                        
                except Exception as e:
                    logger.error(f"Health check error for {name}: {e}")
                    self.health_status[name] = HealthStatus(
                        is_healthy=False,
                        status="error",
                        message=f"Check failed: {str(e)}"
                    )
            
            self.last_check_time = current_time
        
        return self.health_status.copy()
    
    def get_overall_health(self) -> HealthStatus:
        """Get overall system health status.
        
        Returns:
            Overall health status
        """
        if not self.health_status:
            self.run_checks()
        
        failed_checks = [
            name for name, status in self.health_status.items()
            if not status.is_healthy
        ]
        
        if not failed_checks:
            return HealthStatus(
                is_healthy=True,
                status="healthy",
                message="All health checks passed",
                details={"total_checks": len(self.health_status)}
            )
        else:
            return HealthStatus(
                is_healthy=False,
                status="unhealthy",
                message=f"Failed checks: {', '.join(failed_checks)}",
                details={
                    "failed_checks": failed_checks,
                    "total_checks": len(self.health_status)
                }
            )
    
    def get_check_status(self, name: str) -> Optional[HealthStatus]:
        """Get status of specific health check.
        
        Args:
            name: Name of health check
            
        Returns:
            Health status or None if not found
        """
        return self.health_status.get(name)


class PerformanceTracker:
    """Performance metrics tracking and analysis."""
    
    def __init__(self, max_history: int = 1000):
        """Initialize performance tracker.
        
        Args:
            max_history: Maximum number of metrics to keep in history
        """
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self._lock = Lock()
        
        # Current metrics
        self.current_metrics: Optional[PerformanceMetrics] = None
        
        # Thresholds for alerts
        self.thresholds = {
            'response_time_ms': 2000,
            'memory_usage_mb': 512,
            'cpu_usage_percent': 80,
            'disk_usage_percent': 90,
            'error_rate_percent': 5
        }
    
    def record_metrics(self, **kwargs) -> None:
        """Record performance metrics.
        
        Args:
            **kwargs: Metric values to record
        """
        # Get system metrics
        try:
            memory_info = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            disk_usage = psutil.disk_usage('/')
            
            system_metrics = {
                'memory_usage_mb': memory_info.used / (1024 * 1024),
                'cpu_usage_percent': cpu_percent,
                'disk_usage_percent': disk_usage.percent,
            }
        except Exception as e:
            logger.warning(f"Failed to get system metrics: {e}")
            system_metrics = {
                'memory_usage_mb': 0,
                'cpu_usage_percent': 0,
                'disk_usage_percent': 0,
            }
        
        # Combine with provided metrics
        all_metrics = {**system_metrics, **kwargs}
        
        metrics = PerformanceMetrics(
            timestamp=datetime.now(),
            response_time=all_metrics.get('response_time', 0),
            memory_usage_mb=all_metrics['memory_usage_mb'],
            cpu_usage_percent=all_metrics['cpu_usage_percent'],
            disk_usage_percent=all_metrics['disk_usage_percent'],
            active_connections=all_metrics.get('active_connections', 0),
            cache_hit_rate=all_metrics.get('cache_hit_rate', 0),
            error_rate=all_metrics.get('error_rate', 0)
        )
        
        with self._lock:
            self.metrics_history.append(metrics)
            self.current_metrics = metrics
        
        # Check thresholds
        self._check_thresholds(metrics)
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """Get current performance metrics.
        
        Returns:
            Current metrics or None if no metrics recorded
        """
        return self.current_metrics
    
    def get_metrics_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get summary of metrics over specified duration.
        
        Args:
            duration_minutes: Duration to analyze in minutes
            
        Returns:
            Dictionary with metrics summary
        """
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        with self._lock:
            recent_metrics = [
                m for m in self.metrics_history
                if m.timestamp >= cutoff_time
            ]
        
        if not recent_metrics:
            return {'error': 'No metrics available for specified duration'}
        
        # Calculate averages
        avg_response_time = sum(m.response_time for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
        avg_cpu = sum(m.cpu_usage_percent for m in recent_metrics) / len(recent_metrics)
        avg_cache_hit_rate = sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics)
        avg_error_rate = sum(m.error_rate for m in recent_metrics) / len(recent_metrics)
        
        # Calculate maximums
        max_response_time = max(m.response_time for m in recent_metrics)
        max_memory = max(m.memory_usage_mb for m in recent_metrics)
        max_cpu = max(m.cpu_usage_percent for m in recent_metrics)
        
        return {
            'duration_minutes': duration_minutes,
            'sample_count': len(recent_metrics),
            'averages': {
                'response_time_ms': round(avg_response_time * 1000, 2),
                'memory_usage_mb': round(avg_memory, 2),
                'cpu_usage_percent': round(avg_cpu, 2),
                'cache_hit_rate': round(avg_cache_hit_rate * 100, 2),
                'error_rate_percent': round(avg_error_rate * 100, 2)
            },
            'maximums': {
                'response_time_ms': round(max_response_time * 1000, 2),
                'memory_usage_mb': round(max_memory, 2),
                'cpu_usage_percent': round(max_cpu, 2)
            },
            'timestamp': datetime.now().isoformat()
        }
    
    def _check_thresholds(self, metrics: PerformanceMetrics) -> None:
        """Check if metrics exceed thresholds and log warnings."""
        if metrics.response_time * 1000 > self.thresholds['response_time_ms']:
            logger.warning(f"High response time: {metrics.response_time * 1000:.2f}ms")
        
        if metrics.memory_usage_mb > self.thresholds['memory_usage_mb']:
            logger.warning(f"High memory usage: {metrics.memory_usage_mb:.2f}MB")
        
        if metrics.cpu_usage_percent > self.thresholds['cpu_usage_percent']:
            logger.warning(f"High CPU usage: {metrics.cpu_usage_percent:.2f}%")
        
        if metrics.disk_usage_percent > self.thresholds['disk_usage_percent']:
            logger.warning(f"High disk usage: {metrics.disk_usage_percent:.2f}%")
        
        if metrics.error_rate * 100 > self.thresholds['error_rate_percent']:
            logger.warning(f"High error rate: {metrics.error_rate * 100:.2f}%")
    
    def set_threshold(self, metric: str, value: float) -> None:
        """Set threshold for a metric.
        
        Args:
            metric: Metric name
            value: Threshold value
        """
        if metric in self.thresholds:
            self.thresholds[metric] = value
            logger.info(f"Updated threshold for {metric}: {value}")
        else:
            logger.warning(f"Unknown metric for threshold: {metric}")


class MetricsCollector:
    """Collect and aggregate various system metrics."""
    
    def __init__(self):
        """Initialize metrics collector."""
        self.counters: Dict[str, int] = {}
        self.gauges: Dict[str, float] = {}
        self.timers: Dict[str, List[float]] = {}
        self._lock = Lock()
    
    def increment_counter(self, name: str, value: int = 1) -> None:
        """Increment a counter metric.
        
        Args:
            name: Counter name
            value: Value to increment by
        """
        with self._lock:
            self.counters[name] = self.counters.get(name, 0) + value
    
    def set_gauge(self, name: str, value: float) -> None:
        """Set a gauge metric value.
        
        Args:
            name: Gauge name
            value: Gauge value
        """
        with self._lock:
            self.gauges[name] = value
    
    def record_timer(self, name: str, duration: float) -> None:
        """Record a timer metric.
        
        Args:
            name: Timer name
            duration: Duration in seconds
        """
        with self._lock:
            if name not in self.timers:
                self.timers[name] = []
            self.timers[name].append(duration)
            
            # Keep only recent measurements (last 1000)
            if len(self.timers[name]) > 1000:
                self.timers[name] = self.timers[name][-1000:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics.
        
        Returns:
            Dictionary with all metrics
        """
        with self._lock:
            metrics = {
                'counters': self.counters.copy(),
                'gauges': self.gauges.copy(),
                'timers': {}
            }
            
            # Calculate timer statistics
            for name, durations in self.timers.items():
                if durations:
                    metrics['timers'][name] = {
                        'count': len(durations),
                        'avg': sum(durations) / len(durations),
                        'min': min(durations),
                        'max': max(durations),
                        'p95': self._percentile(durations, 0.95),
                        'p99': self._percentile(durations, 0.99)
                    }
        
        return metrics
    
    def reset_metrics(self) -> None:
        """Reset all metrics."""
        with self._lock:
            self.counters.clear()
            self.gauges.clear()
            self.timers.clear()
        logger.info("All metrics reset")
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile)
        return sorted_values[min(index, len(sorted_values) - 1)]


def create_system_health_check() -> Callable[[], HealthStatus]:
    """Create a system health check function.
    
    Returns:
        Health check function
    """
    def check_system_health() -> HealthStatus:
        try:
            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                return HealthStatus(
                    is_healthy=False,
                    status="critical",
                    message=f"High memory usage: {memory.percent:.1f}%",
                    details={'memory_percent': memory.percent}
                )
            
            # Check disk usage
            disk = psutil.disk_usage('/')
            if disk.percent > 95:
                return HealthStatus(
                    is_healthy=False,
                    status="critical",
                    message=f"High disk usage: {disk.percent:.1f}%",
                    details={'disk_percent': disk.percent}
                )
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 95:
                return HealthStatus(
                    is_healthy=False,
                    status="warning",
                    message=f"High CPU usage: {cpu_percent:.1f}%",
                    details={'cpu_percent': cpu_percent}
                )
            
            return HealthStatus(
                is_healthy=True,
                status="healthy",
                message="System resources within normal limits",
                details={
                    'memory_percent': memory.percent,
                    'disk_percent': disk.percent,
                    'cpu_percent': cpu_percent
                }
            )
            
        except Exception as e:
            return HealthStatus(
                is_healthy=False,
                status="error",
                message=f"Failed to check system health: {str(e)}"
            )
    
    return check_system_health


def create_api_health_check(api_manager) -> Callable[[], HealthStatus]:
    """Create an API health check function.
    
    Args:
        api_manager: API manager instance
        
    Returns:
        Health check function
    """
    def check_api_health() -> HealthStatus:
        try:
            # Run health checks on all API providers
            health_results = api_manager.health_check()
            
            failed_providers = [
                name for name, result in health_results.items()
                if not result.success
            ]
            
            if not failed_providers:
                return HealthStatus(
                    is_healthy=True,
                    status="healthy",
                    message="All API providers healthy",
                    details={
                        'total_providers': len(health_results),
                        'healthy_providers': len(health_results)
                    }
                )
            elif len(failed_providers) < len(health_results):
                return HealthStatus(
                    is_healthy=True,
                    status="degraded",
                    message=f"Some API providers unhealthy: {', '.join(failed_providers)}",
                    details={
                        'total_providers': len(health_results),
                        'healthy_providers': len(health_results) - len(failed_providers),
                        'failed_providers': failed_providers
                    }
                )
            else:
                return HealthStatus(
                    is_healthy=False,
                    status="critical",
                    message="All API providers unhealthy",
                    details={
                        'total_providers': len(health_results),
                        'failed_providers': failed_providers
                    }
                )
                
        except Exception as e:
            return HealthStatus(
                is_healthy=False,
                status="error",
                message=f"Failed to check API health: {str(e)}"
            )
    
    return check_api_health


def create_database_health_check(storage) -> Callable[[], HealthStatus]:
    """Create a database health check function.
    
    Args:
        storage: Storage instance
        
    Returns:
        Health check function
    """
    def check_database_health() -> HealthStatus:
        try:
            # Get collection stats
            stats = storage.get_collection_stats()
            
            if 'error' in stats:
                return HealthStatus(
                    is_healthy=False,
                    status="error",
                    message=f"Database error: {stats['error']}"
                )
            
            document_count = stats.get('document_count', 0)
            
            return HealthStatus(
                is_healthy=True,
                status="healthy",
                message=f"Database healthy with {document_count} documents",
                details=stats
            )
            
        except Exception as e:
            return HealthStatus(
                is_healthy=False,
                status="error",
                message=f"Failed to check database health: {str(e)}"
            )
    
    return check_database_health


class AlertManager:
    """Manage alerts based on health checks and metrics."""
    
    def __init__(self):
        """Initialize alert manager."""
        self.alert_handlers: List[Callable[[str, Dict[str, Any]], None]] = []
        self.alert_history: List[Dict[str, Any]] = []
        self.max_history = 1000
    
    def add_alert_handler(self, handler: Callable[[str, Dict[str, Any]], None]) -> None:
        """Add an alert handler function.
        
        Args:
            handler: Function to handle alerts
        """
        self.alert_handlers.append(handler)
        logger.info("Added alert handler")
    
    def send_alert(self, alert_type: str, details: Dict[str, Any]) -> None:
        """Send an alert to all registered handlers.
        
        Args:
            alert_type: Type of alert
            details: Alert details
        """
        alert = {
            'type': alert_type,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        
        # Add to history
        self.alert_history.append(alert)
        if len(self.alert_history) > self.max_history:
            self.alert_history = self.alert_history[-self.max_history:]
        
        # Send to handlers
        for handler in self.alert_handlers:
            try:
                handler(alert_type, details)
            except Exception as e:
                logger.error(f"Alert handler failed: {e}")
        
        logger.warning(f"Alert sent: {alert_type} - {details}")
    
    def get_alert_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent alert history.
        
        Args:
            limit: Maximum number of alerts to return
            
        Returns:
            List of recent alerts
        """
        return self.alert_history[-limit:]


def create_log_alert_handler() -> Callable[[str, Dict[str, Any]], None]:
    """Create a simple log-based alert handler.
    
    Returns:
        Alert handler function
    """
    def log_alert_handler(alert_type: str, details: Dict[str, Any]) -> None:
        logger.critical(f"ALERT [{alert_type}]: {details}")
    
    return log_alert_handler


class MonitoringDashboard:
    """Simple monitoring dashboard for displaying system status."""
    
    def __init__(self, health_monitor: HealthMonitor, 
                 performance_tracker: PerformanceTracker,
                 metrics_collector: MetricsCollector):
        """Initialize monitoring dashboard.
        
        Args:
            health_monitor: Health monitor instance
            performance_tracker: Performance tracker instance
            metrics_collector: Metrics collector instance
        """
        self.health_monitor = health_monitor
        self.performance_tracker = performance_tracker
        self.metrics_collector = metrics_collector
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data.
        
        Returns:
            Dictionary with all monitoring data
        """
        # Get health status
        overall_health = self.health_monitor.get_overall_health()
        health_checks = self.health_monitor.run_checks()
        
        # Get performance metrics
        current_metrics = self.performance_tracker.get_current_metrics()
        metrics_summary = self.performance_tracker.get_metrics_summary(60)
        
        # Get collected metrics
        collected_metrics = self.metrics_collector.get_metrics()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'overall_health': {
                'is_healthy': overall_health.is_healthy,
                'status': overall_health.status,
                'message': overall_health.message,
                'details': overall_health.details
            },
            'health_checks': {
                name: {
                    'is_healthy': status.is_healthy,
                    'status': status.status,
                    'message': status.message,
                    'timestamp': status.timestamp.isoformat()
                }
                for name, status in health_checks.items()
            },
            'current_performance': {
                'response_time_ms': round(current_metrics.response_time * 1000, 2) if current_metrics else 0,
                'memory_usage_mb': round(current_metrics.memory_usage_mb, 2) if current_metrics else 0,
                'cpu_usage_percent': round(current_metrics.cpu_usage_percent, 2) if current_metrics else 0,
                'disk_usage_percent': round(current_metrics.disk_usage_percent, 2) if current_metrics else 0,
                'cache_hit_rate': round(current_metrics.cache_hit_rate * 100, 2) if current_metrics else 0,
            },
            'performance_summary': metrics_summary,
            'metrics': collected_metrics
        }
    
    def print_dashboard(self) -> None:
        """Print dashboard to console."""
        data = self.get_dashboard_data()
        
        print("\n" + "="*60)
        print("SYSTEM MONITORING DASHBOARD")
        print("="*60)
        
        # Overall health
        health = data['overall_health']
        status_symbol = "✅" if health['is_healthy'] else "❌"
        print(f"\nOverall Health: {status_symbol} {health['status'].upper()}")
        print(f"Message: {health['message']}")
        
        # Health checks
        print(f"\nHealth Checks:")
        for name, status in data['health_checks'].items():
            symbol = "✅" if status['is_healthy'] else "❌"
            print(f"  {symbol} {name}: {status['message']}")
        
        # Performance
        perf = data['current_performance']
        print(f"\nCurrent Performance:")
        print(f"  Response Time: {perf['response_time_ms']}ms")
        print(f"  Memory Usage: {perf['memory_usage_mb']}MB")
        print(f"  CPU Usage: {perf['cpu_usage_percent']}%")
        print(f"  Disk Usage: {perf['disk_usage_percent']}%")
        print(f"  Cache Hit Rate: {perf['cache_hit_rate']}%")
        
        # Metrics summary
        if 'averages' in data['performance_summary']:
            avg = data['performance_summary']['averages']
            print(f"\nHourly Averages:")
            print(f"  Avg Response Time: {avg['response_time_ms']}ms")
            print(f"  Avg Memory Usage: {avg['memory_usage_mb']}MB")
            print(f"  Avg CPU Usage: {avg['cpu_usage_percent']}%")
        
        print("\n" + "="*60)