"""
Utility modules for the Personal Command-Line Vector Knowledge Base.

This package contains utility functions and classes for:
- File operations and management
- Text processing and cleaning
- Caching mechanisms
- Monitoring and statistics
- Cost calculation and tracking
"""

from .file_utils import (
    get_file_info,
    is_text_file,
    ensure_directory,
    safe_file_read,
    safe_file_write,
    get_file_hash,
    cleanup_temp_files
)

from .text_utils import (
    clean_text,
    extract_code_blocks,
    normalize_whitespace,
    truncate_text,
    calculate_text_similarity,
    detect_language
)

from .cache_utils import (
    EmbeddingCache,
    ResponseCache,
    CacheManager
)

from .monitor_utils import (
    HealthMonitor,
    PerformanceTracker,
    MetricsCollector
)

from .cost_utils import (
    CostCalculator,
    CostTracker,
    BudgetManager
)

__all__ = [
    # File utilities
    "get_file_info",
    "is_text_file", 
    "ensure_directory",
    "safe_file_read",
    "safe_file_write",
    "get_file_hash",
    "cleanup_temp_files",
    
    # Text utilities
    "clean_text",
    "extract_code_blocks",
    "normalize_whitespace", 
    "truncate_text",
    "calculate_text_similarity",
    "detect_language",
    
    # Cache utilities
    "EmbeddingCache",
    "ResponseCache",
    "CacheManager",
    
    # Monitor utilities
    "HealthMonitor",
    "PerformanceTracker",
    "MetricsCollector",
    
    # Cost utilities
    "CostCalculator",
    "CostTracker", 
    "BudgetManager",
]