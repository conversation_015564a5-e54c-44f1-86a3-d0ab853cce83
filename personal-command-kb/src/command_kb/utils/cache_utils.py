"""
Caching utilities for the Personal Command-Line Vector Knowledge Base.

This module provides caching mechanisms for:
- Embedding results caching
- API response caching  
- General-purpose caching with TTL
- Cache management and statistics
"""

import time
import hashlib
import logging
import pickle
from typing import Any, Dict, Optional, List, Tuple
from pathlib import Path
from threading import Lock
from dataclasses import dataclass
from abc import ABC, abstractmethod

try:
    import diskcache as dc
except ImportError:
    dc = None

logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    """Cache statistics container."""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    size: int = 0
    max_size: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class BaseCache(ABC):
    """Abstract base class for cache implementations."""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        pass


class MemoryCache(BaseCache):
    """In-memory cache with TTL support."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        """Initialize memory cache.
        
        Args:
            max_size: Maximum number of items to cache
            default_ttl: Default TTL in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._lock = Lock()
        self._stats = CacheStats(max_size=max_size)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                self._stats.misses += 1
                return None
            
            value, expires_at = self._cache[key]
            
            # Check if expired
            if expires_at > 0 and time.time() > expires_at:
                del self._cache[key]
                self._stats.misses += 1
                self._stats.size = len(self._cache)
                return None
            
            self._stats.hits += 1
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        with self._lock:
            ttl = ttl or self.default_ttl
            expires_at = time.time() + ttl if ttl > 0 else 0
            
            # Remove oldest items if at capacity
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_oldest()
            
            self._cache[key] = (value, expires_at)
            self._stats.sets += 1
            self._stats.size = len(self._cache)
            return True
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats.deletes += 1
                self._stats.size = len(self._cache)
                return True
            return False
    
    def clear(self) -> bool:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            self._stats.size = 0
            return True
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        with self._lock:
            return CacheStats(
                hits=self._stats.hits,
                misses=self._stats.misses,
                sets=self._stats.sets,
                deletes=self._stats.deletes,
                size=self._stats.size,
                max_size=self._stats.max_size
            )
    
    def _evict_oldest(self) -> None:
        """Evict oldest cache entry."""
        if not self._cache:
            return
        
        # Find entry with earliest expiration (or oldest if no expiration)
        oldest_key = min(
            self._cache.keys(),
            key=lambda k: self._cache[k][1] if self._cache[k][1] > 0 else float('inf')
        )
        del self._cache[oldest_key]


class DiskCache(BaseCache):
    """Disk-based cache using diskcache library."""
    
    def __init__(self, cache_dir: str, max_size: int = 1024*1024*1024, default_ttl: int = 3600):
        """Initialize disk cache.
        
        Args:
            cache_dir: Directory for cache files
            max_size: Maximum cache size in bytes
            default_ttl: Default TTL in seconds
        """
        if dc is None:
            raise ImportError("diskcache library is required for DiskCache")
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.default_ttl = default_ttl
        
        self._cache = dc.Cache(str(self.cache_dir), size_limit=max_size)
        self._stats = CacheStats(max_size=max_size)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            value = self._cache.get(key)
            if value is not None:
                self._stats.hits += 1
                return value
            else:
                self._stats.misses += 1
                return None
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            self._stats.misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            ttl = ttl or self.default_ttl
            expire_time = time.time() + ttl if ttl > 0 else None
            
            self._cache.set(key, value, expire=expire_time)
            self._stats.sets += 1
            self._stats.size = len(self._cache)
            return True
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            result = self._cache.delete(key)
            if result:
                self._stats.deletes += 1
                self._stats.size = len(self._cache)
            return result
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            self._cache.clear()
            self._stats.size = 0
            return True
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        try:
            self._stats.size = len(self._cache)
        except Exception:
            pass
        
        return CacheStats(
            hits=self._stats.hits,
            misses=self._stats.misses,
            sets=self._stats.sets,
            deletes=self._stats.deletes,
            size=self._stats.size,
            max_size=self._stats.max_size
        )


class EmbeddingCache:
    """Specialized cache for embedding results."""
    
    def __init__(self, ttl: int = 86400, max_size: int = 10000, use_disk: bool = False, cache_dir: Optional[str] = None):
        """Initialize embedding cache.
        
        Args:
            ttl: Time to live in seconds (default: 24 hours)
            max_size: Maximum number of cached embeddings
            use_disk: Whether to use disk-based caching
            cache_dir: Directory for disk cache
        """
        self.ttl = ttl
        
        if use_disk and cache_dir:
            try:
                self.cache = DiskCache(cache_dir, max_size * 1024, ttl)
            except ImportError:
                logger.warning("Disk cache not available, falling back to memory cache")
                self.cache = MemoryCache(max_size, ttl)
        else:
            self.cache = MemoryCache(max_size, ttl)
    
    def get(self, text: str) -> Optional[List[float]]:
        """Get embedding for text.
        
        Args:
            text: Text to get embedding for
            
        Returns:
            Embedding vector or None if not cached
        """
        key = self._generate_key(text)
        return self.cache.get(key)
    
    def set(self, text: str, embedding: List[float]) -> bool:
        """Cache embedding for text.
        
        Args:
            text: Text that was embedded
            embedding: Embedding vector
            
        Returns:
            True if cached successfully
        """
        key = self._generate_key(text)
        return self.cache.set(key, embedding, self.ttl)
    
    def _generate_key(self, text: str) -> str:
        """Generate cache key for text."""
        return f"emb_{hashlib.md5(text.encode('utf-8')).hexdigest()}"
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        return self.cache.get_stats()
    
    def clear(self) -> bool:
        """Clear embedding cache."""
        return self.cache.clear()
class ResponseCache:
    """Specialized cache for API response results."""
    
    def __init__(self, ttl: int = 1800, max_size: int = 5000, use_disk: bool = False, cache_dir: Optional[str] = None):
        """Initialize response cache.
        
        Args:
            ttl: Time to live in seconds (default: 30 minutes)
            max_size: Maximum number of cached responses
            use_disk: Whether to use disk-based caching
            cache_dir: Directory for disk cache
        """
        self.ttl = ttl
        
        if use_disk and cache_dir:
            try:
                self.cache = DiskCache(cache_dir, max_size * 1024, ttl)
            except ImportError:
                logger.warning("Disk cache not available, falling back to memory cache")
                self.cache = MemoryCache(max_size, ttl)
        else:
            self.cache = MemoryCache(max_size, ttl)
    
    def get(self, query: str, context_hash: str = "") -> Optional[str]:
        """Get cached response for query and context.
        
        Args:
            query: User query
            context_hash: Hash of context documents
            
        Returns:
            Cached response or None if not cached
        """
        key = self._generate_key(query, context_hash)
        return self.cache.get(key)
    
    def set(self, query: str, response: str, context_hash: str = "") -> bool:
        """Cache response for query and context.
        
        Args:
            query: User query
            response: Generated response
            context_hash: Hash of context documents
            
        Returns:
            True if cached successfully
        """
        key = self._generate_key(query, context_hash)
        return self.cache.set(key, response, self.ttl)
    
    def _generate_key(self, query: str, context_hash: str = "") -> str:
        """Generate cache key for query and context."""
        combined = f"{query}|{context_hash}"
        return f"resp_{hashlib.md5(combined.encode('utf-8')).hexdigest()}"
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        return self.cache.get_stats()
    
    def clear(self) -> bool:
        """Clear response cache."""
        return self.cache.clear()


class CacheManager:
    """Manager for multiple cache instances."""
    
    def __init__(self, cache_dir: Optional[str] = None):
        """Initialize cache manager.
        
        Args:
            cache_dir: Base directory for disk caches
        """
        self.cache_dir = Path(cache_dir) if cache_dir else None
        self.caches: Dict[str, BaseCache] = {}
        
        if self.cache_dir:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def create_cache(self, name: str, cache_type: str = "memory", **kwargs) -> BaseCache:
        """Create and register a new cache.
        
        Args:
            name: Cache name
            cache_type: Type of cache ("memory" or "disk")
            **kwargs: Cache-specific arguments
            
        Returns:
            Created cache instance
        """
        if cache_type == "memory":
            cache = MemoryCache(**kwargs)
        elif cache_type == "disk":
            if not self.cache_dir:
                raise ValueError("Cache directory required for disk cache")
            cache_path = self.cache_dir / name
            cache = DiskCache(str(cache_path), **kwargs)
        else:
            raise ValueError(f"Unknown cache type: {cache_type}")
        
        self.caches[name] = cache
        logger.info(f"Created {cache_type} cache: {name}")
        return cache
    
    def get_cache(self, name: str) -> Optional[BaseCache]:
        """Get cache by name.
        
        Args:
            name: Cache name
            
        Returns:
            Cache instance or None if not found
        """
        return self.caches.get(name)
    
    def clear_all(self) -> Dict[str, bool]:
        """Clear all managed caches.
        
        Returns:
            Dictionary of cache names and clear results
        """
        results = {}
        for name, cache in self.caches.items():
            results[name] = cache.clear()
        return results
    
    def get_all_stats(self) -> Dict[str, CacheStats]:
        """Get statistics for all managed caches.
        
        Returns:
            Dictionary of cache names and their statistics
        """
        stats = {}
        for name, cache in self.caches.items():
            stats[name] = cache.get_stats()
        return stats
    
    def cleanup_expired(self) -> Dict[str, int]:
        """Clean up expired entries from all caches.
        
        Returns:
            Dictionary of cache names and number of cleaned entries
        """
        results = {}
        
        for name, cache in self.caches.items():
            if isinstance(cache, MemoryCache):
                # For memory cache, trigger cleanup by accessing all keys
                cleaned = 0
                with cache._lock:
                    current_time = time.time()
                    expired_keys = []
                    
                    for key, (value, expires_at) in cache._cache.items():
                        if expires_at > 0 and current_time > expires_at:
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        del cache._cache[key]
                        cleaned += 1
                    
                    cache._stats.size = len(cache._cache)
                
                results[name] = cleaned
            else:
                # Disk cache handles expiration automatically
                results[name] = 0
        
        return results


def create_embedding_cache(config: Dict[str, Any]) -> EmbeddingCache:
    """Factory function to create embedding cache from configuration.
    
    Args:
        config: Cache configuration dictionary
        
    Returns:
        Configured EmbeddingCache instance
    """
    return EmbeddingCache(
        ttl=config.get('ttl', 86400),
        max_size=config.get('max_size', 10000),
        use_disk=config.get('use_disk', False),
        cache_dir=config.get('cache_dir')
    )


def create_response_cache(config: Dict[str, Any]) -> ResponseCache:
    """Factory function to create response cache from configuration.
    
    Args:
        config: Cache configuration dictionary
        
    Returns:
        Configured ResponseCache instance
    """
    return ResponseCache(
        ttl=config.get('ttl', 1800),
        max_size=config.get('max_size', 5000),
        use_disk=config.get('use_disk', False),
        cache_dir=config.get('cache_dir')
    )


class CacheWarmer:
    """Utility for pre-warming caches with common data."""
    
    def __init__(self, cache_manager: CacheManager):
        """Initialize cache warmer.
        
        Args:
            cache_manager: Cache manager instance
        """
        self.cache_manager = cache_manager
    
    def warm_embedding_cache(self, texts: List[str], embeddings: List[List[float]]) -> int:
        """Pre-warm embedding cache with common texts.
        
        Args:
            texts: List of texts
            embeddings: Corresponding embeddings
            
        Returns:
            Number of items cached
        """
        embedding_cache = self.cache_manager.get_cache('embeddings')
        if not isinstance(embedding_cache, EmbeddingCache):
            logger.warning("Embedding cache not found or wrong type")
            return 0
        
        cached_count = 0
        for text, embedding in zip(texts, embeddings):
            if embedding_cache.set(text, embedding):
                cached_count += 1
        
        logger.info(f"Pre-warmed embedding cache with {cached_count} items")
        return cached_count
    
    def warm_response_cache(self, query_response_pairs: List[Tuple[str, str]]) -> int:
        """Pre-warm response cache with common query-response pairs.
        
        Args:
            query_response_pairs: List of (query, response) tuples
            
        Returns:
            Number of items cached
        """
        response_cache = self.cache_manager.get_cache('responses')
        if not isinstance(response_cache, ResponseCache):
            logger.warning("Response cache not found or wrong type")
            return 0
        
        cached_count = 0
        for query, response in query_response_pairs:
            if response_cache.set(query, response):
                cached_count += 1
        
        logger.info(f"Pre-warmed response cache with {cached_count} items")
        return cached_count