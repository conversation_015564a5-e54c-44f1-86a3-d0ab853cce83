"""
File utilities for the Personal Command-Line Vector Knowledge Base.

This module provides file operations and management utilities:
- File information and metadata extraction
- Safe file reading and writing operations
- Directory management and cleanup
- File type detection and validation
"""

import os
import hashlib
import logging
import mimetypes
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from ..constants import (
    MAX_DOCUMENT_SIZE,
    DEFAULT_ENCODING,
    SUPPORTED_EXTENSIONS
)

logger = logging.getLogger(__name__)


def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
    """Get comprehensive file information.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary with file information
    """
    file_path = Path(file_path)
    
    try:
        stat = file_path.stat()
        
        info = {
            'path': str(file_path),
            'name': file_path.name,
            'stem': file_path.stem,
            'suffix': file_path.suffix,
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'created_time': datetime.fromtimestamp(stat.st_ctime),
            'modified_time': datetime.fromtimestamp(stat.st_mtime),
            'accessed_time': datetime.fromtimestamp(stat.st_atime),
            'is_file': file_path.is_file(),
            'is_directory': file_path.is_dir(),
            'is_symlink': file_path.is_symlink(),
            'exists': file_path.exists(),
        }
        
        # Add MIME type if it's a file
        if file_path.is_file():
            mime_type, encoding = mimetypes.guess_type(str(file_path))
            info['mime_type'] = mime_type
            info['encoding'] = encoding
            info['is_text'] = is_text_file(file_path)
            info['is_supported'] = file_path.suffix.lower() in SUPPORTED_EXTENSIONS
        
        return info
        
    except Exception as e:
        logger.error(f"Failed to get file info for {file_path}: {e}")
        return {
            'path': str(file_path),
            'error': str(e),
            'exists': False
        }


def is_text_file(file_path: Union[str, Path]) -> bool:
    """Check if file is a text file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if file is text, False otherwise
    """
    file_path = Path(file_path)
    
    if not file_path.is_file():
        return False
    
    # Check by extension first
    text_extensions = {
        '.txt', '.md', '.py', '.js', '.ts', '.html', '.css', '.json',
        '.yaml', '.yml', '.xml', '.csv', '.sh', '.bash', '.zsh',
        '.sql', '.log', '.conf', '.cfg', '.ini', '.toml'
    }
    
    if file_path.suffix.lower() in text_extensions:
        return True
    
    # Check MIME type
    mime_type, _ = mimetypes.guess_type(str(file_path))
    if mime_type and mime_type.startswith('text/'):
        return True
    
    # Check file content (sample first 1024 bytes)
    try:
        with open(file_path, 'rb') as f:
            sample = f.read(1024)
            
        # Check for null bytes (binary indicator)
        if b'\x00' in sample:
            return False
            
        # Try to decode as text
        try:
            sample.decode('utf-8')
            return True
        except UnicodeDecodeError:
            try:
                sample.decode('latin-1')
                return True
            except UnicodeDecodeError:
                return False
                
    except Exception as e:
        logger.debug(f"Could not check file content for {file_path}: {e}")
        return False


def ensure_directory(dir_path: Union[str, Path], mode: int = 0o755) -> bool:
    """Ensure directory exists, create if necessary.
    
    Args:
        dir_path: Path to directory
        mode: Directory permissions (default: 0o755)
        
    Returns:
        True if directory exists or was created successfully
    """
    dir_path = Path(dir_path)
    
    try:
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True, mode=mode)
            logger.debug(f"Created directory: {dir_path}")
        elif not dir_path.is_dir():
            logger.error(f"Path exists but is not a directory: {dir_path}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"Failed to create directory {dir_path}: {e}")
        return False


def safe_file_read(file_path: Union[str, Path], 
                   encoding: str = DEFAULT_ENCODING,
                   max_size: int = MAX_DOCUMENT_SIZE) -> Optional[str]:
    """Safely read file content with size and encoding checks.
    
    Args:
        file_path: Path to file
        encoding: Text encoding to use
        max_size: Maximum file size to read
        
    Returns:
        File content as string, or None if failed
    """
    file_path = Path(file_path)
    
    try:
        # Check if file exists and is readable
        if not file_path.is_file():
            logger.error(f"File does not exist or is not a file: {file_path}")
            return None
        
        # Check file size
        file_size = file_path.stat().st_size
        if file_size > max_size:
            logger.warning(f"File too large ({file_size} bytes): {file_path}")
            return None
        
        # Read file content
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
        
        logger.debug(f"Successfully read file: {file_path} ({len(content)} chars)")
        return content
        
    except UnicodeDecodeError as e:
        logger.error(f"Encoding error reading {file_path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to read file {file_path}: {e}")
        return None


def safe_file_write(file_path: Union[str, Path], 
                    content: str,
                    encoding: str = DEFAULT_ENCODING,
                    backup: bool = True) -> bool:
    """Safely write content to file with backup option.
    
    Args:
        file_path: Path to file
        content: Content to write
        encoding: Text encoding to use
        backup: Whether to create backup of existing file
        
    Returns:
        True if successful, False otherwise
    """
    file_path = Path(file_path)
    
    try:
        # Ensure parent directory exists
        if not ensure_directory(file_path.parent):
            return False
        
        # Create backup if file exists and backup is requested
        if backup and file_path.exists():
            backup_path = file_path.with_suffix(file_path.suffix + '.bak')
            try:
                import shutil
                shutil.copy2(file_path, backup_path)
                logger.debug(f"Created backup: {backup_path}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")
        
        # Write content to file
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        
        logger.debug(f"Successfully wrote file: {file_path} ({len(content)} chars)")
        return True
        
    except Exception as e:
        logger.error(f"Failed to write file {file_path}: {e}")
        return False


def get_file_hash(file_path: Union[str, Path], 
                  algorithm: str = 'md5') -> Optional[str]:
    """Calculate hash of file content.
    
    Args:
        file_path: Path to file
        algorithm: Hash algorithm ('md5', 'sha1', 'sha256')
        
    Returns:
        Hex digest of file hash, or None if failed
    """
    file_path = Path(file_path)
    
    if not file_path.is_file():
        logger.error(f"File does not exist: {file_path}")
        return None
    
    try:
        # Get hash function
        if algorithm == 'md5':
            hasher = hashlib.md5()
        elif algorithm == 'sha1':
            hasher = hashlib.sha1()
        elif algorithm == 'sha256':
            hasher = hashlib.sha256()
        else:
            logger.error(f"Unsupported hash algorithm: {algorithm}")
            return None
        
        # Read file in chunks to handle large files
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hasher.update(chunk)
        
        return hasher.hexdigest()
        
    except Exception as e:
        logger.error(f"Failed to calculate hash for {file_path}: {e}")
        return None


def cleanup_temp_files(temp_dir: Union[str, Path], 
                       max_age_hours: int = 24,
                       pattern: str = "*.tmp") -> int:
    """Clean up temporary files older than specified age.
    
    Args:
        temp_dir: Directory containing temporary files
        max_age_hours: Maximum age in hours before cleanup
        pattern: File pattern to match (glob pattern)
        
    Returns:
        Number of files cleaned up
    """
    temp_dir = Path(temp_dir)
    
    if not temp_dir.is_dir():
        logger.warning(f"Temp directory does not exist: {temp_dir}")
        return 0
    
    try:
        import time
        from datetime import timedelta
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        # Find matching files
        for file_path in temp_dir.glob(pattern):
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                
                if file_age > max_age_seconds:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.debug(f"Cleaned up temp file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Failed to delete temp file {file_path}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} temporary files from {temp_dir}")
        
        return cleaned_count
        
    except Exception as e:
        logger.error(f"Failed to cleanup temp files in {temp_dir}: {e}")
        return 0


def find_files(directory: Union[str, Path], 
               pattern: str = "*",
               recursive: bool = True,
               include_dirs: bool = False) -> List[Path]:
    """Find files matching pattern in directory.
    
    Args:
        directory: Directory to search
        pattern: File pattern to match (glob pattern)
        recursive: Whether to search recursively
        include_dirs: Whether to include directories in results
        
    Returns:
        List of matching file paths
    """
    directory = Path(directory)
    
    if not directory.is_dir():
        logger.error(f"Directory does not exist: {directory}")
        return []
    
    try:
        if recursive:
            matches = directory.rglob(pattern)
        else:
            matches = directory.glob(pattern)
        
        results = []
        for path in matches:
            if include_dirs or path.is_file():
                results.append(path)
        
        logger.debug(f"Found {len(results)} files matching '{pattern}' in {directory}")
        return results
        
    except Exception as e:
        logger.error(f"Failed to find files in {directory}: {e}")
        return []


def get_directory_size(directory: Union[str, Path]) -> Dict[str, Any]:
    """Calculate total size of directory and its contents.
    
    Args:
        directory: Directory to analyze
        
    Returns:
        Dictionary with size information
    """
    directory = Path(directory)
    
    if not directory.is_dir():
        logger.error(f"Directory does not exist: {directory}")
        return {'error': 'Directory not found'}
    
    try:
        total_size = 0
        file_count = 0
        dir_count = 0
        
        for path in directory.rglob('*'):
            if path.is_file():
                total_size += path.stat().st_size
                file_count += 1
            elif path.is_dir():
                dir_count += 1
        
        return {
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'total_size_gb': round(total_size / (1024 * 1024 * 1024), 2),
            'file_count': file_count,
            'directory_count': dir_count,
            'path': str(directory)
        }
        
    except Exception as e:
        logger.error(f"Failed to calculate directory size for {directory}: {e}")
        return {'error': str(e)}


def create_file_index(directory: Union[str, Path], 
                      output_file: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """Create an index of all files in directory with metadata.
    
    Args:
        directory: Directory to index
        output_file: Optional file to save index to
        
    Returns:
        Dictionary with file index
    """
    directory = Path(directory)
    
    if not directory.is_dir():
        logger.error(f"Directory does not exist: {directory}")
        return {'error': 'Directory not found'}
    
    try:
        index = {
            'directory': str(directory),
            'created_at': datetime.now().isoformat(),
            'files': []
        }
        
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                file_info = get_file_info(file_path)
                # Make paths relative to indexed directory
                file_info['relative_path'] = str(file_path.relative_to(directory))
                index['files'].append(file_info)
        
        index['total_files'] = len(index['files'])
        
        # Save to file if requested
        if output_file:
            import json
            output_path = Path(output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(index, f, indent=2, default=str)
            logger.info(f"File index saved to: {output_path}")
        
        logger.info(f"Created index of {index['total_files']} files in {directory}")
        return index
        
    except Exception as e:
        logger.error(f"Failed to create file index for {directory}: {e}")
        return {'error': str(e)}