"""
Text processing utilities for the Personal Command-Line Vector Knowledge Base.

This module provides text processing and analysis utilities:
- Text cleaning and normalization
- Code block extraction and processing
- Language detection and analysis
- Text similarity calculations
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)


def clean_text(text: str, 
               remove_extra_whitespace: bool = True,
               remove_special_chars: bool = False,
               preserve_newlines: bool = True) -> str:
    """Clean and normalize text content.
    
    Args:
        text: Text to clean
        remove_extra_whitespace: Whether to remove extra whitespace
        remove_special_chars: Whether to remove special characters
        preserve_newlines: Whether to preserve newline characters
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    cleaned = text
    
    # Remove or normalize whitespace
    if remove_extra_whitespace:
        if preserve_newlines:
            # Replace multiple spaces/tabs with single space, preserve newlines
            cleaned = re.sub(r'[ \t]+', ' ', cleaned)
            cleaned = re.sub(r'\n\s*\n', '\n\n', cleaned)  # Normalize paragraph breaks
        else:
            # Replace all whitespace with single spaces
            cleaned = re.sub(r'\s+', ' ', cleaned)
    
    # Remove special characters if requested
    if remove_special_chars:
        # Keep alphanumeric, basic punctuation, and whitespace
        cleaned = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\]', '', cleaned)
    
    # Strip leading/trailing whitespace
    cleaned = cleaned.strip()
    
    return cleaned


def normalize_whitespace(text: str) -> str:
    """Normalize whitespace in text.
    
    Args:
        text: Text to normalize
        
    Returns:
        Text with normalized whitespace
    """
    if not text:
        return ""
    
    # Replace tabs with spaces
    text = text.replace('\t', '    ')
    
    # Normalize line endings
    text = text.replace('\r\n', '\n').replace('\r', '\n')
    
    # Remove trailing whitespace from lines
    lines = text.split('\n')
    lines = [line.rstrip() for line in lines]
    
    # Remove excessive blank lines (more than 2 consecutive)
    normalized_lines = []
    blank_count = 0
    
    for line in lines:
        if line.strip() == '':
            blank_count += 1
            if blank_count <= 2:
                normalized_lines.append(line)
        else:
            blank_count = 0
            normalized_lines.append(line)
    
    return '\n'.join(normalized_lines)


def extract_code_blocks(text: str) -> List[Dict[str, Any]]:
    """Extract code blocks from Markdown text.
    
    Args:
        text: Markdown text containing code blocks
        
    Returns:
        List of dictionaries with code block information
    """
    code_blocks = []
    
    # Pattern for fenced code blocks
    fenced_pattern = r'```(\w*)\n(.*?)\n```'
    
    for match in re.finditer(fenced_pattern, text, re.DOTALL):
        language = match.group(1) or 'text'
        code = match.group(2)
        start_pos = match.start()
        end_pos = match.end()
        
        code_blocks.append({
            'language': language,
            'code': code,
            'start_position': start_pos,
            'end_position': end_pos,
            'line_count': len(code.split('\n')),
            'char_count': len(code)
        })
    
    # Pattern for indented code blocks (4+ spaces)
    indented_pattern = r'^(?: {4,}|\t+)(.+)$'
    
    lines = text.split('\n')
    current_block = []
    block_start_line = -1
    
    for i, line in enumerate(lines):
        if re.match(indented_pattern, line):
            if not current_block:
                block_start_line = i
            # Remove leading indentation
            dedented = re.sub(r'^(?: {4}|\t)', '', line)
            current_block.append(dedented)
        else:
            if current_block:
                # End of indented block
                code_blocks.append({
                    'language': 'text',
                    'code': '\n'.join(current_block),
                    'start_line': block_start_line,
                    'end_line': i - 1,
                    'line_count': len(current_block),
                    'char_count': sum(len(line) for line in current_block)
                })
                current_block = []
                block_start_line = -1
    
    # Handle final indented block
    if current_block:
        code_blocks.append({
            'language': 'text',
            'code': '\n'.join(current_block),
            'start_line': block_start_line,
            'end_line': len(lines) - 1,
            'line_count': len(current_block),
            'char_count': sum(len(line) for line in current_block)
        })
    
    return code_blocks


def truncate_text(text: str, 
                  max_length: int = 1000,
                  truncate_at_word: bool = True,
                  suffix: str = "...") -> str:
    """Truncate text to specified length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length of result
        truncate_at_word: Whether to truncate at word boundaries
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    if truncate_at_word:
        # Find last word boundary before max_length
        truncate_pos = max_length - len(suffix)
        
        # Look for word boundary
        for i in range(truncate_pos, max(0, truncate_pos - 50), -1):
            if text[i].isspace():
                return text[:i].rstrip() + suffix
        
        # If no word boundary found, truncate at character
        return text[:truncate_pos].rstrip() + suffix
    else:
        # Simple character truncation
        return text[:max_length - len(suffix)] + suffix


def calculate_text_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts.
    
    Args:
        text1: First text
        text2: Second text
        
    Returns:
        Similarity score between 0.0 and 1.0
    """
    if not text1 or not text2:
        return 0.0
    
    # Use SequenceMatcher for basic similarity
    matcher = SequenceMatcher(None, text1.lower(), text2.lower())
    return matcher.ratio()


def detect_language(text: str) -> str:
    """Detect programming language from code text.
    
    Args:
        text: Code text to analyze
        
    Returns:
        Detected language name
    """
    if not text:
        return 'text'
    
    # Language detection patterns
    patterns = {
        'python': [
            r'def\s+\w+\s*\(',
            r'import\s+\w+',
            r'from\s+\w+\s+import',
            r'if\s+__name__\s*==\s*["\']__main__["\']',
            r'print\s*\(',
        ],
        'javascript': [
            r'function\s+\w+\s*\(',
            r'var\s+\w+\s*=',
            r'let\s+\w+\s*=',
            r'const\s+\w+\s*=',
            r'console\.log\s*\(',
        ],
        'bash': [
            r'#!/bin/bash',
            r'#!/bin/sh',
            r'\$\w+',
            r'echo\s+',
            r'if\s*\[\s*.*\s*\]',
        ],
        'sql': [
            r'SELECT\s+.*\s+FROM',
            r'INSERT\s+INTO',
            r'UPDATE\s+.*\s+SET',
            r'DELETE\s+FROM',
            r'CREATE\s+TABLE',
        ],
        'java': [
            r'public\s+class\s+\w+',
            r'public\s+static\s+void\s+main',
            r'import\s+java\.',
            r'System\.out\.println',
        ],
        'cpp': [
            r'#include\s*<.*>',
            r'int\s+main\s*\(',
            r'std::',
            r'cout\s*<<',
        ],
        'html': [
            r'<html.*?>',
            r'<head.*?>',
            r'<body.*?>',
            r'<div.*?>',
        ],
        'css': [
            r'\w+\s*\{[^}]*\}',
            r'@media\s*\(',
            r'#\w+\s*\{',
            r'\.\w+\s*\{',
        ],
        'json': [
            r'^\s*\{.*\}\s*$',
            r'"\w+"\s*:\s*',
        ],
        'yaml': [
            r'^\w+:\s*$',
            r'^\s*-\s+\w+',
        ],
    }
    
    # Count matches for each language
    scores = {}
    text_lower = text.lower()
    
    for language, lang_patterns in patterns.items():
        score = 0
        for pattern in lang_patterns:
            matches = len(re.findall(pattern, text_lower, re.MULTILINE))
            score += matches
        scores[language] = score
    
    # Return language with highest score
    if scores:
        best_language = max(scores, key=scores.get)
        if scores[best_language] > 0:
            return best_language
    
    return 'text'


def extract_commands_from_text(text: str) -> List[Dict[str, Any]]:
    """Extract command-line commands from text.
    
    Args:
        text: Text to extract commands from
        
    Returns:
        List of extracted commands with metadata
    """
    commands = []
    
    # Command patterns
    patterns = [
        (r'^\$\s+(.+)$', 'shell'),           # $ command
        (r'^>\s+(.+)$', 'powershell'),       # > command  
        (r'^#\s+(.+)$', 'comment'),          # # command
        (r'^sudo\s+(.+)$', 'sudo'),          # sudo command
        (r'^docker\s+(.+)$', 'docker'),      # docker command
        (r'^git\s+(.+)$', 'git'),            # git command
        (r'^npm\s+(.+)$', 'npm'),            # npm command
        (r'^pip\s+(.+)$', 'pip'),            # pip command
        (r'^curl\s+(.+)$', 'curl'),          # curl command
    ]
    
    lines = text.split('\n')
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
            
        for pattern, cmd_type in patterns:
            match = re.match(pattern, line, re.IGNORECASE)
            if match:
                command = match.group(1).strip()
                
                # Extract description from surrounding context
                description = _get_command_description(lines, line_num - 1)
                
                commands.append({
                    'command': command,
                    'type': cmd_type,
                    'line_number': line_num,
                    'original_line': line,
                    'description': description,
                    'tags': _extract_command_tags(command)
                })
                break
    
    return commands


def _get_command_description(lines: List[str], command_line_index: int) -> str:
    """Get description for a command from surrounding context."""
    description_parts = []
    
    # Look for comment on same line
    line = lines[command_line_index]
    if '#' in line:
        comment_part = line.split('#', 1)[1].strip()
        if comment_part and not comment_part.startswith(' '):
            description_parts.append(comment_part)
    
    # Look for preceding comment lines
    for i in range(command_line_index - 1, max(-1, command_line_index - 3), -1):
        if i < 0:
            break
        prev_line = lines[i].strip()
        if not prev_line:
            break
        if prev_line.startswith('#'):
            comment = prev_line[1:].strip()
            if comment:
                description_parts.insert(0, comment)
        else:
            break
    
    return ' '.join(description_parts)


def _extract_command_tags(command: str) -> List[str]:
    """Extract tags from command for categorization."""
    tags = []
    command_lower = command.lower()
    
    # Common command categories
    tag_patterns = {
        'docker': ['docker'],
        'git': ['git'],
        'npm': ['npm', 'yarn', 'pnpm'],
        'python': ['pip', 'python', 'python3', 'pipenv', 'poetry'],
        'system': ['sudo', 'systemctl', 'service', 'ps', 'kill'],
        'network': ['curl', 'wget', 'ssh', 'scp', 'ping', 'netstat'],
        'file': ['ls', 'cp', 'mv', 'rm', 'find', 'grep', 'cat', 'less'],
        'database': ['mysql', 'postgres', 'mongo', 'redis'],
        'kubernetes': ['kubectl', 'helm', 'k9s'],
        'aws': ['aws', 'terraform', 'ansible'],
    }
    
    for tag, keywords in tag_patterns.items():
        if any(keyword in command_lower for keyword in keywords):
            tags.append(tag)
    
    return tags


def highlight_code_syntax(code: str, language: str = 'text') -> str:
    """Add basic syntax highlighting markers to code.
    
    Args:
        code: Code to highlight
        language: Programming language
        
    Returns:
        Code with highlighting markers
    """
    if not code or language == 'text':
        return code
    
    # Basic highlighting patterns
    if language in ['python', 'py']:
        # Highlight Python keywords
        keywords = ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except', 'import', 'from']
        for keyword in keywords:
            pattern = r'\b' + keyword + r'\b'
            code = re.sub(pattern, f'**{keyword}**', code)
    
    elif language in ['bash', 'sh', 'shell']:
        # Highlight shell commands
        code = re.sub(r'^\$\s*', '**$** ', code, flags=re.MULTILINE)
        code = re.sub(r'\b(echo|ls|cd|mkdir|rm|cp|mv|grep|find|awk|sed)\b', r'**\1**', code)
    
    elif language in ['javascript', 'js']:
        # Highlight JavaScript keywords
        keywords = ['function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'return']
        for keyword in keywords:
            pattern = r'\b' + keyword + r'\b'
            code = re.sub(pattern, f'**{keyword}**', code)
    
    return code


def extract_urls(text: str) -> List[str]:
    """Extract URLs from text.
    
    Args:
        text: Text to extract URLs from
        
    Returns:
        List of found URLs
    """
    url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
    urls = re.findall(url_pattern, text)
    return list(set(urls))  # Remove duplicates


def extract_email_addresses(text: str) -> List[str]:
    """Extract email addresses from text.
    
    Args:
        text: Text to extract emails from
        
    Returns:
        List of found email addresses
    """
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    return list(set(emails))  # Remove duplicates


def count_words(text: str) -> Dict[str, int]:
    """Count words in text and return statistics.
    
    Args:
        text: Text to analyze
        
    Returns:
        Dictionary with word count statistics
    """
    if not text:
        return {
            'total_words': 0,
            'unique_words': 0,
            'characters': 0,
            'lines': 0,
            'paragraphs': 0
        }
    
    # Basic counts
    words = re.findall(r'\b\w+\b', text.lower())
    unique_words = set(words)
    lines = text.split('\n')
    paragraphs = [p for p in text.split('\n\n') if p.strip()]
    
    return {
        'total_words': len(words),
        'unique_words': len(unique_words),
        'characters': len(text),
        'characters_no_spaces': len(text.replace(' ', '')),
        'lines': len(lines),
        'paragraphs': len(paragraphs),
        'avg_words_per_line': len(words) / max(1, len(lines)),
        'avg_chars_per_word': len(text.replace(' ', '')) / max(1, len(words))
    }


def create_text_summary(text: str, max_sentences: int = 3) -> str:
    """Create a simple extractive summary of text.
    
    Args:
        text: Text to summarize
        max_sentences: Maximum number of sentences in summary
        
    Returns:
        Text summary
    """
    if not text:
        return ""
    
    # Split into sentences
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]
    
    if len(sentences) <= max_sentences:
        return text
    
    # Simple scoring: prefer sentences with more words and common terms
    scored_sentences = []
    
    for i, sentence in enumerate(sentences):
        words = re.findall(r'\b\w+\b', sentence.lower())
        
        # Score based on length and position
        score = len(words)
        if i < len(sentences) * 0.3:  # Boost early sentences
            score *= 1.2
        
        scored_sentences.append((score, sentence))
    
    # Sort by score and take top sentences
    scored_sentences.sort(key=lambda x: x[0], reverse=True)
    top_sentences = [sent for score, sent in scored_sentences[:max_sentences]]
    
    # Maintain original order
    summary_sentences = []
    for sentence in sentences:
        if sentence in top_sentences:
            summary_sentences.append(sentence)
            if len(summary_sentences) >= max_sentences:
                break
    
    return '. '.join(summary_sentences) + '.'