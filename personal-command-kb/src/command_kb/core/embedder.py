"""
API-based text embedder for RAG pipeline - Embed step.

This module handles text embedding using multiple API providers with:
- High availability through provider failover
- Intelligent caching to reduce API calls
- Batch processing for efficiency
- Cost optimization and monitoring
"""

import logging
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from langchain_core.documents import Document

from ..api.api_manager import APIManager
from ..api.base_client import APIResponse
from ..utils.cache_utils import EmbeddingCache
from ..config import AppConfig

logger = logging.getLogger(__name__)


@dataclass
class EmbeddingResult:
    """Result of embedding operation."""
    embeddings: List[List[float]]
    success: bool
    error: Optional[str] = None
    provider_used: str = ""
    tokens_used: int = 0
    cost: float = 0.0
    cached_count: int = 0
    api_calls: int = 0


class APIEmbedder:
    """API-based text embedder with caching and failover.
    
    Provides high-availability text embedding through:
    - Multiple API provider support
    - Intelligent caching to reduce costs
    - Batch processing optimization
    - Automatic failover and retry
    """
    
    def __init__(self, config: AppConfig, api_manager: APIManager):
        """Initialize API embedder.
        
        Args:
            config: Application configuration
            api_manager: API manager for provider access
        """
        self.config = config
        self.api_manager = api_manager
        
        # Initialize cache if enabled
        self.cache = None
        if config.cache.enabled and config.cache.enable_embedding_cache:
            self.cache = EmbeddingCache(
                ttl=config.cache.ttl,
                max_size=config.cache.max_size
            )
        
        # Statistics
        self.total_embeddings = 0
        self.total_api_calls = 0
        self.total_cache_hits = 0
        self.total_cost = 0.0
        
        logger.info("Initialized API embedder with caching enabled: %s", 
                   self.cache is not None)
    
    def embed_documents(self, documents: List[Document]) -> EmbeddingResult:
        """Embed multiple documents.
        
        Args:
            documents: List of documents to embed
            
        Returns:
            EmbeddingResult with embeddings and metadata
        """
        if not documents:
            return EmbeddingResult(
                embeddings=[],
                success=False,
                error="No documents provided"
            )
        
        # Extract texts from documents
        texts = [doc.page_content for doc in documents]
        
        return self.embed_texts(texts)
    
    def embed_texts(self, texts: List[str]) -> EmbeddingResult:
        """Embed multiple texts with caching and batch processing.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            EmbeddingResult with embeddings and metadata
        """
        if not texts:
            return EmbeddingResult(
                embeddings=[],
                success=False,
                error="No texts provided"
            )
        
        # Check cache for existing embeddings
        cached_embeddings, uncached_texts, cache_map = self._check_cache(texts)
        
        embeddings = [None] * len(texts)
        total_tokens = 0
        total_cost = 0.0
        api_calls = 0
        
        # Fill in cached embeddings
        for i, embedding in cached_embeddings.items():
            embeddings[i] = embedding
        
        # Process uncached texts
        if uncached_texts:
            # Process in batches for efficiency
            batch_size = self._get_optimal_batch_size()
            
            for i in range(0, len(uncached_texts), batch_size):
                batch = uncached_texts[i:i + batch_size]
                batch_indices = [cache_map[text] for text in batch]
                
                # Make API call
                response = self.api_manager.embed_texts(batch)
                api_calls += 1
                
                if response.success:
                    # Store embeddings
                    for j, embedding in enumerate(response.data):
                        original_index = batch_indices[j]
                        embeddings[original_index] = embedding
                        
                        # Cache the embedding
                        if self.cache:
                            self.cache.set(batch[j], embedding)
                    
                    total_tokens += response.tokens_used
                    total_cost += response.cost
                else:
                    logger.error(f"Embedding batch failed: {response.error}")
                    return EmbeddingResult(
                        embeddings=[],
                        success=False,
                        error=response.error,
                        provider_used=response.provider
                    )
        
        # Update statistics
        self.total_embeddings += len(texts)
        self.total_api_calls += api_calls
        self.total_cache_hits += len(cached_embeddings)
        self.total_cost += total_cost
        
        return EmbeddingResult(
            embeddings=embeddings,
            success=True,
            provider_used=getattr(response, 'provider', '') if 'response' in locals() else '',
            tokens_used=total_tokens,
            cost=total_cost,
            cached_count=len(cached_embeddings),
            api_calls=api_calls
        )    
    def embed_single_text(self, text: str) -> EmbeddingResult:
        """Embed a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            EmbeddingResult with single embedding
        """
        result = self.embed_texts([text])
        
        if result.success and result.embeddings:
            result.embeddings = result.embeddings[0]
        
        return result
    
    def _check_cache(self, texts: List[str]) -> Tuple[Dict[int, List[float]], List[str], Dict[str, int]]:
        """Check cache for existing embeddings.
        
        Args:
            texts: List of texts to check
            
        Returns:
            Tuple of (cached_embeddings_by_index, uncached_texts, text_to_index_map)
        """
        cached_embeddings = {}
        uncached_texts = []
        cache_map = {}
        
        if not self.cache:
            # No cache, all texts need embedding
            for i, text in enumerate(texts):
                uncached_texts.append(text)
                cache_map[text] = i
            return cached_embeddings, uncached_texts, cache_map
        
        for i, text in enumerate(texts):
            cached_embedding = self.cache.get(text)
            if cached_embedding is not None:
                cached_embeddings[i] = cached_embedding
            else:
                uncached_texts.append(text)
                cache_map[text] = i
        
        logger.debug(f"Cache hit rate: {len(cached_embeddings)}/{len(texts)} "
                    f"({len(cached_embeddings)/len(texts)*100:.1f}%)")
        
        return cached_embeddings, uncached_texts, cache_map
    
    def _get_optimal_batch_size(self) -> int:
        """Get optimal batch size for API calls.
        
        Returns:
            Optimal batch size
        """
        # Start with a reasonable default
        base_batch_size = 50
        
        # Adjust based on configuration
        if hasattr(self.config, 'embedding_batch_size'):
            return self.config.embedding_batch_size
        
        return base_batch_size
    
    def _generate_cache_key(self, text: str) -> str:
        """Generate cache key for text.
        
        Args:
            text: Text to generate key for
            
        Returns:
            Cache key string
        """
        # Use hash of text for consistent key generation
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def precompute_embeddings(self, documents: List[Document], 
                            show_progress: bool = True) -> EmbeddingResult:
        """Precompute embeddings for a large set of documents.
        
        Args:
            documents: List of documents to precompute embeddings for
            show_progress: Whether to show progress information
            
        Returns:
            EmbeddingResult with overall statistics
        """
        if not documents:
            return EmbeddingResult(embeddings=[], success=True)
        
        total_embeddings = []
        total_tokens = 0
        total_cost = 0.0
        total_api_calls = 0
        total_cached = 0
        
        # Process in batches to manage memory
        batch_size = 100
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            
            if show_progress:
                logger.info(f"Processing batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1}")
            
            result = self.embed_documents(batch)
            
            if not result.success:
                logger.error(f"Failed to embed batch {i//batch_size + 1}: {result.error}")
                return result
            
            total_embeddings.extend(result.embeddings)
            total_tokens += result.tokens_used
            total_cost += result.cost
            total_api_calls += result.api_calls
            total_cached += result.cached_count
        
        return EmbeddingResult(
            embeddings=total_embeddings,
            success=True,
            tokens_used=total_tokens,
            cost=total_cost,
            cached_count=total_cached,
            api_calls=total_api_calls
        )
    
    def get_embedding_stats(self) -> Dict[str, Any]:
        """Get embedding statistics.
        
        Returns:
            Dictionary with statistics
        """
        cache_hit_rate = 0.0
        if self.total_embeddings > 0:
            cache_hit_rate = self.total_cache_hits / self.total_embeddings
        
        stats = {
            'total_embeddings': self.total_embeddings,
            'total_api_calls': self.total_api_calls,
            'total_cache_hits': self.total_cache_hits,
            'cache_hit_rate': cache_hit_rate,
            'total_cost': self.total_cost,
            'avg_cost_per_embedding': (
                self.total_cost / max(1, self.total_embeddings - self.total_cache_hits)
            ),
        }
        
        # Add cache statistics if available
        if self.cache:
            cache_stats = self.cache.get_stats()
            stats['cache_stats'] = cache_stats
        
        return stats
    
    def clear_cache(self) -> None:
        """Clear the embedding cache."""
        if self.cache:
            self.cache.clear()
            logger.info("Embedding cache cleared")
    
    def reset_stats(self) -> None:
        """Reset embedding statistics."""
        self.total_embeddings = 0
        self.total_api_calls = 0
        self.total_cache_hits = 0
        self.total_cost = 0.0
        logger.info("Embedding statistics reset")


class BatchEmbedder:
    """Specialized embedder for large batch processing."""
    
    def __init__(self, embedder: APIEmbedder, batch_size: int = 100):
        """Initialize batch embedder.
        
        Args:
            embedder: Base embedder instance
            batch_size: Size of processing batches
        """
        self.embedder = embedder
        self.batch_size = batch_size
    
    def embed_large_dataset(self, documents: List[Document], 
                          checkpoint_interval: int = 1000) -> EmbeddingResult:
        """Embed a large dataset with checkpointing.
        
        Args:
            documents: List of documents to embed
            checkpoint_interval: Save progress every N documents
            
        Returns:
            EmbeddingResult with overall statistics
        """
        total_results = []
        checkpoint_count = 0
        
        for i in range(0, len(documents), self.batch_size):
            batch = documents[i:i + self.batch_size]
            
            # Process batch
            result = self.embedder.embed_documents(batch)
            
            if not result.success:
                logger.error(f"Batch processing failed at document {i}: {result.error}")
                return result
            
            total_results.append(result)
            
            # Checkpoint progress
            if (i + len(batch)) % checkpoint_interval == 0:
                checkpoint_count += 1
                logger.info(f"Checkpoint {checkpoint_count}: Processed {i + len(batch)} documents")
                
                # Could save intermediate results here if needed
        
        # Combine all results
        all_embeddings = []
        total_tokens = 0
        total_cost = 0.0
        total_api_calls = 0
        total_cached = 0
        
        for result in total_results:
            all_embeddings.extend(result.embeddings)
            total_tokens += result.tokens_used
            total_cost += result.cost
            total_api_calls += result.api_calls
            total_cached += result.cached_count
        
        return EmbeddingResult(
            embeddings=all_embeddings,
            success=True,
            tokens_used=total_tokens,
            cost=total_cost,
            cached_count=total_cached,
            api_calls=total_api_calls
        )