"""
API-based answer generator for RAG pipeline - Generate step.

This module handles answer generation using multiple API providers with:
- Context-aware prompt engineering
- Streaming response support
- Multi-provider failover
- Response caching and optimization
"""

import logging
from typing import List, Dict, Any, Optional, Iterator
from dataclasses import dataclass

from langchain_core.documents import Document

from ..api.api_manager import APIManager
from ..config import AppConfig
from ..utils.cache_utils import ResponseCache

logger = logging.getLogger(__name__)


@dataclass
class GenerationResult:
    """Result of text generation."""
    answer: str
    success: bool
    error: Optional[str] = None
    provider_used: str = ""
    tokens_used: int = 0
    cost: float = 0.0
    cached: bool = False
    context_used: int = 0


class APIGenerator:
    """API-based answer generator with caching and failover."""
    
    def __init__(self, config: AppConfig, api_manager: APIManager):
        """Initialize API generator.
        
        Args:
            config: Application configuration
            api_manager: API manager for provider access
        """
        self.config = config
        self.api_manager = api_manager
        
        # Initialize cache if enabled
        self.cache = None
        if config.cache.enabled and config.cache.enable_generation_cache:
            self.cache = ResponseCache(
                ttl=config.cache.ttl,
                max_size=config.cache.max_size
            )
        
        # Generation parameters
        self.max_tokens = config.generation.max_tokens
        self.temperature = config.generation.temperature
        self.system_prompt = config.generation.system_prompt
        
        # Statistics
        self.total_generations = 0
        self.total_cache_hits = 0
        self.total_cost = 0.0
        
        logger.info("Initialized API generator")
    
    def generate_answer(self, query: str, context_documents: List[Document],
                       max_tokens: Optional[int] = None,
                       temperature: Optional[float] = None) -> GenerationResult:
        """基于查询和上下文生成答案。
        
        args：
            查询：用户查询
            context_documents：检索上下文文档
            max_tokens：可选的最大令牌覆盖
            温度：可选温度覆盖
            
        返回：
            带有生成答案的生产力
        """
        max_tokens = max_tokens or self.max_tokens
        temperature = temperature or self.temperature
        
        # Check cache first
        cache_key = self._generate_cache_key(query, context_documents)
        if self.cache:
            cached_result = self.cache.get(cache_key)
            if cached_result:
                self.total_cache_hits += 1
                return GenerationResult(
                    answer=cached_result,
                    success=True,
                    cached=True,
                    context_used=len(context_documents)
                )
        
        # Build prompt with context
        prompt = self._build_prompt(query, context_documents)
        
        # Generate response
        response = self.api_manager.generate_text(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        if response.success:
            # Cache the result
            if self.cache:
                self.cache.set(cache_key, response.data)
            
            # Update statistics
            self.total_generations += 1
            self.total_cost += response.cost
            
            return GenerationResult(
                answer=response.data,
                success=True,
                provider_used=response.provider,
                tokens_used=response.tokens_used,
                cost=response.cost,
                context_used=len(context_documents)
            )
        else:
            return GenerationResult(
                answer="",
                success=False,
                error=response.error,
                provider_used=response.provider,
                context_used=len(context_documents)
            )
    
    def generate_streaming(self, query: str, context_documents: List[Document],
                          max_tokens: Optional[int] = None,
                          temperature: Optional[float] = None) -> Iterator[str]:
        """Generate answer with streaming response.
        
        Args:
            query: User query
            context_documents: Retrieved context documents
            max_tokens: Optional max tokens override
            temperature: Optional temperature override
            
        Yields:
            Chunks of generated text
        """
        max_tokens = max_tokens or self.max_tokens
        temperature = temperature or self.temperature
        
        # Build prompt with context
        prompt = self._build_prompt(query, context_documents)
        
        # Generate streaming response
        try:
            for chunk in self.api_manager.generate_streaming(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature
            ):
                yield chunk
        except Exception as e:
            logger.error(f"Streaming generation failed: {e}")
            yield f"Error: {str(e)}"
    
    def _build_prompt(self, query: str, context_documents: List[Document]) -> str:
        """Build prompt with context and query.
        
        Args:
            query: User query
            context_documents: Context documents
            
        Returns:
            Formatted prompt string
        """
        # Build context section
        context_parts = []
        for i, doc in enumerate(context_documents):
            # Add document with source information
            source = doc.metadata.get('source', 'Unknown')
            context_parts.append(f"[Document {i+1} from {source}]\n{doc.page_content}")
        
        context_text = "\n\n".join(context_parts)
        
        # Build complete prompt
        prompt = f"""{self.system_prompt}

Context Information:
{context_text}

User Question: {query}

Please provide a helpful and accurate answer based on the context information above. If the context doesn't contain enough information to answer the question, please say so clearly.

Answer:"""
        
        return prompt
    
    def _generate_cache_key(self, query: str, context_documents: List[Document]) -> str:
        """Generate cache key for query and context.
        
        Args:
            query: User query
            context_documents: Context documents
            
        Returns:
            Cache key string
        """
        import hashlib
        
        # Create hash from query and context
        content = query
        for doc in context_documents:
            content += doc.page_content[:200]  # Use first 200 chars
        
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """Get generation statistics.
        
        Returns:
            Dictionary with statistics
        """
        cache_hit_rate = 0.0
        if self.total_generations + self.total_cache_hits > 0:
            cache_hit_rate = self.total_cache_hits / (self.total_generations + self.total_cache_hits)
        
        stats = {
            'total_generations': self.total_generations,
            'total_cache_hits': self.total_cache_hits,
            'cache_hit_rate': cache_hit_rate,
            'total_cost': self.total_cost,
            'avg_cost_per_generation': self.total_cost / max(1, self.total_generations),
        }
        
        if self.cache:
            stats['cache_stats'] = self.cache.get_stats()
        
        return stats
    
    def clear_cache(self) -> None:
        """Clear the generation cache."""
        if self.cache:
            self.cache.clear()
            logger.info("Generation cache cleared")
    
    def reset_stats(self) -> None:
        """Reset generation statistics."""
        self.total_generations = 0
        self.total_cache_hits = 0
        self.total_cost = 0.0
        logger.info("Generation statistics reset")


class PromptTemplate:
    """Template for building prompts."""
    
    def __init__(self, template: str):
        """Initialize prompt template.
        
        Args:
            template: Template string with placeholders
        """
        self.template = template
    
    def format(self, **kwargs) -> str:
        """Format template with provided values.
        
        Args:
            **kwargs: Values to substitute in template
            
        Returns:
            Formatted prompt string
        """
        return self.template.format(**kwargs)


# Predefined prompt templates
COMMAND_HELP_TEMPLATE = PromptTemplate("""
You are a helpful command-line assistant. Based on the provided context, help the user with their command-line question.

Context:
{context}

Question: {query}

Please provide:
1. A direct answer to the question
2. Relevant command examples if applicable
3. Any important warnings or considerations

Answer:
""")

CODE_EXPLANATION_TEMPLATE = PromptTemplate("""
You are a code explanation assistant. Based on the provided code context, explain the code or answer the user's question.

Code Context:
{context}

Question: {query}

Please provide:
1. A clear explanation
2. How the code works
3. Any relevant examples or usage patterns

Answer:
""")

TROUBLESHOOTING_TEMPLATE = PromptTemplate("""
You are a troubleshooting assistant. Based on the provided context, help the user solve their problem.

Context:
{context}

Problem: {query}

Please provide:
1. Likely causes of the problem
2. Step-by-step solution
3. Prevention tips for the future

Answer:
""")


def create_specialized_generator(config: AppConfig, api_manager: APIManager, 
                               template: PromptTemplate) -> APIGenerator:
    """Create a specialized generator with custom prompt template.
    
    Args:
        config: Application configuration
        api_manager: API manager instance
        template: Custom prompt template
        
    Returns:
        Configured APIGenerator instance
    """
    generator = APIGenerator(config, api_manager)
    
    # Override the prompt building method
    original_build_prompt = generator._build_prompt
    
    def custom_build_prompt(query: str, context_documents: List[Document]) -> str:
        context_text = "\n\n".join([doc.page_content for doc in context_documents])
        return template.format(context=context_text, query=query)
    
    generator._build_prompt = custom_build_prompt
    
    return generator