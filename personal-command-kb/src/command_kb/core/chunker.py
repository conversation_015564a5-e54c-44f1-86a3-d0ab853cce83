"""
RAG管道的智能文本分片器 - 分片步骤。

本模块提供智能文本分片功能，保持语义完整性和命令行上下文。
确保命令和其描述保持在一起，同时维持最佳的分片大小以便嵌入。
"""

import re
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod

from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from ..constants import (
    DEFAULT_CHUNK_SIZE, 
    DEFAULT_CHUNK_OVERLAP, 
    MIN_CHUNK_SIZE, 
    MAX_CHUNK_SIZE
)

logger = logging.getLogger(__name__)


class ChunkingStrategy(Enum):
    """不同的分片策略。"""
    FIXED_SIZE = "fixed_size"        # 固定大小分片
    SEMANTIC = "semantic"            # 语义感知分片
    COMMAND_AWARE = "command_aware"  # 命令感知分片
    HYBRID = "hybrid"                # 混合策略分片


@dataclass
class ChunkingConfig:
    """文本分片配置。"""
    chunk_size: int = DEFAULT_CHUNK_SIZE              # 分片大小
    chunk_overlap: int = DEFAULT_CHUNK_OVERLAP        # 分片重叠大小
    strategy: ChunkingStrategy = ChunkingStrategy.COMMAND_AWARE  # 分片策略
    preserve_commands: bool = True                    # 保持命令完整性
    preserve_code_blocks: bool = True                 # 保持代码块完整性
    min_chunk_size: int = MIN_CHUNK_SIZE             # 最小分片大小
    max_chunk_size: int = MAX_CHUNK_SIZE             # 最大分片大小


class BaseChunker(ABC):
    """文本分片器的抽象基类。"""
    
    def __init__(self, config: ChunkingConfig):
        """使用配置初始化分片器。
        
        Args:
            config: 分片配置
        """
        self.config = config
    
    @abstractmethod
    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """将文档分片为更小的片段。
        
        Args:
            documents: 要分片的文档列表
            
        Returns:
            分片后的文档列表
        """
        pass
    
    def _create_chunk_metadata(self, original_doc: Document, chunk_index: int, 
                              chunk_text: str, start_pos: int = 0) -> Dict[str, Any]:
        """为分片创建元数据。
        
        Args:
            original_doc: 原始文档
            chunk_index: 分片索引
            chunk_text: 分片文本内容
            start_pos: 在原始文本中的起始位置
            
        Returns:
            元数据字典
        """
        metadata = original_doc.metadata.copy()
        metadata.update({
            'chunk_index': chunk_index,
            'chunk_size': len(chunk_text),
            'chunk_start_pos': start_pos,
            'original_doc_size': len(original_doc.page_content),
            'chunking_strategy': self.config.strategy.value,
        })
        return metadata


class SmartChunker(BaseChunker):
    """保留命令行上下文和语义含义的智能分片器。
    
    这个分片器使用多种策略：
    1. 保存命令块及其描述在一起
    2. 尊重Markdown结构（标题、列表等）
    3. 维护代码块的完整性
    4. 尽可能使用语义边界
    """
    
    def __init__(self, config: Optional[ChunkingConfig] = None):
        """初始化智能分片器。
        
        Args:
            config: 分片配置，如果为None则使用默认配置
        """
        super().__init__(config or ChunkingConfig())
        
        # 初始化基础文本分割器作为备用
        self.base_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            separators=["\n\n", "\n", " ", ""],
        )
        
        # 用于识别不同内容类型的模式
        self.code_block_pattern = re.compile(r'```[\s\S]*?```', re.MULTILINE)  # 代码块模式
        self.command_pattern = re.compile(r'^\$\s+.+$', re.MULTILINE)          # 命令行模式
        self.header_pattern = re.compile(r'^#{1,6}\s+.+$', re.MULTILINE)       # 标题模式
    
    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """使用智能策略对文档进行分片。
        
        Args:
            documents: 要分片的文档列表
            
        Returns:
            分片后的文档列表
        """
        chunked_docs = []
        
        for doc in documents:
            if self.config.strategy == ChunkingStrategy.COMMAND_AWARE:
                chunks = self._command_aware_chunking(doc)  # 命令感知分片
            elif self.config.strategy == ChunkingStrategy.SEMANTIC:
                chunks = self._semantic_chunking(doc)       # 语义感知分片
            elif self.config.strategy == ChunkingStrategy.HYBRID:
                chunks = self._hybrid_chunking(doc)         # 混合策略分片
            else:  # FIXED_SIZE
                chunks = self._fixed_size_chunking(doc)     # 固定大小分片
            
            chunked_docs.extend(chunks)
        
        logger.info(f"将 {len(documents)} 个文档分片为 {len(chunked_docs)} 个块")
        return chunked_docs    
        
    def _command_aware_chunking(self, document: Document) -> List[Document]:
        """在保持命令上下文的同时对文档进行分片。
        
        Args:
            document: 要分片的文档
            
        Returns:
            分片后的文档列表
        """
        content = document.page_content
        chunks = []
        
        # 查找所有代码块和命令
        code_blocks = list(self.code_block_pattern.finditer(content))
        commands = list(self.command_pattern.finditer(content))
        headers = list(self.header_pattern.finditer(content))
        
        # 基于重要边界创建语义段落
        segments = self._create_semantic_segments(content, code_blocks, commands, headers)
        
        # 将段落组合成块
        current_chunk = ""
        current_start = 0
        chunk_index = 0
        
        for segment in segments:
            # 检查添加这个段落是否会超过块大小
            if (len(current_chunk) + len(segment['text']) > self.config.chunk_size and 
                len(current_chunk) > self.config.min_chunk_size):
                
                # 从当前内容创建块
                if current_chunk.strip():
                    chunk_doc = Document(
                        page_content=current_chunk.strip(),
                        metadata=self._create_chunk_metadata(
                            document, chunk_index, current_chunk, current_start
                        )
                    )
                    chunks.append(chunk_doc)
                    chunk_index += 1
                
                # 使用重叠开始新块
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + segment['text']
                current_start = segment['start']
            else:
                current_chunk += segment['text']
                if not current_chunk:
                    current_start = segment['start']
        
        # 添加最后一个块
        if current_chunk.strip():
            chunk_doc = Document(
                page_content=current_chunk.strip(),
                metadata=self._create_chunk_metadata(
                    document, chunk_index, current_chunk, current_start
                )
            )
            chunks.append(chunk_doc)
        
        return chunks
    
    def _create_semantic_segments(self, content: str, code_blocks: List, 
                                 commands: List, headers: List) -> List[Dict[str, Any]]:
        """从内容创建语义段落。
        
        Args:
            content: 文本内容
            code_blocks: 代码块匹配列表
            commands: 命令匹配列表
            headers: 标题匹配列表
            
        Returns:
            段落字典列表
        """
        segments = []
        last_pos = 0
        
        # 组合所有重要位置
        important_positions = []
        
        for match in code_blocks:
            important_positions.append({
                'start': match.start(),
                'end': match.end(),
                'type': 'code_block',
                'text': match.group()
            })
        
        for match in headers:
            important_positions.append({
                'start': match.start(),
                'end': match.end(),
                'type': 'header',
                'text': match.group()
            })
        
        # 按位置排序
        important_positions.sort(key=lambda x: x['start'])
        
        # Create segments
        for pos in important_positions:
            # Add text before this important element
            if pos['start'] > last_pos:
                text_before = content[last_pos:pos['start']].strip()
                if text_before:
                    segments.append({
                        'start': last_pos,
                        'end': pos['start'],
                        'type': 'text',
                        'text': text_before + '\n'
                    })
            
            # Add the important element
            segments.append({
                'start': pos['start'],
                'end': pos['end'],
                'type': pos['type'],
                'text': pos['text'] + '\n'
            })
            
            last_pos = pos['end']
        
        # Add remaining text
        if last_pos < len(content):
            remaining_text = content[last_pos:].strip()
            if remaining_text:
                segments.append({
                    'start': last_pos,
                    'end': len(content),
                    'type': 'text',
                    'text': remaining_text
                })
        
        return segments   
    def _semantic_chunking(self, document: Document) -> List[Document]:
        """Chunk document based on semantic boundaries.
        
        Args:
            document: Document to chunk
            
        Returns:
            List of chunked documents
        """
        content = document.page_content
        
        # Split by paragraphs first
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""
        chunk_index = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # Check if adding this paragraph would exceed chunk size
            if (len(current_chunk) + len(paragraph) > self.config.chunk_size and
                len(current_chunk) > self.config.min_chunk_size):
                
                # Create chunk
                if current_chunk.strip():
                    chunk_doc = Document(
                        page_content=current_chunk.strip(),
                        metadata=self._create_chunk_metadata(
                            document, chunk_index, current_chunk
                        )
                    )
                    chunks.append(chunk_doc)
                    chunk_index += 1
                
                # Start new chunk
                current_chunk = paragraph + '\n\n'
            else:
                current_chunk += paragraph + '\n\n'
        
        # Add final chunk
        if current_chunk.strip():
            chunk_doc = Document(
                page_content=current_chunk.strip(),
                metadata=self._create_chunk_metadata(
                    document, chunk_index, current_chunk
                )
            )
            chunks.append(chunk_doc)
        
        return chunks
    
    def _hybrid_chunking(self, document: Document) -> List[Document]:
        """Combine command-aware and semantic chunking.
        
        Args:
            document: Document to chunk
            
        Returns:
            List of chunked documents
        """
        # First try command-aware chunking
        command_chunks = self._command_aware_chunking(document)
        
        # If chunks are too large, apply semantic chunking to them
        final_chunks = []
        for chunk in command_chunks:
            if len(chunk.page_content) > self.config.max_chunk_size:
                # Further split large chunks
                sub_chunks = self._semantic_chunking(chunk)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append(chunk)
        
        return final_chunks
    
    def _fixed_size_chunking(self, document: Document) -> List[Document]:
        """Simple fixed-size chunking using base splitter.
        
        Args:
            document: Document to chunk
            
        Returns:
            List of chunked documents
        """
        chunks = self.base_splitter.split_documents([document])
        
        # Add chunk metadata
        for i, chunk in enumerate(chunks):
            chunk.metadata.update(
                self._create_chunk_metadata(document, i, chunk.page_content)
            )
        
        return chunks
    
    def _get_overlap_text(self, text: str) -> str:
        """Get overlap text for chunk continuity.
        
        Args:
            text: Current chunk text
            
        Returns:
            Overlap text for next chunk
        """
        if len(text) <= self.config.chunk_overlap:
            return text
        
        # Try to find a good breaking point for overlap
        overlap_start = len(text) - self.config.chunk_overlap
        
        # Look for sentence or paragraph boundary
        for i in range(overlap_start, len(text)):
            if text[i] in '.!?\n':
                return text[i+1:].lstrip()
        
        # 退化到简单子字符串
        return text[-self.config.chunk_overlap:]
    
    def _preserve_code_blocks(self, text: str) -> bool:
        """检查文本是否包含应该保留的代码块。
        
        Args:
            text: 要检查的文本
            
        Returns:
            如果包含代码块则返回True
        """
        return bool(self.code_block_pattern.search(text))
    
    def _contains_commands(self, text: str) -> bool:
        """检查文本是否包含命令行命令。
        
        Args:
            text: 要检查的文本
            
        Returns:
            如果包含命令则返回True
        """
        return bool(self.command_pattern.search(text))


class FixedSizeChunker(BaseChunker):
    """用于对比的简单固定大小分片器。"""
    
    def __init__(self, config: Optional[ChunkingConfig] = None):
        """初始化固定大小分片器。
        
        Args:
            config: 分片配置
        """
        super().__init__(config or ChunkingConfig(strategy=ChunkingStrategy.FIXED_SIZE))
        
        self.splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
        )
    
    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """使用固定大小策略对文档进行分片。
        
        Args:
            documents: 要分片的文档列表
            
        Returns:
            分片后的文档列表
        """
        all_chunks = []
        
        for doc in documents:
            chunks = self.splitter.split_documents([doc])
            
            # 添加元数据
            for i, chunk in enumerate(chunks):
                chunk.metadata.update(
                    self._create_chunk_metadata(doc, i, chunk.page_content)
                )
            
            all_chunks.extend(chunks)
        
        return all_chunks


def create_chunker(strategy: ChunkingStrategy = ChunkingStrategy.COMMAND_AWARE,
                  chunk_size: int = DEFAULT_CHUNK_SIZE,
                  chunk_overlap: int = DEFAULT_CHUNK_OVERLAP) -> BaseChunker:
    """创建适当分片器的工厂函数。
    
    Args:
        strategy: 要使用的分片策略
        chunk_size: 块的大小
        chunk_overlap: 块之间的重叠
        
    Returns:
        配置好的分片器实例
    """
    config = ChunkingConfig(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        strategy=strategy,
    )
    
    if strategy == ChunkingStrategy.FIXED_SIZE:
        return FixedSizeChunker(config)
    else:
        return SmartChunker(config)
