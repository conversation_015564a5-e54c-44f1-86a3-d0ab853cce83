"""
ChromaDB storage for RAG pipeline - Store step.

This module handles vector storage and retrieval using ChromaDB:
- Local vector database for privacy
- Efficient similarity search
- Metadata filtering and querying
- Batch operations for performance
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

import chromadb
from chromadb.config import Settings
from langchain_core.documents import Document

from ..config import AppConfig
from ..constants import DEFAULT_COLLECTION_NAME, DEFAULT_DISTANCE_METRIC

logger = logging.getLogger(__name__)


class ChromaStorage:
    """ChromaDB-based vector storage with local persistence.
    
    Provides efficient vector storage and retrieval with:
    - Local file-based persistence for privacy
    - Metadata filtering and search
    - Batch operations for performance
    - Automatic collection management
    """
    
    def __init__(self, config: AppConfig):
        """Initialize ChromaDB storage.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.db_path = Path(config.database.path)
        self.collection_name = config.database.collection_name
        self.distance_metric = getattr(config.database, 'distance_metric', DEFAULT_DISTANCE_METRIC)
        
        # Ensure database directory exists
        self.db_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=str(self.db_path),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True,
            )
        )
        
        # Get or create collection
        self.collection = self._get_or_create_collection()
        
        logger.info(f"Initialized ChromaDB storage at {self.db_path}")
        logger.info(f"Collection: {self.collection_name}, Distance: {self.distance_metric}")
    
    def _get_or_create_collection(self):
        """Get existing collection or create new one.
        
        Returns:
            ChromaDB collection instance
        """
        try:
            # Try to get existing collection
            collection = self.client.get_collection(
                name=self.collection_name,
                embedding_function=None  # We provide embeddings directly
            )
            logger.info(f"Using existing collection: {self.collection_name}")
            
        except Exception:
            # Create new collection
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"hnsw:space": self.distance_metric},
                embedding_function=None
            )
            logger.info(f"Created new collection: {self.collection_name}")
        
        return collection
    
    def store_documents(self, documents: List[Document], 
                       embeddings: List[List[float]]) -> bool:
        """Store documents with their embeddings.
        
        Args:
            documents: List of documents to store
            embeddings: List of embedding vectors
            
        Returns:
            True if successful, False otherwise
        """
        if len(documents) != len(embeddings):
            logger.error("Number of documents and embeddings must match")
            return False
        
        if not documents:
            logger.warning("No documents to store")
            return True
        
        try:
            # Prepare data for ChromaDB
            ids = []
            texts = []
            metadatas = []
            
            for i, (doc, embedding) in enumerate(zip(documents, embeddings)):
                # Generate unique ID
                doc_id = self._generate_document_id(doc, i)
                ids.append(doc_id)
                
                # Store text content
                texts.append(doc.page_content)
                
                # Prepare metadata (ChromaDB has restrictions on metadata)
                metadata = self._prepare_metadata(doc.metadata)
                metadatas.append(metadata)
            
            # Store in ChromaDB
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas
            )
            
            logger.info(f"Stored {len(documents)} documents in ChromaDB")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store documents: {e}")
            return False
    
    def store_single_document(self, document: Document, 
                            embedding: List[float]) -> bool:
        """Store a single document with its embedding.
        
        Args:
            document: Document to store
            embedding: Embedding vector
            
        Returns:
            True if successful, False otherwise
        """
        return self.store_documents([document], [embedding])
    
    def search_similar(self, query_embedding: List[float], 
                      top_k: int = 5,
                      metadata_filter: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar documents.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of results to return
            metadata_filter: Optional metadata filter
            
        Returns:
            List of search results with documents and scores
        """
        try:
            # Perform similarity search
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=metadata_filter,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Format results
            formatted_results = []
            
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    result = {
                        'document': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else 0.0,
                        'similarity': 1 - results['distances'][0][i] if results['distances'] else 1.0,
                    }
                    formatted_results.append(result)
            
            logger.debug(f"Found {len(formatted_results)} similar documents")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []   
    def search_by_metadata(self, metadata_filter: Dict[str, Any], 
                        limit: int = 100) -> List[Dict[str, Any]]:
        """Search documents by metadata only.
        
        Args:
            metadata_filter: Metadata filter criteria
            limit: Maximum number of results
            
        Returns:
            List of matching documents
        """
        try:
            results = self.collection.get(
                where=metadata_filter,
                limit=limit,
                include=['documents', 'metadatas']
            )
            
            formatted_results = []
            
            if results['documents']:
                for i in range(len(results['documents'])):
                    result = {
                        'document': results['documents'][i],
                        'metadata': results['metadatas'][i] if results['metadatas'] else {},
                    }
                    formatted_results.append(result)
            
            logger.debug(f"Found {len(formatted_results)} documents by metadata")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Metadata search failed: {e}")
            return []
    
    def update_document(self, document_id: str, document: Document, 
                       embedding: List[float]) -> bool:
        """Update an existing document.
        
        Args:
            document_id: ID of document to update
            document: Updated document
            embedding: Updated embedding
            
        Returns:
            True if successful, False otherwise
        """
        try:
            metadata = self._prepare_metadata(document.metadata)
            
            self.collection.update(
                ids=[document_id],
                embeddings=[embedding],
                documents=[document.page_content],
                metadatas=[metadata]
            )
            
            logger.debug(f"Updated document: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update document {document_id}: {e}")
            return False
    
    def delete_document(self, document_id: str) -> bool:
        """Delete a document by ID.
        
        Args:
            document_id: ID of document to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.collection.delete(ids=[document_id])
            logger.debug(f"Deleted document: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            return False
    
    def delete_by_metadata(self, metadata_filter: Dict[str, Any]) -> int:
        """Delete documents matching metadata filter.
        
        Args:
            metadata_filter: Metadata filter criteria
            
        Returns:
            Number of documents deleted
        """
        try:
            # First get documents to delete
            results = self.collection.get(
                where=metadata_filter,
                include=['ids']
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                deleted_count = len(results['ids'])
                logger.info(f"Deleted {deleted_count} documents by metadata")
                return deleted_count
            else:
                logger.debug("No documents found matching metadata filter")
                return 0
                
        except Exception as e:
            logger.error(f"Failed to delete by metadata: {e}")
            return 0
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics.
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            count = self.collection.count()
            
            # Get sample of metadata to understand structure
            sample_results = self.collection.get(
                limit=10,
                include=['metadatas']
            )
            
            metadata_keys = set()
            if sample_results['metadatas']:
                for metadata in sample_results['metadatas']:
                    if metadata:
                        metadata_keys.update(metadata.keys())
            
            stats = {
                'collection_name': self.collection_name,
                'document_count': count,
                'database_path': str(self.db_path),
                'distance_metric': self.distance_metric,
                'metadata_fields': list(metadata_keys),
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def clear_collection(self) -> bool:
        """Clear all documents from the collection.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get all document IDs
            results = self.collection.get(include=['ids'])
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"Cleared {len(results['ids'])} documents from collection")
            else:
                logger.info("Collection is already empty")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear collection: {e}")
            return False
    
    def _generate_document_id(self, document: Document, index: int) -> str:
        """Generate unique ID for a document.
        
        Args:
            document: Document to generate ID for
            index: Index in batch
            
        Returns:
            Unique document ID
        """
        # Try to use source file and chunk info if available
        source = document.metadata.get('source', 'unknown')
        chunk_index = document.metadata.get('chunk_index', index)
        
        # Create deterministic ID based on source and chunk
        base_id = f"{Path(source).stem}_{chunk_index}"
        
        # Add UUID suffix to ensure uniqueness
        unique_suffix = str(uuid.uuid4())[:8]
        
        return f"{base_id}_{unique_suffix}"
    
    def _prepare_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare metadata for ChromaDB storage.
        
        ChromaDB has restrictions on metadata types and nesting.
        
        Args:
            metadata: Original metadata
            
        Returns:
            Cleaned metadata for ChromaDB
        """
        cleaned_metadata = {}
        
        for key, value in metadata.items():
            # Convert complex types to strings
            if isinstance(value, (list, dict)):
                cleaned_metadata[key] = str(value)
            elif isinstance(value, (int, float, str, bool)):
                cleaned_metadata[key] = value
            elif value is None:
                continue  # Skip None values
            else:
                cleaned_metadata[key] = str(value)
        
        return cleaned_metadata
    
    def backup_collection(self, backup_path: str) -> bool:
        """Create a backup of the collection.
        
        Args:
            backup_path: Path to save backup
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import shutil
            
            backup_path = Path(backup_path)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # Copy database files
            shutil.copytree(
                self.db_path,
                backup_path / "chromadb_backup",
                dirs_exist_ok=True
            )
            
            logger.info(f"Backup created at {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def restore_collection(self, backup_path: str) -> bool:
        """Restore collection from backup.
        
        Args:
            backup_path: Path to backup
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import shutil
            
            backup_path = Path(backup_path) / "chromadb_backup"
            
            if not backup_path.exists():
                logger.error(f"Backup not found at {backup_path}")
                return False
            
            # Close current client
            self.client = None
            
            # Remove current database
            if self.db_path.exists():
                shutil.rmtree(self.db_path)
            
            # Restore from backup
            shutil.copytree(backup_path, self.db_path)
            
            # Reinitialize client and collection
            self.client = chromadb.PersistentClient(
                path=str(self.db_path),
                settings=Settings(anonymized_telemetry=False)
            )
            self.collection = self._get_or_create_collection()
            
            logger.info(f"Restored from backup: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Restore failed: {e}")
            return False