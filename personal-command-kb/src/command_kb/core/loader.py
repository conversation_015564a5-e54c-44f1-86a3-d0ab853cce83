"""
Document loader for RAG pipeline - Load step.

This module handles loading and parsing of various document formats:
- Markdown files with command-line snippets
- Code files (Python, Shell, etc.)
- Plain text files
- Structured extraction of commands and metadata
"""

import os
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Iterator
from dataclasses import dataclass
from abc import ABC, abstractmethod

from langchain_core.documents import Document

from ..constants import SUPPORTED_EXTENSIONS, MAX_DOCUMENT_SIZE, DEFAULT_ENCODING
from ..utils.file_utils import get_file_info, is_text_file

logger = logging.getLogger(__name__)


@dataclass
class CommandSnippet:
    """Represents a command-line snippet with metadata."""
    command: str
    description: str
    language: str = "bash"
    tags: List[str] = None
    source_file: str = ""
    line_number: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class DocumentLoader(ABC):
    """Abstract base class for document loaders."""
    
    @abstractmethod
    def load_documents(self, source_path: str) -> List[Document]:
        """Load documents from source path.
        
        Args:
            source_path: Path to source file or directory
            
        Returns:
            List of loaded documents
        """
        pass
    
    @abstractmethod
    def extract_commands(self, content: str, source_file: str = "") -> List[CommandSnippet]:
        """Extract command snippets from content.
        
        Args:
            content: Text content to parse
            source_file: Source file path for metadata
            
        Returns:
            List of extracted command snippets
        """
        pass


class MarkdownLoader(DocumentLoader):
    """Loader for Markdown files with command-line snippet extraction.
    
    Specializes in parsing Markdown files to extract:
    - Code blocks with command-line content
    - Inline code snippets
    - Associated documentation and context
    """
    
    def __init__(self, encoding: str = DEFAULT_ENCODING):
        """Initialize Markdown loader.
        
        Args:
            encoding: Text encoding for file reading
        """
        self.encoding = encoding
        
        # Patterns for command detection
        self.command_patterns = [
            r'^\$\s+(.+)$',  # Shell prompt: $ command
            r'^>\s+(.+)$',   # PowerShell prompt: > command
            r'^#\s+(.+)$',   # Comment command: # command
            r'^sudo\s+(.+)$',  # Sudo commands
            r'^docker\s+(.+)$',  # Docker commands
            r'^git\s+(.+)$',     # Git commands
            r'^npm\s+(.+)$',     # NPM commands
            r'^pip\s+(.+)$',     # Pip commands
            r'^curl\s+(.+)$',    # Curl commands
        ]
        
        # Code block languages that likely contain commands
        self.command_languages = {
            'bash', 'sh', 'shell', 'zsh', 'fish',
            'powershell', 'ps1', 'cmd', 'batch',
            'console', 'terminal', 'cli'
        }
    
    def load_documents(self, source_path: str) -> List[Document]:
        """Load Markdown documents from path.
        
        Args:
            source_path: Path to file or directory
            
        Returns:
            List of Document objects
        """
        documents = []
        source_path = Path(source_path).resolve()
        
        try:
            if source_path.exists() and source_path.is_file():
                if self._is_supported_file(source_path):
                    doc = self._load_single_file(source_path)
                    if doc:
                        documents.append(doc)
            elif source_path.exists() and os.path.isdir(str(source_path)):
                documents.extend(self._load_directory(source_path))
            else:
                logger.error(f"Source path does not exist: {source_path}")
        except Exception as e:
            logger.error(f"Error processing path {source_path}: {e}")
        
        logger.info(f"Loaded {len(documents)} documents from {source_path}")
        return documents    
    def _load_directory(self, directory: Path) -> List[Document]:
        """Load all supported files from directory recursively.
        
        Args:
            directory: Directory path to scan
            
        Returns:
            List of Document objects
        """
        documents = []
        
        for file_path in directory.rglob("*"):
            if file_path.is_file() and self._is_supported_file(file_path):
                doc = self._load_single_file(file_path)
                if doc:
                    documents.append(doc)
        
        return documents
    
    def _load_single_file(self, file_path: Path) -> Optional[Document]:
        """Load a single file as a Document.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Document object or None if failed
        """
        try:
            # Check file size
            file_info = get_file_info(str(file_path))
            if file_info.get('size', 0) > MAX_DOCUMENT_SIZE:
                logger.warning(f"File too large, skipping: {file_path}")
                return None
            
            # Read file content
            with open(file_path, 'r', encoding=self.encoding) as f:
                content = f.read()
            
            # Extract metadata
            metadata = {
                'source': str(file_path),
                'file_name': file_path.name,
                'file_type': file_path.suffix.lower(),
                'size': len(content),
                'modified_time': file_info.get('modified_time'),
            }
            
            # Extract command snippets
            commands = self.extract_commands(content, str(file_path))
            if commands:
                metadata['command_count'] = len(commands)
                metadata['commands'] = [
                    {
                        'command': cmd.command,
                        'description': cmd.description,
                        'language': cmd.language,
                        'tags': cmd.tags,
                        'line_number': cmd.line_number,
                    }
                    for cmd in commands
                ]
            
            return Document(
                page_content=content,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to load file {file_path}: {e}")
            return None
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """Check if file is supported for loading.
        
        Args:
            file_path: Path to check
            
        Returns:
            True if file is supported
        """
        if not is_text_file(str(file_path)):
            return False
        
        extension = file_path.suffix.lower()
        return extension in SUPPORTED_EXTENSIONS    
    def extract_commands(self, content: str, source_file: str = "") -> List[CommandSnippet]:
        """Extract command snippets from Markdown content.
        
        Args:
            content: Markdown content to parse
            source_file: Source file path for metadata
            
        Returns:
            List of CommandSnippet objects
        """
        commands = []
        lines = content.split('\n')
        
        # Extract from code blocks
        commands.extend(self._extract_from_code_blocks(lines, source_file))
        
        # Extract from inline patterns
        commands.extend(self._extract_from_patterns(lines, source_file))
        
        return commands
    
    def _extract_from_code_blocks(self, lines: List[str], source_file: str) -> List[CommandSnippet]:
        """Extract commands from Markdown code blocks.
        
        Args:
            lines: Lines of content
            source_file: Source file path
            
        Returns:
            List of CommandSnippet objects
        """
        commands = []
        in_code_block = False
        code_language = ""
        code_lines = []
        block_start_line = 0
        description_lines = []
        
        for i, line in enumerate(lines):
            # Check for code block start
            if line.strip().startswith('```'):
                if not in_code_block:
                    # Starting code block
                    in_code_block = True
                    code_language = line.strip()[3:].strip().lower()
                    code_lines = []
                    block_start_line = i + 1
                    
                    # Look for description in previous lines
                    description_lines = self._get_preceding_description(lines, i)
                else:
                    # Ending code block
                    in_code_block = False
                    
                    # Process code block if it contains commands
                    if self._is_command_block(code_language, code_lines):
                        block_commands = self._parse_code_block(
                            code_lines, code_language, description_lines,
                            source_file, block_start_line
                        )
                        commands.extend(block_commands)
                    
                    code_language = ""
                    code_lines = []
                    description_lines = []
            elif in_code_block:
                code_lines.append(line)
        
        return commands
    
    def _extract_from_patterns(self, lines: List[str], source_file: str) -> List[CommandSnippet]:
        """Extract commands using regex patterns.
        
        Args:
            lines: Lines of content
            source_file: Source file path
            
        Returns:
            List of CommandSnippet objects
        """
        commands = []
        
        for i, line in enumerate(lines):
            for pattern in self.command_patterns:
                match = re.match(pattern, line.strip())
                if match:
                    command = match.group(1).strip()
                    description = self._get_command_description(lines, i)
                    
                    commands.append(CommandSnippet(
                        command=command,
                        description=description,
                        language="bash",
                        source_file=source_file,
                        line_number=i + 1,
                        tags=self._extract_tags(command)
                    ))
                    break
        
        return commands    
    def _is_command_block(self, language: str, code_lines: List[str]) -> bool:
        """Check if code block likely contains commands.
        
        Args:
            language: Code block language
            code_lines: Lines of code
            
        Returns:
            True if block likely contains commands
        """
        if language in self.command_languages:
            return True
        
        # Check content for command patterns
        for line in code_lines:
            for pattern in self.command_patterns:
                if re.match(pattern, line.strip()):
                    return True
        
        return False
    
    def _parse_code_block(
        self, 
        code_lines: List[str], 
        language: str, 
        description_lines: List[str],
        source_file: str, 
        start_line: int
    ) -> List[CommandSnippet]:
        """Parse commands from a code block.
        
        Args:
            code_lines: Lines of code
            language: Programming language
            description_lines: Description lines
            source_file: Source file path
            start_line: Starting line number
            
        Returns:
            List of CommandSnippet objects
        """
        commands = []
        description = ' '.join(description_lines).strip()
        
        for i, line in enumerate(code_lines):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Extract command
            command = self._clean_command(line)
            if command:
                commands.append(CommandSnippet(
                    command=command,
                    description=description,
                    language=language or "bash",
                    source_file=source_file,
                    line_number=start_line + i,
                    tags=self._extract_tags(command)
                ))
        
        return commands
    
    def _get_preceding_description(self, lines: List[str], code_block_line: int) -> List[str]:
        """Get description lines preceding a code block.
        
        Args:
            lines: All lines
            code_block_line: Line number of code block start
            
        Returns:
            List of description lines
        """
        description_lines = []
        
        # Look backwards for description
        for i in range(code_block_line - 1, -1, -1):
            line = lines[i].strip()
            
            # Stop at empty line or another code block
            if not line or line.startswith('```'):
                break
            
            # Skip markdown headers
            if line.startswith('#'):
                continue
            
            description_lines.insert(0, line)
            
            # Stop after reasonable amount of context
            if len(description_lines) >= 3:
                break
        
        return description_lines    
    def _get_command_description(self, lines: List[str], command_line: int) -> str:
        """Get description for a command line.
        
        Args:
            lines: All lines
            command_line: Line number of command
            
        Returns:
            Description string
        """
        description_parts = []
        
        # Look for comment on same line
        line = lines[command_line]
        if '#' in line:
            comment_part = line.split('#', 1)[1].strip()
            if comment_part:
                description_parts.append(comment_part)
        
        # Look for preceding comment lines
        for i in range(command_line - 1, -1, -1):
            prev_line = lines[i].strip()
            if not prev_line:
                break
            if prev_line.startswith('#'):
                comment = prev_line[1:].strip()
                if comment:
                    description_parts.insert(0, comment)
            else:
                break
        
        return ' '.join(description_parts)
    
    def _clean_command(self, command: str) -> str:
        """Clean and normalize command string.
        
        Args:
            command: Raw command string
            
        Returns:
            Cleaned command string
        """
        # Remove common prefixes
        prefixes = ['$', '>', '#', 'sudo']
        for prefix in prefixes:
            if command.startswith(prefix + ' '):
                command = command[len(prefix):].strip()
                break
        
        # Remove trailing comments
        if '#' in command:
            command = command.split('#')[0].strip()
        
        return command
    
    def _extract_tags(self, command: str) -> List[str]:
        """Extract tags from command for categorization.
        
        Args:
            command: Command string
            
        Returns:
            List of tags
        """
        tags = []
        command_lower = command.lower()
        
        # Common command categories
        tag_patterns = {
            'docker': ['docker'],
            'git': ['git'],
            'npm': ['npm', 'yarn'],
            'python': ['pip', 'python', 'python3'],
            'system': ['sudo', 'systemctl', 'service'],
            'network': ['curl', 'wget', 'ssh', 'scp'],
            'file': ['ls', 'cp', 'mv', 'rm', 'find', 'grep'],
            'database': ['mysql', 'postgres', 'mongo'],
            'kubernetes': ['kubectl', 'helm'],
        }
        
        for tag, keywords in tag_patterns.items():
            if any(keyword in command_lower for keyword in keywords):
                tags.append(tag)
        
        return tags


class CodeFileLoader(DocumentLoader):
    """Loader for code files (Python, Shell, etc.)."""
    
    def __init__(self, encoding: str = DEFAULT_ENCODING):
        """Initialize code file loader.
        
        Args:
            encoding: Text encoding for file reading
        """
        self.encoding = encoding
    
    def load_documents(self, source_path: str) -> List[Document]:
        """Load code files as documents.
        
        Args:
            source_path: Path to file or directory
            
        Returns:
            List of Document objects
        """
        # Implementation similar to MarkdownLoader but for code files
        # This is a simplified version - can be expanded as needed
        documents = []
        source_path = Path(source_path)
        
        if source_path.is_file():
            doc = self._load_code_file(source_path)
            if doc:
                documents.append(doc)
        
        return documents
    
    def _load_code_file(self, file_path: Path) -> Optional[Document]:
        """Load a single code file."""
        try:
            with open(file_path, 'r', encoding=self.encoding) as f:
                content = f.read()
            
            metadata = {
                'source': str(file_path),
                'file_name': file_path.name,
                'file_type': file_path.suffix.lower(),
                'language': self._detect_language(file_path),
            }
            
            return Document(page_content=content, metadata=metadata)
        except Exception as e:
            logger.error(f"Failed to load code file {file_path}: {e}")
            return None
    
    def _detect_language(self, file_path: Path) -> str:
        """Detect programming language from file extension."""
        extension_map = {
            '.py': 'python',
            '.sh': 'bash',
            '.bash': 'bash',
            '.zsh': 'zsh',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust',
        }
        return extension_map.get(file_path.suffix.lower(), 'text')
    
    def extract_commands(self, content: str, source_file: str = "") -> List[CommandSnippet]:
        """Extract commands from code content."""
        # Basic implementation - can be enhanced for specific languages
        commands = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            # Look for shell commands in comments or strings
            if 'os.system(' in line or 'subprocess.' in line:
                # Extract command from Python subprocess calls
                command = self._extract_python_command(line)
                if command:
                    commands.append(CommandSnippet(
                        command=command,
                        description=f"Command from {source_file}",
                        language="python",
                        source_file=source_file,
                        line_number=i + 1,
                    ))
        
        return commands
    
    def _extract_python_command(self, line: str) -> Optional[str]:
        """Extract command from Python subprocess call."""
        # Simple regex to extract commands - can be improved
        patterns = [
            r'os\.system\(["\']([^"\']+)["\']',
            r'subprocess\.run\(["\']([^"\']+)["\']',
            r'subprocess\.call\(["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        
        return None