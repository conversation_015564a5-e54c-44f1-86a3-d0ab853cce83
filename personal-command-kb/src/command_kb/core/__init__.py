"""
Core RAG modules for the Personal Command-Line Vector Knowledge Base.

This package contains the six core RAG pipeline components:
1. Load - Document loading and parsing
2. Chunk - Intelligent text chunking
3. Embed - Text embedding via APIs
4. Store - Vector database storage
5. Retrieve - Semantic search and retrieval
6. Generate - Answer generation via APIs

Each module is designed to be independent and composable, following
the single responsibility principle.
"""

from .loader import MarkdownLoader, DocumentLoader
from .chunker import SmartChunker, ChunkingStrategy
from .embedder import APIEmbedder
from .storage import ChromaStorage
from .retriever import SemanticRetriever
from .generator import APIGenerator

__all__ = [
    "MarkdownLoader",
    "DocumentLoader", 
    "SmartChunker",
    "ChunkingStrategy",
    "APIEmbedder",
    "ChromaStorage",
    "SemanticRetriever",
    "APIGenerator",
]