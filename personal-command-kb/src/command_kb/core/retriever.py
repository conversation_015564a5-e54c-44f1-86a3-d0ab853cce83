"""
Semantic retriever for RAG pipeline - Retrieve step.

This module handles information retrieval with:
- Semantic similarity search
- Hybrid search (semantic + keyword)
- Metadata filtering
- Result ranking and reranking



"""

import logging
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from langchain_core.documents import Document

from .storage import ChromaStorage
from .embedder import APIEmbedder
from ..config import AppConfig

logger = logging.getLogger(__name__)


@dataclass
class RetrievalResult:
    """Result of retrieval operation."""
    documents: List[Document]
    scores: List[float]
    query: str
    total_found: int
    retrieval_time: float = 0.0
    method: str = "semantic"


class SemanticRetriever:
    """Semantic retriever with hybrid search capabilities."""
    
    def __init__(self, config: AppConfig, storage: ChromaStorage, embedder: APIEmbedder):
        """Initialize semantic retriever.
        
        Args:
            config: Application configuration
            storage: Vector storage instance
            embedder: Text embedder instance
        """
        self.config = config
        self.storage = storage
        self.embedder = embedder
        
        # Retrieval parameters
        self.top_k = config.retrieval.top_k
        self.similarity_threshold = config.retrieval.similarity_threshold
        self.enable_hybrid = config.retrieval.enable_hybrid_search
        
        logger.info("Initialized semantic retriever")
    
    # 主检索函数   retrieve 检索
    def retrieve(self, query: str, top_k: Optional[int] = None,
                metadata_filter: Optional[Dict[str, Any]] = None) -> RetrievalResult:
        """Retrieve relevant documents for query.
        
        Args:
            query: 查询内容
            top_k: 要取前面多少个结果集
            metadata_filter: 可为空的metadata过滤
            
        Returns:
            RetrievalResult with documents and scores
        """
        import time
        start_time = time.time()
        
        top_k = top_k or self.top_k
        
        if self.enable_hybrid:
            # 混合搜索
            result = self._hybrid_search(query, top_k, metadata_filter)
        else:
            result = self._semantic_search(query, top_k, metadata_filter)
        
        result.retrieval_time = time.time() - start_time
        
        logger.debug(f"Retrieved {len(result.documents)} documents in {result.retrieval_time:.2f}s")
        return result
    
    def _semantic_search(self, query: str, top_k: int,
                        metadata_filter: Optional[Dict[str, Any]] = None) -> RetrievalResult:
        """Perform semantic similarity search.
        
        Args:
            query: Search query
            top_k: Number of results
            metadata_filter: Optional metadata filter
            
        Returns:
            RetrievalResult with semantic search results
        """
        # Generate query embedding
        embedding_result = self.embedder.embed_single_text(query)
        
        if not embedding_result.success:
            logger.error(f"Failed to embed query: {embedding_result.error}")
            return RetrievalResult(
                documents=[],
                scores=[],
                query=query,
                total_found=0,
                method="semantic"
            )
        
        # Search similar documents
        search_results = self.storage.search_similar(
            query_embedding=embedding_result.embeddings,
            top_k=top_k,
            metadata_filter=metadata_filter
        )
        
        # Convert to Document objects
        documents = []
        scores = []
        
        for result in search_results:
            if result['similarity'] >= self.similarity_threshold:
                doc = Document(
                    page_content=result['document'],
                    metadata=result['metadata']
                )
                documents.append(doc)
                scores.append(result['similarity'])
        
        return RetrievalResult(
            documents=documents,
            scores=scores,
            query=query,
            total_found=len(search_results),
            method="semantic"
        )
    
    def _hybrid_search(self, query: str, top_k: int,
                      metadata_filter: Optional[Dict[str, Any]] = None) -> RetrievalResult:
        """执行混合搜索（语义+关键字）
        
        Args:
            query: Search query
            top_k: 前多少个结果
            metadata_filter: Optional metadata filter
            
        Returns:
            RetrievalResult 为混合搜索结果
        """
        # 获得语义搜索结果
        semantic_results = self._semantic_search(query, top_k * 2, metadata_filter)
        
        # 获得关键字搜索结果
        keyword_results = self._keyword_search(query, top_k, metadata_filter)
        
        # 合并和重读结果
        combined_results = self._combine_results(
            semantic_results, keyword_results, query, top_k
        )
        
        combined_results.method = "hybrid" # 搜索方式
        return combined_results
    
    def _keyword_search(self, query: str, top_k: int,
                       metadata_filter: Optional[Dict[str, Any]] = None) -> RetrievalResult:
        """执行关键字搜索.
        
        Args:
            query: Search query
            top_k: Number of results
            metadata_filter: Optional metadata filter
            
        Returns:
            RetrievalResult 为关键字搜索结果
        """
        # 从查询条件中提取关键字
        keywords = self._extract_keywords(query)
        
        # 通过metadata和content进行关键字查询
        all_docs = self.storage.search_by_metadata(
            metadata_filter or {}, limit=1000
        )
        
        # 通过关键字相关性得分文档
        scored_docs = []
        for doc_result in all_docs:
            score = self._calculate_keyword_score(
                doc_result['document'], keywords
            )
            if score > 0:
                scored_docs.append((doc_result, score))
        
        # Sort by score and take top results
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        scored_docs = scored_docs[:top_k]
        
        # Convert to result format
        documents = []
        scores = []
        
        for doc_result, score in scored_docs:
            doc = Document(
                page_content=doc_result['document'],
                metadata=doc_result['metadata']
            )
            documents.append(doc)
            scores.append(score)
        
        return RetrievalResult(
            documents=documents,
            scores=scores,
            query=query,
            total_found=len(scored_docs),
            method="keyword"
        )
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract keywords from query."""
        # Simple keyword extraction
        keywords = re.findall(r'\b\w+\b', query.lower())
        
        # Remove common stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [kw for kw in keywords if kw not in stop_words and len(kw) > 2]
        
        return keywords
    """
        预处理 ✍️：把文章全部转成小写，准备好一个空的记分板。

        计分 🔢：逐个检查关键词，根据 出现次数 和 关键词长度 累加一个“基础分”。

        平衡 ⚖️：用文章的总长度来调整分数，文章越长，分数就被“稀释”得越多，以体现关键词的“浓度”。

        封顶 🧢：无论分数被算得多高，最高分就是 1.0，确保所有文章都在同一个标准下比较。
    """
    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """计算关键字相关得分."""
        text_lower = text.lower()
        score = 0.0
        
        for keyword in keywords:
            # 计算出现次数
            count = text_lower.count(keyword)
            if count > 0:
                # 按频率和关键词长度加权
                score += count * (len(keyword) / 10.0)
        
        # 按文本长度归一化
        if len(text) > 0:
            score = score / (len(text) / 1000.0)
        
        return min(score, 1.0)
    
    def _combine_results(self, semantic_results: RetrievalResult,
                        keyword_results: RetrievalResult,
                        query: str, top_k: int) -> RetrievalResult:
        """Combine semantic and keyword results."""
        # Create combined document set
        doc_scores = {}
        
        # Add semantic results with weight
        for doc, score in zip(semantic_results.documents, semantic_results.scores):
            doc_key = doc.page_content[:100]  # Use first 100 chars as key
            doc_scores[doc_key] = {
                'document': doc,
                'semantic_score': score * 0.7,  # Weight semantic results
                'keyword_score': 0.0
            }
        
        # Add keyword results with weight
        for doc, score in zip(keyword_results.documents, keyword_results.scores):
            doc_key = doc.page_content[:100]
            if doc_key in doc_scores:
                doc_scores[doc_key]['keyword_score'] = score * 0.3
            else:
                doc_scores[doc_key] = {
                    'document': doc,
                    'semantic_score': 0.0,
                    'keyword_score': score * 0.3
                }
        
        # Calculate combined scores
        combined_docs = []
        for doc_info in doc_scores.values():
            combined_score = doc_info['semantic_score'] + doc_info['keyword_score']
            combined_docs.append((doc_info['document'], combined_score))
        
        # Sort by combined score
        combined_docs.sort(key=lambda x: x[1], reverse=True)
        combined_docs = combined_docs[:top_k]
        
        # Extract documents and scores
        documents = [doc for doc, score in combined_docs]
        scores = [score for doc, score in combined_docs]
        
        return RetrievalResult(
            documents=documents,
            scores=scores,
            query=query,
            total_found=len(combined_docs),
            method="hybrid"
        )