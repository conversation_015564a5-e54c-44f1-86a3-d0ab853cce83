"""
Constants and configuration values for the Personal Command-Line Vector Knowledge Base.

This module contains all hardcoded values, timeouts, limits, and other constants
used throughout the application. All values are externalized to prevent hardcoding
in business logic.
"""

from typing import Dict, List, Tuple

# =============================================================================
# Application Metadata
# =============================================================================
APP_NAME = "Personal Command-Line Vector Knowledge Base"
APP_VERSION = "0.1.0"
APP_DESCRIPTION = "RAG-based intelligent retrieval system for command-line snippets"

# =============================================================================
# API Configuration Constants
# =============================================================================
# Default timeout values (seconds)
DEFAULT_API_TIMEOUT = 30
DEFAULT_CONNECT_TIMEOUT = 10
DEFAULT_READ_TIMEOUT = 60

# Retry configuration
DEFAULT_MAX_RETRIES = 3
DEFAULT_BACKOFF_FACTOR = 2.0
DEFAULT_RETRY_STATUSES = [429, 500, 502, 503, 504]

# Rate limiting defaults
DEFAULT_RATE_LIMIT_RPM = 60  # Requests per minute
DEFAULT_BURST_SIZE = 10
DEFAULT_RATE_LIMIT_WINDOW = 60  # seconds

# API provider names
API_PROVIDERS = {
    "OPENAI": "openai",
    "SILICONFLOW": "siliconflow", 
    "ZHIPU": "zhipu",
    "MOONSHOT": "moonshot",
}

# Default API endpoints
DEFAULT_API_ENDPOINTS = {
    "openai": "https://api.openai.com/v1",
    "siliconflow": "https://api.siliconflow.cn/v1",
    "zhipu": "https://open.bigmodel.cn/api/paas/v4",
    "moonshot": "https://api.moonshot.cn/v1",
}# =============================================================================
# Model Configuration Constants
# =============================================================================
# Default embedding models for each provider
DEFAULT_EMBEDDING_MODELS = {
    "openai": "text-embedding-3-small",
    "siliconflow": "text-embedding-3-small",  # OpenAI compatible
    "zhipu": "embedding-2",
    "moonshot": "text-embedding-v1",
}

# Default generation models for each provider
DEFAULT_GENERATION_MODELS = {
    "openai": "gpt-4o-mini",
    "siliconflow": "gpt-4o-mini",  # OpenAI compatible
    "zhipu": "glm-4-flash",
    "moonshot": "moonshot-v1-8k",
}

# Model dimensions
EMBEDDING_DIMENSIONS = {
    "text-embedding-3-small": 1536,
    "text-embedding-3-large": 3072,
    "embedding-2": 1024,
    "text-embedding-v1": 1536,
}

# =============================================================================
# Cost Configuration Constants
# =============================================================================
# Default cost per 1K tokens (USD) - approximate values
DEFAULT_EMBEDDING_COSTS = {
    "openai": {"text-embedding-3-small": 0.00002},
    "siliconflow": {"text-embedding-3-small": 0.000007},
    "zhipu": {"embedding-2": 0.00001},
    "moonshot": {"text-embedding-v1": 0.00001},
}

DEFAULT_GENERATION_COSTS = {
    "openai": {"gpt-4o-mini": 0.00015},
    "siliconflow": {"gpt-4o-mini": 0.000042},
    "zhipu": {"glm-4-flash": 0.00001},
    "moonshot": {"moonshot-v1-8k": 0.00012},
}

# Cost control defaults
DEFAULT_DAILY_COST_LIMIT = 10.0  # USD
DEFAULT_COST_WARNING_THRESHOLD = 0.8  # 80%
DEFAULT_COST_CHECK_INTERVAL = 300  # seconds# =============================================================================
# Database Configuration Constants
# =============================================================================
# ChromaDB defaults
DEFAULT_COLLECTION_NAME = "command_kb"
DEFAULT_VECTOR_DB_PATH = "./data/vector_db"
DEFAULT_DISTANCE_METRIC = "cosine"

# Database operation limits
MAX_BATCH_SIZE = 1000
DEFAULT_BATCH_SIZE = 100
MAX_QUERY_RESULTS = 100
DEFAULT_TOP_K = 5

# =============================================================================
# Text Processing Constants
# =============================================================================
# Chunking defaults
DEFAULT_CHUNK_SIZE = 500
DEFAULT_CHUNK_OVERLAP = 50
MIN_CHUNK_SIZE = 100
MAX_CHUNK_SIZE = 2000

# Text processing limits
MAX_TEXT_LENGTH = 100000  # characters
MAX_DOCUMENT_SIZE = 10 * 1024 * 1024  # 10MB
DEFAULT_ENCODING = "utf-8"

# Supported file extensions
SUPPORTED_EXTENSIONS = [".md", ".txt", ".py", ".sh", ".bash", ".zsh", ".json", ".yaml", ".yml"]

# =============================================================================
# Caching Configuration Constants
# =============================================================================
# Cache TTL (Time To Live) in seconds
DEFAULT_CACHE_TTL = 3600  # 1 hour
EMBEDDING_CACHE_TTL = 86400  # 24 hours
GENERATION_CACHE_TTL = 1800  # 30 minutes
RETRIEVAL_CACHE_TTL = 600  # 10 minutes

# Cache size limits
DEFAULT_MAX_CACHE_SIZE = 1000
MAX_MEMORY_CACHE_SIZE = 10000
MAX_DISK_CACHE_SIZE = 1024 * 1024 * 1024  # 1GB

# Cache key prefixes
CACHE_PREFIXES = {
    "embedding": "emb:",
    "generation": "gen:",
    "retrieval": "ret:",
    "health": "health:",
}# =============================================================================
# Logging Configuration Constants
# =============================================================================
# Log levels
LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
DEFAULT_LOG_LEVEL = "INFO"
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Log file settings
DEFAULT_LOG_FILE = "./logs/command_kb.log"
DEFAULT_LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
DEFAULT_LOG_BACKUP_COUNT = 5

# Structured logging
STRUCTURED_LOG_FIELDS = [
    "timestamp", "level", "logger", "message", "module", "function", 
    "line", "thread", "process", "user_id", "session_id", "request_id"
]

# =============================================================================
# Monitoring and Health Check Constants
# =============================================================================
# Health check intervals
DEFAULT_HEALTH_CHECK_INTERVAL = 300  # 5 minutes
API_HEALTH_CHECK_TIMEOUT = 10  # seconds
DATABASE_HEALTH_CHECK_TIMEOUT = 5  # seconds

# Performance thresholds
SLOW_QUERY_THRESHOLD = 2.0  # seconds
HIGH_MEMORY_THRESHOLD = 0.8  # 80%
HIGH_CPU_THRESHOLD = 0.8  # 80%

# Monitoring metrics
METRICS_COLLECTION_INTERVAL = 60  # seconds
METRICS_RETENTION_DAYS = 30

# =============================================================================
# Security Configuration Constants
# =============================================================================
# API key patterns for validation
API_KEY_PATTERNS = {
    "openai": r"^sk-[a-zA-Z0-9]{48}$",
    "siliconflow": r"^sk-[a-zA-Z0-9]{48}$",
    "zhipu": r"^[a-zA-Z0-9]{32}\.[a-zA-Z0-9]{32}$",
    "moonshot": r"^sk-[a-zA-Z0-9]{48}$",
}

# Sensitive data patterns to filter
SENSITIVE_PATTERNS = [
    r"password\s*[:=]\s*['\"]?([^'\"\\s]+)",
    r"api[_-]?key\s*[:=]\s*['\"]?([^'\"\\s]+)",
    r"secret\s*[:=]\s*['\"]?([^'\"\\s]+)",
    r"token\s*[:=]\s*['\"]?([^'\"\\s]+)",
]

# Security defaults
DEFAULT_API_KEY_ROTATION_DAYS = 90
AUDIT_LOG_RETENTION_DAYS = 365

# =============================================================================
# CLI Configuration Constants
# =============================================================================
# Command line interface defaults
DEFAULT_OUTPUT_FORMAT = "table"
SUPPORTED_OUTPUT_FORMATS = ["table", "json", "yaml", "csv"]

# Progress bar settings
PROGRESS_BAR_WIDTH = 50
PROGRESS_UPDATE_INTERVAL = 0.1  # seconds

# CLI colors and styling
CLI_COLORS = {
    "success": "green",
    "error": "red", 
    "warning": "yellow",
    "info": "blue",
    "highlight": "cyan",
}

# =============================================================================
# Error Messages and Status Codes
# =============================================================================
# HTTP status codes for API responses
HTTP_STATUS_CODES = {
    "OK": 200,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "TOO_MANY_REQUESTS": 429,
    "INTERNAL_SERVER_ERROR": 500,
    "BAD_GATEWAY": 502,
    "SERVICE_UNAVAILABLE": 503,
    "GATEWAY_TIMEOUT": 504,
}

# Application error codes
APP_ERROR_CODES = {
    "CONFIG_ERROR": 1001,
    "API_ERROR": 1002,
    "DATABASE_ERROR": 1003,
    "CACHE_ERROR": 1004,
    "VALIDATION_ERROR": 1005,
    "AUTHENTICATION_ERROR": 1006,
    "RATE_LIMIT_ERROR": 1007,
    "COST_LIMIT_ERROR": 1008,
}

# Default error messages
DEFAULT_ERROR_MESSAGES = {
    "api_unavailable": "API service is currently unavailable. Please try again later.",
    "rate_limit_exceeded": "Rate limit exceeded. Please wait before making more requests.",
    "cost_limit_exceeded": "Daily cost limit exceeded. Please check your usage.",
    "invalid_api_key": "Invalid API key. Please check your configuration.",
    "network_error": "Network connection error. Please check your internet connection.",
}