"""
OpenAI API client implementation.

This module provides a concrete implementation of the BaseAPIClient for OpenAI's API.
It supports both direct OpenAI API calls and OpenAI-compatible APIs (like SiliconFlow).
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional, Iterator

import requests
from openai import OpenAI

from .base_client import BaseAPIClient, APIResponse, APIError, APIErrorType
from ..constants import DEFAULT_EMBEDDING_COSTS, DEFAULT_GENERATION_COSTS

logger = logging.getLogger(__name__)


class OpenAIClient(BaseAPIClient):
    """OpenAI API client implementation.
    
    Supports both OpenAI and OpenAI-compatible APIs with unified interface.
    Provides embedding and text generation capabilities with proper error handling.
    """
    
    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.openai.com/v1",
        timeout: int = 30,
        max_retries: int = 3,
        rate_limit_rpm: int = 500,
        provider_name: str = "openai",
        embedding_model: str = "text-embedding-3-small",
        generation_model: str = "gpt-4o-mini",
    ):
        """Initialize OpenAI client.
        
        Args:
            api_key: OpenAI API key
            base_url: API base URL (for OpenAI-compatible APIs)
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
            rate_limit_rpm: Rate limit in requests per minute
            provider_name: Provider name for identification
            embedding_model: Default embedding model
            generation_model: Default generation model
        """
        super().__init__(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout,
            max_retries=max_retries,
            rate_limit_rpm=rate_limit_rpm,
            provider_name=provider_name,
        )
        
        self.embedding_model = embedding_model
        self.generation_model = generation_model
        
        # Initialize OpenAI client
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout,
        )
        
        logger.info(f"Initialized OpenAI client for {provider_name}")
    
    def embed_text(self, text: str, model: Optional[str] = None) -> APIResponse:
        """Generate embedding for a single text.
        
        Args:
            text: Text to embed
            model: Optional model override
            
        Returns:
            APIResponse with embedding vector
        """
        return self.embed_texts([text], model)   
    def embed_texts(self, texts: List[str], model: Optional[str] = None) -> APIResponse:
        """Generate embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            
        Returns:
            APIResponse with list of embedding vectors
        """
        if not texts:
            return APIResponse(
                success=False,
                error="No texts provided for embedding",
                error_type=APIErrorType.VALIDATION,
                provider=self.provider_name,
            )
        
        model = model or self.embedding_model
        start_time = time.time()
        
        try:
            # Handle rate limiting
            self._handle_rate_limit()
            
            # Make API request
            response = self.client.embeddings.create(
                input=texts,
                model=model,
            )
            
            # Extract embeddings
            embeddings = [item.embedding for item in response.data]
            tokens_used = response.usage.total_tokens
            
            # Calculate cost
            cost = self._calculate_embedding_cost(tokens_used, model)
            
            # Update statistics
            self._update_stats(tokens_used, cost)
            
            response_time = time.time() - start_time
            
            return APIResponse(
                success=True,
                data=embeddings,
                response_time=response_time,
                tokens_used=tokens_used,
                cost=cost,
                provider=self.provider_name,
                model=model,
            )
            
        except Exception as e:
            error_type, retryable = self._classify_error(e)
            return APIResponse(
                success=False,
                error=str(e),
                error_type=error_type,
                response_time=time.time() - start_time,
                provider=self.provider_name,
                model=model,
            )    
    def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> APIResponse:
        """Generate text based on prompt.
        
        Args:
            prompt: Input prompt
            model: Optional model override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
            
        Returns:
            APIResponse with generated text
        """
        model = model or self.generation_model
        start_time = time.time()
        
        try:
            # Handle rate limiting
            self._handle_rate_limit()
            
            # Prepare messages
            messages = [{"role": "user", "content": prompt}]
            
            # Make API request
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            # Extract generated text
            generated_text = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            
            # Calculate cost
            cost = self._calculate_generation_cost(tokens_used, model)
            
            # Update statistics
            self._update_stats(tokens_used, cost)
            
            response_time = time.time() - start_time
            
            return APIResponse(
                success=True,
                data=generated_text,
                response_time=response_time,
                tokens_used=tokens_used,
                cost=cost,
                provider=self.provider_name,
                model=model,
            )
            
        except Exception as e:
            error_type, retryable = self._classify_error(e)
            return APIResponse(
                success=False,
                error=str(e),
                error_type=error_type,
                response_time=time.time() - start_time,
                provider=self.provider_name,
                model=model,
            )    
    def generate_streaming(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> Iterator[str]:
        """Generate text with streaming response.
        
        Args:
            prompt: Input prompt
            model: Optional model override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
            
        Yields:
            Chunks of generated text
        """
        model = model or self.generation_model
        
        try:
            # Handle rate limiting
            self._handle_rate_limit()
            
            # Prepare messages
            messages = [{"role": "user", "content": prompt}]
            
            # Make streaming API request
            stream = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
                **kwargs
            )
            
            # Yield chunks as they arrive
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Streaming generation error: {e}")
            yield f"Error: {str(e)}"
    
    def health_check(self) -> APIResponse:
        """Check API health by making a simple request.
        
        Returns:
            APIResponse indicating health status
        """
        start_time = time.time()
        
        try:
            # Simple embedding request to test connectivity
            response = self.client.embeddings.create(
                input=["health check"],
                model=self.embedding_model,
            )
            
            response_time = time.time() - start_time
            
            return APIResponse(
                success=True,
                data={"status": "healthy", "model": self.embedding_model},
                response_time=response_time,
                provider=self.provider_name,
            )
            
        except Exception as e:
            error_type, retryable = self._classify_error(e)
            return APIResponse(
                success=False,
                error=str(e),
                error_type=error_type,
                response_time=time.time() - start_time,
                provider=self.provider_name,
            )    
    def _calculate_embedding_cost(self, tokens_used: int, model: str) -> float:
        """Calculate cost for embedding operation.
        
        Args:
            tokens_used: Number of tokens used
            model: Model name
            
        Returns:
            Cost in USD
        """
        provider_costs = DEFAULT_EMBEDDING_COSTS.get(self.provider_name, {})
        cost_per_1k = provider_costs.get(model, 0.00002)  # Default OpenAI cost
        return (tokens_used / 1000) * cost_per_1k
    
    def _calculate_generation_cost(self, tokens_used: int, model: str) -> float:
        """Calculate cost for generation operation.
        
        Args:
            tokens_used: Number of tokens used
            model: Model name
            
        Returns:
            Cost in USD
        """
        provider_costs = DEFAULT_GENERATION_COSTS.get(self.provider_name, {})
        cost_per_1k = provider_costs.get(model, 0.00015)  # Default OpenAI cost
        return (tokens_used / 1000) * cost_per_1k
    
    def _classify_error(self, error: Exception) -> tuple[APIErrorType, bool]:
        """Classify error type and determine if retryable.
        
        Args:
            error: Exception that occurred
            
        Returns:
            Tuple of (error_type, is_retryable)
        """
        error_str = str(error).lower()
        
        if "unauthorized" in error_str or "invalid api key" in error_str:
            return APIErrorType.AUTHENTICATION, False
        elif "rate limit" in error_str or "too many requests" in error_str:
            return APIErrorType.RATE_LIMIT, True
        elif "quota" in error_str or "billing" in error_str:
            return APIErrorType.QUOTA_EXCEEDED, False
        elif "timeout" in error_str or "connection" in error_str:
            return APIErrorType.NETWORK, True
        elif "500" in error_str or "502" in error_str or "503" in error_str:
            return APIErrorType.SERVER_ERROR, True
        elif "400" in error_str or "invalid" in error_str:
            return APIErrorType.VALIDATION, False
        else:
            return APIErrorType.UNKNOWN, True
    
    def _validate_api_key(self) -> bool:
        """Validate OpenAI API key format.
        
        Returns:
            True if API key appears valid
        """
        if not self.api_key:
            return False
        
        # OpenAI API keys start with 'sk-' and are 51 characters long
        return self.api_key.startswith('sk-') and len(self.api_key) == 51