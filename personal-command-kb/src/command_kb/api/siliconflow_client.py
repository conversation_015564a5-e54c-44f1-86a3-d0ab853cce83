"""
SiliconFlow API client implementation.

SiliconFlow provides OpenAI-compatible API endpoints with competitive pricing.
This client extends the OpenAI client with SiliconFlow-specific configurations.
"""

import logging
from typing import Optional

from .openai_client import OpenAIClient
from ..constants import DEFAULT_EMBEDDING_COSTS, DEFAULT_GENERATION_COSTS

logger = logging.getLogger(__name__)


class SiliconFlowClient(OpenAIClient):
    """SiliconFlow API client implementation.
    
    SiliconFlow provides OpenAI-compatible APIs with better pricing.
    This client inherits from OpenAIClient and overrides SiliconFlow-specific behavior.
    """
    
    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.siliconflow.cn/v1",
        timeout: int = 30,
        max_retries: int = 3,
        rate_limit_rpm: int = 200,  # SiliconFlow has lower rate limits
        embedding_model: str = "text-embedding-3-small",
        generation_model: str = "gpt-4o-mini",
    ):
        """Initialize SiliconFlow client.
        
        Args:
            api_key: SiliconFlow API key
            base_url: SiliconFlow API base URL
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
            rate_limit_rpm: Rate limit (SiliconFlow: ~200 RPM)
            embedding_model: Default embedding model
            generation_model: Default generation model
        """
        super().__init__(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout,
            max_retries=max_retries,
            rate_limit_rpm=rate_limit_rpm,
            provider_name="siliconflow",
            embedding_model=embedding_model,
            generation_model=generation_model,
        )
        
        logger.info("Initialized SiliconFlow API client")
    
    def _calculate_embedding_cost(self, tokens_used: int, model: str) -> float:
        """Calculate cost for SiliconFlow embedding operation.
        
        SiliconFlow offers competitive pricing compared to OpenAI.
        
        Args:
            tokens_used: Number of tokens used
            model: Model name
            
        Returns:
            Cost in USD
        """
        # SiliconFlow pricing (approximately 1/3 of OpenAI pricing)
        siliconflow_costs = DEFAULT_EMBEDDING_COSTS.get("siliconflow", {})
        cost_per_1k = siliconflow_costs.get(model, 0.000007)  # Default SiliconFlow cost
        return (tokens_used / 1000) * cost_per_1k    
        
    def _calculate_generation_cost(self, tokens_used: int, model: str) -> float:
        """Calculate cost for SiliconFlow generation operation.
        
        Args:
            tokens_used: Number of tokens used
            model: Model name
            
        Returns:
            Cost in USD
        """
        # SiliconFlow pricing (approximately 1/4 of OpenAI pricing)
        siliconflow_costs = DEFAULT_GENERATION_COSTS.get("siliconflow", {})
        cost_per_1k = siliconflow_costs.get(model, 0.000042)  # Default SiliconFlow cost
        return (tokens_used / 1000) * cost_per_1k
    
    def _validate_api_key(self) -> bool:
        """Validate SiliconFlow API key format.
        
        SiliconFlow uses OpenAI-compatible API key format.
        
        Returns:
            True if API key appears valid
        """
        if not self.api_key:
            return False
        
        # SiliconFlow API keys follow OpenAI format: sk-xxx
        return self.api_key.startswith('sk-') and len(self.api_key) >= 20
    
    def health_check(self):
        """Check SiliconFlow API health.
        
        Override to use SiliconFlow-specific health check if needed.
        
        Returns:
            APIResponse indicating health status
        """
        # Use parent implementation but with SiliconFlow-specific logging
        logger.debug("Performing SiliconFlow API health check")
        response = super().health_check()
        
        if response.success:
            logger.info("SiliconFlow API health check passed")
        else:
            logger.warning(f"SiliconFlow API health check failed: {response.error}")
            
        return response