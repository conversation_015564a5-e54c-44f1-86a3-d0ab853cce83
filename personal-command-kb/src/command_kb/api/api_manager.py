"""
API Manager for high-availability multi-provider support.

This module provides the core API management functionality:
- Load balancing across multiple providers
- Automatic failover and fallback
- Health monitoring and circuit breaking
- Cost optimization and tracking
- Unified interface for all providers
"""

import logging
import time
from typing import Dict, List, Optional, Any, Iterator
from dataclasses import dataclass
from enum import Enum

from .base_client import BaseAPIClient, APIResponse, APIError, APIErrorType
from .openai_client import OpenAIClient
from .siliconflow_client import SiliconFlowClient
from .zhipu_client import ZhipuClient
from .retry_handler import <PERSON><PERSON><PERSON>and<PERSON>, RetryConfig
from .rate_limiter import RateLimiter, RateLimitConfig
from ..config import AppConfig, APIProviderConfig

logger = logging.getLogger(__name__)


class ProviderStatus(Enum):
    """Status of an API provider."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    DISABLED = "disabled"


@dataclass
class ProviderHealth:
    """Health information for an API provider."""
    status: ProviderStatus
    last_check: float
    success_rate: float
    avg_response_time: float
    error_count: int
    consecutive_failures: int
    last_error: Optional[str] = None


class APIManager:
    """High-availability API manager with multi-provider support.
    
    Provides unified interface to multiple API providers with:
    - Automatic load balancing
    - Health monitoring and failover
    - Cost optimization
    - Rate limiting and retry handling
    """
    
    def __init__(self, config: AppConfig):
        """Initialize API manager.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.clients: Dict[str, BaseAPIClient] = {}
        self.health_status: Dict[str, ProviderHealth] = {}
        self.retry_handler = RetryHandler()
        self.rate_limiter = RateLimiter()
        
        # Statistics
        self.total_requests = 0
        self.total_cost = 0.0
        self.provider_usage: Dict[str, int] = {}
        
        # Initialize providers
        self._initialize_providers()
        self._setup_rate_limiting()
        
        logger.info(f"Initialized API manager with {len(self.clients)} providers")
    
    # 初始化提供商
    def _initialize_providers(self) -> None:
        """Initialize API provider clients."""
        for provider_name, provider_config in self.config.api_providers.items():
            if not provider_config.enabled:
                logger.info(f"Skipping disabled provider: {provider_name}")
                continue
                
            try:
                client = self._create_client(provider_name, provider_config)
                self.clients[provider_name] = client
                
                # Initialize health status
                self.health_status[provider_name] = ProviderHealth(
                    status=ProviderStatus.HEALTHY,
                    last_check=time.time(),
                    success_rate=1.0,
                    avg_response_time=0.0,
                    error_count=0,
                    consecutive_failures=0,
                )
                
                # Initialize usage tracking
                self.provider_usage[provider_name] = 0
                
                logger.info(f"Initialized provider: {provider_name}")
                
            except Exception as e:
                logger.error(f"Failed to initialize provider {provider_name}: {e}")
                self.health_status[provider_name] = ProviderHealth(
                    status=ProviderStatus.UNHEALTHY,
                    last_check=time.time(),
                    success_rate=0.0,
                    avg_response_time=0.0,
                    error_count=1,
                    consecutive_failures=1,
                    last_error=str(e),
                )    
    def _create_client(self, provider_name: str, config: APIProviderConfig) -> BaseAPIClient:
        """Create API client for a provider.
        
        Args:
            provider_name: Name of the provider
            config: Provider configuration
            
        Returns:
            Initialized API client
        """
        if provider_name == "openai":
            return OpenAIClient(
                api_key=config.api_key,
                base_url=config.base_url,
                timeout=config.timeout,
                max_retries=config.max_retries,
                rate_limit_rpm=config.rate_limit_rpm,
                provider_name=provider_name,
                embedding_model=config.embedding_model,
                generation_model=config.generation_model,
            )
        elif provider_name == "siliconflow":
            return SiliconFlowClient(
                api_key=config.api_key,
                base_url=config.base_url,
                timeout=config.timeout,
                max_retries=config.max_retries,
                rate_limit_rpm=config.rate_limit_rpm,
                embedding_model=config.embedding_model,
                generation_model=config.generation_model,
            )
        elif provider_name == "zhipu":
            return ZhipuClient(
                api_key=config.api_key,
                base_url=config.base_url,
                timeout=config.timeout,
                max_retries=config.max_retries,
                rate_limit_rpm=config.rate_limit_rpm,
                embedding_model=config.embedding_model,
                generation_model=config.generation_model,
            )
        else:
            raise ValueError(f"Unsupported provider: {provider_name}")
    
    def _setup_rate_limiting(self) -> None:
        """Setup rate limiting for all providers."""
        for provider_name, provider_config in self.config.api_providers.items():
            if provider_config.enabled:
                rate_config = RateLimitConfig(
                    requests_per_minute=provider_config.rate_limit_rpm,
                    burst_size=max(10, provider_config.rate_limit_rpm // 6),
                )
                self.rate_limiter.add_provider(provider_name, rate_config)
    
    def _select_provider(self, operation: str = "default") -> Optional[str]:
        """Select the best available provider for an operation.
        
        Args:
            operation: Type of operation (embedding, generation, etc.)
            
        Returns:
            Selected provider name or None if none available
        """
        # Get healthy providers
        healthy_providers = [
            name for name, health in self.health_status.items()
            if health.status in [ProviderStatus.HEALTHY, ProviderStatus.DEGRADED]
            and name in self.clients
        ]
        
        if not healthy_providers:
            logger.error("No healthy providers available")
            return None
        
        # Prefer primary provider if healthy
        if (self.config.primary_provider in healthy_providers and
            self.health_status[self.config.primary_provider].status == ProviderStatus.HEALTHY):
            return self.config.primary_provider
        
        # Use fallback providers in order
        for fallback in self.config.fallback_providers:
            if fallback in healthy_providers:
                return fallback
        
        # Use any healthy provider
        return healthy_providers[0]    
    def embed_text(self, text: str, model: Optional[str] = None) -> APIResponse:
        """Generate embedding for text with automatic provider selection.
        
        Args:
            text: Text to embed
            model: Optional model override
            
        Returns:
            APIResponse with embedding
        """
        return self.embed_texts([text], model)
    
    def embed_texts(self, texts: List[str], model: Optional[str] = None) -> APIResponse:
        """Generate embeddings for multiple texts with failover.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            
        Returns:
            APIResponse with embeddings
        """
        if not texts:
            return APIResponse(
                success=False,
                error="No texts provided",
                error_type=APIErrorType.VALIDATION,
            )
        
        # Check cost limits
        if not self._check_cost_limits():
            return APIResponse(
                success=False,
                error="Daily cost limit exceeded",
                error_type=APIErrorType.QUOTA_EXCEEDED,
            )
        
        # Try providers with fallback
        providers_to_try = self._get_provider_fallback_order()
        last_error = None
        
        for provider_name in providers_to_try:
            try:
                # Check rate limits
                if not self.rate_limiter.acquire(provider_name):
                    self.rate_limiter.wait_if_needed(provider_name)
                
                client = self.clients[provider_name]
                
                # Make API call with retry
                response = self.retry_handler.retry_with_backoff(
                    client.embed_texts, texts, model
                )
                
                if response.success:
                    # Update statistics
                    self._update_provider_stats(provider_name, response, True)
                    self.total_requests += 1
                    self.total_cost += response.cost
                    self.provider_usage[provider_name] += 1
                    
                    logger.debug(f"Embedding successful with {provider_name}")
                    return response
                else:
                    # Handle API error
                    self._update_provider_stats(provider_name, response, False)
                    last_error = response.error
                    
                    # Don't try other providers for non-retryable errors
                    if response.error_type in [APIErrorType.AUTHENTICATION, APIErrorType.VALIDATION]:
                        break
                        
            except Exception as e:
                logger.warning(f"Provider {provider_name} failed: {e}")
                self._update_provider_health(provider_name, False, str(e))
                last_error = str(e)
        
        # All providers failed
        return APIResponse(
            success=False,
            error=f"All providers failed. Last error: {last_error}",
            error_type=APIErrorType.SERVER_ERROR,
        )   
    def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> APIResponse:
        """Generate text with automatic provider selection and failover.
        
        Args:
            prompt: Input prompt
            model: Optional model override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
            
        Returns:
            APIResponse with generated text
        """
        # Check cost limits
        if not self._check_cost_limits():
            return APIResponse(
                success=False,
                error="Daily cost limit exceeded",
                error_type=APIErrorType.QUOTA_EXCEEDED,
            )
        
        # Try providers with fallback
        providers_to_try = self._get_provider_fallback_order()
        last_error = None
        
        for provider_name in providers_to_try:
            try:
                # Check rate limits
                if not self.rate_limiter.acquire(provider_name):
                    self.rate_limiter.wait_if_needed(provider_name)
                
                client = self.clients[provider_name]
                
                # Make API call with retry
                response = self.retry_handler.retry_with_backoff(
                    client.generate_text, prompt, model, max_tokens, temperature, **kwargs
                )
                
                if response.success:
                    # Update statistics
                    self._update_provider_stats(provider_name, response, True)
                    self.total_requests += 1
                    self.total_cost += response.cost
                    self.provider_usage[provider_name] += 1
                    
                    logger.debug(f"Generation successful with {provider_name}")
                    return response
                else:
                    # Handle API error
                    self._update_provider_stats(provider_name, response, False)
                    last_error = response.error
                    
                    # Don't try other providers for non-retryable errors
                    if response.error_type in [APIErrorType.AUTHENTICATION, APIErrorType.VALIDATION]:
                        break
                        
            except Exception as e:
                logger.warning(f"Provider {provider_name} failed: {e}")
                self._update_provider_health(provider_name, False, str(e))
                last_error = str(e)
        
        # All providers failed
        return APIResponse(
            success=False,
            error=f"All providers failed. Last error: {last_error}",
            error_type=APIErrorType.SERVER_ERROR,
        )  
    def generate_streaming(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> Iterator[str]:
        """Generate text with streaming response and failover.
        
        Args:
            prompt: Input prompt
            model: Optional model override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
            
        Yields:
            Chunks of generated text
        """
        # Check cost limits
        if not self._check_cost_limits():
            yield "Error: Daily cost limit exceeded"
            return
        
        # Try providers with fallback
        providers_to_try = self._get_provider_fallback_order()
        
        for provider_name in providers_to_try:
            try:
                # Check rate limits
                if not self.rate_limiter.acquire(provider_name):
                    self.rate_limiter.wait_if_needed(provider_name)
                
                client = self.clients[provider_name]
                
                # Make streaming API call
                for chunk in client.generate_streaming(
                    prompt, model, max_tokens, temperature, **kwargs
                ):
                    yield chunk
                
                # If we get here, streaming was successful
                self._update_provider_health(provider_name, True)
                self.provider_usage[provider_name] += 1
                return
                
            except Exception as e:
                logger.warning(f"Streaming failed with {provider_name}: {e}")
                self._update_provider_health(provider_name, False, str(e))
                continue
        
        # All providers failed
        yield "Error: All providers failed for streaming generation"
    
    def health_check(self, provider_name: Optional[str] = None) -> Dict[str, APIResponse]:
        """Perform health check on providers.
        
        Args:
            provider_name: Specific provider to check, or None for all
            
        Returns:
            Dictionary of health check results
        """
        results = {}
        
        providers_to_check = [provider_name] if provider_name else list(self.clients.keys())
        
        for name in providers_to_check:
            if name not in self.clients:
                continue
                
            try:
                client = self.clients[name]
                response = client.health_check()
                results[name] = response
                
                # Update health status
                self._update_provider_health(name, response.success, response.error)
                
            except Exception as e:
                results[name] = APIResponse(
                    success=False,
                    error=str(e),
                    provider=name,
                )
                self._update_provider_health(name, False, str(e))
        
        return results    
    def _get_provider_fallback_order(self) -> List[str]:
        """Get ordered list of providers to try.
        
        Returns:
            List of provider names in fallback order
        """
        providers = []
        
        # Start with primary provider if healthy
        if (self.config.primary_provider in self.clients and
            self.health_status[self.config.primary_provider].status != ProviderStatus.UNHEALTHY):
            providers.append(self.config.primary_provider)
        
        # Add fallback providers
        for fallback in self.config.fallback_providers:
            if (fallback in self.clients and 
                fallback not in providers and
                self.health_status[fallback].status != ProviderStatus.UNHEALTHY):
                providers.append(fallback)
        
        # Add any remaining healthy providers
        for name in self.clients.keys():
            if (name not in providers and
                self.health_status[name].status != ProviderStatus.UNHEALTHY):
                providers.append(name)
        
        return providers
    
    def _check_cost_limits(self) -> bool:
        """Check if daily cost limits are exceeded.
        
        Returns:
            True if within limits, False if exceeded
        """
        if not self.config.cost_control.enable_tracking:
            return True
        
        daily_limit = self.config.cost_control.daily_limit
        warning_threshold = self.config.cost_control.warning_threshold
        
        if self.total_cost >= daily_limit:
            logger.error(f"Daily cost limit exceeded: ${self.total_cost:.4f} >= ${daily_limit}")
            return False
        
        if self.total_cost >= daily_limit * warning_threshold:
            logger.warning(
                f"Cost warning: ${self.total_cost:.4f} >= ${daily_limit * warning_threshold:.4f} "
                f"({warning_threshold*100:.0f}% of daily limit)"
            )
        
        return True
    
    def _update_provider_stats(self, provider_name: str, response: APIResponse, success: bool) -> None:
        """Update provider statistics.
        
        Args:
            provider_name: Name of the provider
            response: API response
            success: Whether the request was successful
        """
        health = self.health_status.get(provider_name)
        if not health:
            return
        
        # Update response time
        if response.response_time > 0:
            # Simple moving average
            alpha = 0.1
            health.avg_response_time = (
                alpha * response.response_time + 
                (1 - alpha) * health.avg_response_time
            )
        
        # Update success rate
        if success:
            health.consecutive_failures = 0
            health.success_rate = min(1.0, health.success_rate + 0.01)
        else:
            health.error_count += 1
            health.consecutive_failures += 1
            health.success_rate = max(0.0, health.success_rate - 0.05)
            health.last_error = response.error
        
        # Update health status based on metrics
        self._update_health_status(provider_name)    
    def _update_provider_health(self, provider_name: str, success: bool, error: Optional[str] = None) -> None:
        """Update provider health status.
        
        Args:
            provider_name: Name of the provider
            success: Whether the operation was successful
            error: Error message if failed
        """
        health = self.health_status.get(provider_name)
        if not health:
            return
        
        health.last_check = time.time()
        
        if success:
            health.consecutive_failures = 0
            health.success_rate = min(1.0, health.success_rate + 0.01)
        else:
            health.error_count += 1
            health.consecutive_failures += 1
            health.success_rate = max(0.0, health.success_rate - 0.05)
            health.last_error = error
        
        self._update_health_status(provider_name)
    
    def _update_health_status(self, provider_name: str) -> None:
        """Update overall health status for a provider.
        
        Args:
            provider_name: Name of the provider
        """
        health = self.health_status.get(provider_name)
        if not health:
            return
        
        # Determine status based on metrics
        if health.consecutive_failures >= 5:
            health.status = ProviderStatus.UNHEALTHY
        elif health.success_rate < 0.5 or health.consecutive_failures >= 3:
            health.status = ProviderStatus.DEGRADED
        elif health.success_rate >= 0.9:
            health.status = ProviderStatus.HEALTHY
        
        logger.debug(
            f"Provider {provider_name} status: {health.status.value} "
            f"(success_rate: {health.success_rate:.2f}, "
            f"consecutive_failures: {health.consecutive_failures})"
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive API manager statistics.
        
        Returns:
            Dictionary with statistics
        """
        return {
            "total_requests": self.total_requests,
            "total_cost": self.total_cost,
            "provider_usage": self.provider_usage.copy(),
            "provider_health": {
                name: {
                    "status": health.status.value,
                    "success_rate": health.success_rate,
                    "avg_response_time": health.avg_response_time,
                    "error_count": health.error_count,
                    "consecutive_failures": health.consecutive_failures,
                    "last_error": health.last_error,
                }
                for name, health in self.health_status.items()
            },
            "rate_limiter_stats": self.rate_limiter.get_stats(),
        }
    
    def reset_stats(self) -> None:
        """Reset all statistics."""
        self.total_requests = 0
        self.total_cost = 0.0
        self.provider_usage = {name: 0 for name in self.clients.keys()}
        
        # Reset health statistics but keep status
        for health in self.health_status.values():
            health.error_count = 0
            health.consecutive_failures = 0
            health.success_rate = 1.0
            health.avg_response_time = 0.0
            health.last_error = None
        
        logger.info("API manager statistics reset")