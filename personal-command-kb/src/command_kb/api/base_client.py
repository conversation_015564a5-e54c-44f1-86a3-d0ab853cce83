"""
Base API client interface for multi-provider support.

This module defines the abstract base class and common interfaces for all API providers.
All API clients must implement this interface to ensure consistent behavior across
different providers.
"""

import time
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union, Iterator
from enum import Enum

logger = logging.getLogger(__name__)


class APIErrorType(Enum):
    """Types of API errors for categorization."""
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    QUOTA_EXCEEDED = "quota_exceeded"
    NETWORK = "network"
    SERVER_ERROR = "server_error"
    VALIDATION = "validation"
    UNKNOWN = "unknown"


@dataclass
class APIResponse:
    """Standardized API response container."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    error_type: Optional[APIErrorType] = None
    status_code: Optional[int] = None
    response_time: float = 0.0
    tokens_used: int = 0
    cost: float = 0.0
    provider: str = ""
    model: str = ""
    cached: bool = False


class APIError(Exception):
    """Custom exception for API-related errors."""
    
    def __init__(
        self, 
        message: str, 
        error_type: APIErrorType = APIErrorType.UNKNOWN,
        status_code: Optional[int] = None,
        provider: str = "",
        retryable: bool = False
    ):
        super().__init__(message)
        self.error_type = error_type
        self.status_code = status_code
        self.provider = provider
        self.retryable = retryable


class BaseAPIClient(ABC):
    """Abstract base class for all API clients.
    
    This class defines the common interface that all API providers must implement.
    It provides standardized methods for embedding, generation, and health checks.
    """
    
    def __init__(
        self,
        api_key: str,
        base_url: str,
        timeout: int = 30,
        max_retries: int = 3,
        rate_limit_rpm: int = 60,
        provider_name: str = "",
    ):
        """Initialize the API client.
        
        Args:
            api_key: API key for authentication
            base_url: Base URL for the API endpoint
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            rate_limit_rpm: Rate limit in requests per minute
            provider_name: Name of the API provider
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.rate_limit_rpm = rate_limit_rpm
        self.provider_name = provider_name
        
        # Internal state
        self._last_request_time = 0.0
        self._request_count = 0
        self._total_tokens_used = 0
        self._total_cost = 0.0
        
        logger.info(f"Initialized {provider_name} API client")
    
    @abstractmethod
    def embed_text(self, text: str, model: Optional[str] = None) -> APIResponse:
        """Generate embeddings for the given text.
        
        Args:
            text: Text to embed
            model: Optional model name override
            
        Returns:
            APIResponse containing embedding vector
        """
        pass
    
    @abstractmethod
    def embed_texts(self, texts: List[str], model: Optional[str] = None) -> APIResponse:
        """Generate embeddings for multiple texts (batch processing).
        
        Args:
            texts: List of texts to embed
            model: Optional model name override
            
        Returns:
            APIResponse containing list of embedding vectors
        """
        pass

    @abstractmethod
    def generate_text(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> APIResponse:
        """Generate text based on the given prompt.
        
        Args:
            prompt: Input prompt for text generation
            model: Optional model name override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional model-specific parameters
            
        Returns:
            APIResponse containing generated text
        """
        pass
    
    @abstractmethod
    def generate_streaming(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> Iterator[str]:
        """Generate text with streaming response.
        
        Args:
            prompt: Input prompt for text generation
            model: Optional model name override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional model-specific parameters
            
        Yields:
            Chunks of generated text
        """
        pass
    
    @abstractmethod
    def health_check(self) -> APIResponse:
        """Check the health and availability of the API.
        
        Returns:
            APIResponse indicating API health status
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get usage statistics for this client.
        
        Returns:
            Dictionary containing usage statistics
        """
        return {
            "provider": self.provider_name,
            "total_requests": self._request_count,
            "total_tokens_used": self._total_tokens_used,
            "total_cost": self._total_cost,
            "last_request_time": self._last_request_time,
        }

    def _update_stats(self, tokens_used: int, cost: float) -> None:
        """Update internal usage statistics.
        
        Args:
            tokens_used: Number of tokens used in the request
            cost: Cost of the request in USD
        """
        self._request_count += 1
        self._total_tokens_used += tokens_used
        self._total_cost += cost
        self._last_request_time = time.time()
    
    def _calculate_cost(self, tokens_used: int, model: str, operation: str) -> float:
        """Calculate the cost of an API operation.
        
        Args:
            tokens_used: Number of tokens used
            model: Model name used
            operation: Type of operation (embedding, generation)
            
        Returns:
            Cost in USD
        """
        # This is a base implementation - subclasses should override
        # with provider-specific pricing
        base_cost_per_1k = 0.0001  # Default fallback cost
        return (tokens_used / 1000) * base_cost_per_1k
    
    def _validate_api_key(self) -> bool:
        """Validate the API key format.
        
        Returns:
            True if API key appears valid
        """
        if not self.api_key:
            return False
        
        # Basic validation - subclasses can override with provider-specific validation
        return len(self.api_key) > 10
    
    def _handle_rate_limit(self) -> None:
        """Handle rate limiting by adding appropriate delays."""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time
        
        # Calculate minimum time between requests
        min_interval = 60.0 / self.rate_limit_rpm
        
        if time_since_last_request < min_interval:
            sleep_time = min_interval - time_since_last_request
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
    
    def __str__(self) -> str:
        """String representation of the API client."""
        return f"{self.provider_name}APIClient(base_url={self.base_url})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the API client."""
        return (
            f"{self.__class__.__name__}("
            f"provider={self.provider_name}, "
            f"base_url={self.base_url}, "
            f"timeout={self.timeout}, "
            f"max_retries={self.max_retries})"
        )