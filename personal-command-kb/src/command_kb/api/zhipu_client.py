"""
Zhipu AI API client implementation.

Zhipu AI (智谱AI) provides Chinese-optimized language models with competitive pricing.
This client implements the Zhipu AI API interface following their specific protocol.
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional, Iterator

import requests

from .base_client import BaseAPIClient, APIResponse, APIError, APIErrorType
from ..constants import DEFAULT_EMBEDDING_COSTS, DEFAULT_GENERATION_COSTS

logger = logging.getLogger(__name__)


class ZhipuClient(BaseAPIClient):
    """Zhipu AI API client implementation.
    
    Implements Zhipu AI's specific API protocol for embeddings and text generation.
    Provides cost-effective Chinese language model access.
    """
    
    def __init__(
        self,
        api_key: str,
        base_url: str = "https://open.bigmodel.cn/api/paas/v4",
        timeout: int = 30,
        max_retries: int = 3,
        rate_limit_rpm: int = 100,  # Zhipu has conservative rate limits
        embedding_model: str = "embedding-2",
        generation_model: str = "glm-4-flash",
    ):
        """Initialize Zhipu AI client.
        
        Args:
            api_key: Zhipu AI API key
            base_url: Zhipu AI API base URL
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
            rate_limit_rpm: Rate limit in requests per minute
            embedding_model: Default embedding model
            generation_model: Default generation model
        """
        super().__init__(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout,
            max_retries=max_retries,
            rate_limit_rpm=rate_limit_rpm,
            provider_name="zhipu",
        )
        
        self.embedding_model = embedding_model
        self.generation_model = generation_model
        
        # Zhipu API uses different authentication
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        
        logger.info("Initialized Zhipu AI client")
    
    def embed_text(self, text: str, model: Optional[str] = None) -> APIResponse:
        """Generate embedding for a single text.
        
        Args:
            text: Text to embed
            model: Optional model override
            
        Returns:
            APIResponse with embedding vector
        """
        return self.embed_texts([text], model)   
    def embed_texts(self, texts: List[str], model: Optional[str] = None) -> APIResponse:
        """Generate embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            
        Returns:
            APIResponse with list of embedding vectors
        """
        if not texts:
            return APIResponse(
                success=False,
                error="No texts provided for embedding",
                error_type=APIErrorType.VALIDATION,
                provider=self.provider_name,
            )
        
        model = model or self.embedding_model
        start_time = time.time()
        
        try:
            # Handle rate limiting
            self._handle_rate_limit()
            
            # Prepare request payload for Zhipu API
            payload = {
                "model": model,
                "input": texts,
            }
            
            # Make API request
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers=self.headers,
                json=payload,
                timeout=self.timeout,
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Extract embeddings
            embeddings = [item["embedding"] for item in result["data"]]
            tokens_used = result.get("usage", {}).get("total_tokens", 0)
            
            # Calculate cost
            cost = self._calculate_embedding_cost(tokens_used, model)
            
            # Update statistics
            self._update_stats(tokens_used, cost)
            
            response_time = time.time() - start_time
            
            return APIResponse(
                success=True,
                data=embeddings,
                response_time=response_time,
                tokens_used=tokens_used,
                cost=cost,
                provider=self.provider_name,
                model=model,
            )
            
        except Exception as e:
            error_type, retryable = self._classify_error(e)
            return APIResponse(
                success=False,
                error=str(e),
                error_type=error_type,
                response_time=time.time() - start_time,
                provider=self.provider_name,
                model=model,
            )    
    def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> APIResponse:
        """Generate text based on prompt.
        
        Args:
            prompt: Input prompt
            model: Optional model override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
            
        Returns:
            APIResponse with generated text
        """
        model = model or self.generation_model
        start_time = time.time()
        
        try:
            # Handle rate limiting
            self._handle_rate_limit()
            
            # Prepare request payload for Zhipu API
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }
            
            # Make API request
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout,
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Extract generated text
            generated_text = result["choices"][0]["message"]["content"]
            tokens_used = result.get("usage", {}).get("total_tokens", 0)
            
            # Calculate cost
            cost = self._calculate_generation_cost(tokens_used, model)
            
            # Update statistics
            self._update_stats(tokens_used, cost)
            
            response_time = time.time() - start_time
            
            return APIResponse(
                success=True,
                data=generated_text,
                response_time=response_time,
                tokens_used=tokens_used,
                cost=cost,
                provider=self.provider_name,
                model=model,
            )
            
        except Exception as e:
            error_type, retryable = self._classify_error(e)
            return APIResponse(
                success=False,
                error=str(e),
                error_type=error_type,
                response_time=time.time() - start_time,
                provider=self.provider_name,
                model=model,
            )    
    def generate_streaming(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> Iterator[str]:
        """Generate text with streaming response.
        
        Args:
            prompt: Input prompt
            model: Optional model override
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
            
        Yields:
            Chunks of generated text
        """
        model = model or self.generation_model
        
        try:
            # Handle rate limiting
            self._handle_rate_limit()
            
            # Prepare request payload with streaming
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True,
                **kwargs
            }
            
            # Make streaming API request
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout,
                stream=True,
            )
            
            response.raise_for_status()
            
            # Process streaming response
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]  # Remove 'data: ' prefix
                        if data.strip() == '[DONE]':
                            break
                        try:
                            chunk_data = json.loads(data)
                            if 'choices' in chunk_data and chunk_data['choices']:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Zhipu streaming generation error: {e}")
            yield f"Error: {str(e)}"    
    def health_check(self) -> APIResponse:
        """Check Zhipu AI API health.
        
        Returns:
            APIResponse indicating health status
        """
        start_time = time.time()
        
        try:
            # Simple embedding request to test connectivity
            payload = {
                "model": self.embedding_model,
                "input": ["health check"],
            }
            
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers=self.headers,
                json=payload,
                timeout=self.timeout,
            )
            
            response.raise_for_status()
            response_time = time.time() - start_time
            
            return APIResponse(
                success=True,
                data={"status": "healthy", "model": self.embedding_model},
                response_time=response_time,
                provider=self.provider_name,
            )
            
        except Exception as e:
            error_type, retryable = self._classify_error(e)
            return APIResponse(
                success=False,
                error=str(e),
                error_type=error_type,
                response_time=time.time() - start_time,
                provider=self.provider_name,
            )
    
    def _calculate_embedding_cost(self, tokens_used: int, model: str) -> float:
        """Calculate cost for Zhipu embedding operation."""
        zhipu_costs = DEFAULT_EMBEDDING_COSTS.get("zhipu", {})
        cost_per_1k = zhipu_costs.get(model, 0.00001)  # Default Zhipu cost
        return (tokens_used / 1000) * cost_per_1k
    
    def _calculate_generation_cost(self, tokens_used: int, model: str) -> float:
        """Calculate cost for Zhipu generation operation."""
        zhipu_costs = DEFAULT_GENERATION_COSTS.get("zhipu", {})
        cost_per_1k = zhipu_costs.get(model, 0.00001)  # Default Zhipu cost
        return (tokens_used / 1000) * cost_per_1k
    
    def _classify_error(self, error: Exception) -> tuple[APIErrorType, bool]:
        """Classify Zhipu API error type."""
        error_str = str(error).lower()
        
        if "unauthorized" in error_str or "invalid api key" in error_str:
            return APIErrorType.AUTHENTICATION, False
        elif "rate limit" in error_str or "too many requests" in error_str:
            return APIErrorType.RATE_LIMIT, True
        elif "quota" in error_str or "billing" in error_str:
            return APIErrorType.QUOTA_EXCEEDED, False
        elif "timeout" in error_str or "connection" in error_str:
            return APIErrorType.NETWORK, True
        elif "500" in error_str or "502" in error_str or "503" in error_str:
            return APIErrorType.SERVER_ERROR, True
        elif "400" in error_str or "invalid" in error_str:
            return APIErrorType.VALIDATION, False
        else:
            return APIErrorType.UNKNOWN, True
    
    def _validate_api_key(self) -> bool:
        """Validate Zhipu API key format."""
        if not self.api_key:
            return False
        
        # Zhipu API keys are typically 32-64 characters long
        return len(self.api_key) >= 32