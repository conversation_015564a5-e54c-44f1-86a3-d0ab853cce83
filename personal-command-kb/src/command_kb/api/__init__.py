"""
API integration module for multi-provider support.

This module provides high-availability API integration with multiple providers:
- OpenAI and OpenAI-compatible APIs
- SiliconFlow (OpenAI-compatible)
- Zhipu AI
- Moonshot

Features:
- Unified API interface
- Automatic retry and fallback
- Rate limiting and cost control
- Health monitoring
- Caching support
"""

from .base_client import BaseAPIClient, APIResponse, APIError
from .api_manager import APIManager

__all__ = [
    "BaseAPIClient",
    "APIResponse", 
    "APIError",
    "APIManager",
]