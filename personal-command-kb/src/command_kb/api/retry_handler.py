"""
Retry handler for API requests with exponential backoff.

This module provides intelligent retry mechanisms for API calls with:
- Exponential backoff strategy
- Jitter to prevent thundering herd
- Error classification for retry decisions
- Circuit breaker pattern
- Comprehensive logging
"""

import random
import time
import logging
from typing import Any, Callable, Optional, Tuple
from functools import wraps
from dataclasses import dataclass

from .base_client import APIError, APIErrorType

logger = logging.getLogger(__name__)


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True
    retryable_errors: set = None
    
    def __post_init__(self):
        if self.retryable_errors is None:
            self.retryable_errors = {
                APIErrorType.RATE_LIMIT,
                APIErrorType.NETWORK,
                APIErrorType.SERVER_ERROR,
                APIErrorType.UNKNOWN,
            }


class CircuitBreaker:
    """断路器以防止级联故障。"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: type = Exception,
    ):
        """Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before attempting recovery
            expected_exception: Exception type to monitor
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func: Callable) -> Callable:
        """Decorator to apply circuit breaker to function."""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise APIError(
                        "Circuit breaker is OPEN",
                        error_type=APIErrorType.SERVER_ERROR,
                        retryable=False
                    )
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.expected_exception as e:
                self._on_failure()
                raise e
        
        return wrapper    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self) -> None:
        """Handle successful operation."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self) -> None:
        """Handle failed operation."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class RetryHandler:
    """Handles retry logic with exponential backoff and jitter."""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        """Initialize retry handler.
        
        Args:
            config: Retry configuration, uses defaults if None
        """
        self.config = config or RetryConfig()
        self.circuit_breaker = CircuitBreaker()
    
    def retry_with_backoff(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """Execute function with retry and exponential backoff.
        
        Args:
            func: Function to execute
            *args: Positional arguments for function
            **kwargs: Keyword arguments for function
            
        Returns:
            Function result
            
        Raises:
            APIError: If all retry attempts fail
        """
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                # Apply circuit breaker
                protected_func = self.circuit_breaker(func)
                result = protected_func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"Retry succeeded on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if error is retryable
                if not self._is_retryable_error(e):
                    logger.error(f"Non-retryable error: {e}")
                    raise e
                
                # Don't sleep on the last attempt
                if attempt < self.config.max_attempts - 1:
                    delay = self._calculate_delay(attempt)
                    logger.warning(
                        f"Attempt {attempt + 1} failed: {e}. "
                        f"Retrying in {delay:.2f} seconds..."
                    )
                    time.sleep(delay)
                else:
                    logger.error(f"All {self.config.max_attempts} attempts failed")
        
        # All attempts failed
        raise last_exception    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for exponential backoff with jitter.
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        # Exponential backoff: base_delay * (backoff_factor ^ attempt)
        delay = self.config.base_delay * (self.config.backoff_factor ** attempt)
        
        # Cap at max_delay
        delay = min(delay, self.config.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.config.jitter:
            jitter_range = delay * 0.1  # 10% jitter
            jitter = random.uniform(-jitter_range, jitter_range)
            delay += jitter
        
        return max(0, delay)
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error is retryable.
        
        Args:
            error: Exception to check
            
        Returns:
            True if error should be retried
        """
        if isinstance(error, APIError):
            return error.error_type in self.config.retryable_errors
        
        # Check common retryable error patterns
        error_str = str(error).lower()
        retryable_patterns = [
            "timeout",
            "connection",
            "rate limit",
            "too many requests",
            "server error",
            "503",
            "502",
            "500",
        ]
        
        return any(pattern in error_str for pattern in retryable_patterns)
    
    def with_retry(self, config: Optional[RetryConfig] = None):
        """Decorator to add retry behavior to functions.
        
        Args:
            config: Optional retry configuration override
            
        Returns:
            Decorator function
        """
        retry_config = config or self.config
        
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                handler = RetryHandler(retry_config)
                return handler.retry_with_backoff(func, *args, **kwargs)
            return wrapper
        return decorator


# Convenience decorators with common configurations
def retry_on_api_error(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    backoff_factor: float = 2.0
):
    """Decorator for API error retry with exponential backoff.
    
    Args:
        max_attempts: Maximum retry attempts
        base_delay: Base delay in seconds
        backoff_factor: Exponential backoff factor
        
    Returns:
        Decorator function
    """
    config = RetryConfig(
        max_attempts=max_attempts,
        base_delay=base_delay,
        backoff_factor=backoff_factor,
    )
    handler = RetryHandler(config)
    return handler.with_retry()


def retry_on_rate_limit(max_attempts: int = 5, base_delay: float = 2.0):
    """Decorator specifically for rate limit errors.
    
    Args:
        max_attempts: Maximum retry attempts
        base_delay: Base delay in seconds
        
    Returns:
        Decorator function
    """
    config = RetryConfig(
        max_attempts=max_attempts,
        base_delay=base_delay,
        backoff_factor=2.0,
        retryable_errors={APIErrorType.RATE_LIMIT},
    )
    handler = RetryHandler(config)
    return handler.with_retry()