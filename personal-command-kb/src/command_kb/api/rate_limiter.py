"""
Rate limiter for API requests using token bucket algorithm.

This module provides rate limiting functionality to prevent API quota exhaustion:
- Token bucket algorithm for smooth rate limiting
- Burst capacity support
- Per-provider rate limiting
- Thread-safe implementation
- Monitoring and statistics
"""

import time
import threading
import logging
from typing import Dict, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    requests_per_minute: int = 60
    burst_size: int = 10
    window_size: int = 60  # seconds
    
    @property
    def requests_per_second(self) -> float:
        """Calculate requests per second from RPM."""
        return self.requests_per_minute / 60.0


class TokenBucket:
    """Token bucket implementation for rate limiting.
    
    The token bucket algorithm allows for burst traffic while maintaining
    an average rate limit over time.
    """
    
    def __init__(self, config: RateLimitConfig):
        """Initialize token bucket.
        
        Args:
            config: Rate limiting configuration
        """
        self.config = config
        self.capacity = config.burst_size
        self.tokens = float(config.burst_size)
        self.fill_rate = config.requests_per_second
        self.last_update = time.time()
        self.lock = threading.Lock()
        
        # Statistics
        self.total_requests = 0
        self.rejected_requests = 0
        self.last_rejection_time = None
    
    def acquire(self, tokens: int = 1) -> bool:
        """Attempt to acquire tokens from the bucket.
        
        Args:
            tokens: Number of tokens to acquire
            
        Returns:
            True if tokens were acquired, False if rate limited
        """
        with self.lock:
            now = time.time()
            
            # Add tokens based on elapsed time
            elapsed = now - self.last_update
            self.tokens = min(
                self.capacity,
                self.tokens + elapsed * self.fill_rate
            )
            self.last_update = now
            
            # Check if we have enough tokens
            if self.tokens >= tokens:
                self.tokens -= tokens
                self.total_requests += 1
                return True
            else:
                self.rejected_requests += 1
                self.last_rejection_time = now
                return False
    
    def wait_time(self, tokens: int = 1) -> float:
        """Calculate time to wait for tokens to be available.
        
        Args:
            tokens: Number of tokens needed
            
        Returns:
            Time to wait in seconds
        """
        with self.lock:
            if self.tokens >= tokens:
                return 0.0
            
            tokens_needed = tokens - self.tokens
            return tokens_needed / self.fill_rate
    
    def get_stats(self) -> Dict[str, any]:
        """Get rate limiting statistics.
        
        Returns:
            Dictionary with statistics
        """
        with self.lock:
            return {
                "total_requests": self.total_requests,
                "rejected_requests": self.rejected_requests,
                "rejection_rate": (
                    self.rejected_requests / max(1, self.total_requests)
                ),
                "current_tokens": self.tokens,
                "capacity": self.capacity,
                "fill_rate": self.fill_rate,
                "last_rejection_time": self.last_rejection_time,
            }
class RateLimiter:
    """Multi-provider rate limiter with token bucket algorithm."""
    
    def __init__(self):
        """Initialize rate limiter."""
        self.buckets: Dict[str, TokenBucket] = {}
        self.lock = threading.Lock()
    
    def add_provider(self, provider_name: str, config: RateLimitConfig) -> None:
        """Add a provider with rate limiting configuration.
        
        Args:
            provider_name: Name of the API provider
            config: Rate limiting configuration
        """
        with self.lock:
            self.buckets[provider_name] = TokenBucket(config)
            logger.info(
                f"Added rate limiter for {provider_name}: "
                f"{config.requests_per_minute} RPM, burst {config.burst_size}"
            )
    
    def acquire(self, provider_name: str, tokens: int = 1) -> bool:
        """Acquire tokens for a provider.
        
        Args:
            provider_name: Name of the API provider
            tokens: Number of tokens to acquire
            
        Returns:
            True if tokens were acquired, False if rate limited
        """
        bucket = self.buckets.get(provider_name)
        if not bucket:
            logger.warning(f"No rate limiter configured for {provider_name}")
            return True  # Allow request if no limiter configured
        
        acquired = bucket.acquire(tokens)
        if not acquired:
            logger.warning(f"Rate limit exceeded for {provider_name}")
        
        return acquired
    
    def wait_time(self, provider_name: str, tokens: int = 1) -> float:
        """Get wait time for tokens to be available.
        
        Args:
            provider_name: Name of the API provider
            tokens: Number of tokens needed
            
        Returns:
            Time to wait in seconds
        """
        bucket = self.buckets.get(provider_name)
        if not bucket:
            return 0.0
        
        return bucket.wait_time(tokens)
    
    def wait_if_needed(self, provider_name: str, tokens: int = 1) -> None:
        """Wait if rate limit would be exceeded.
        
        Args:
            provider_name: Name of the API provider
            tokens: Number of tokens needed
        """
        wait_time = self.wait_time(provider_name, tokens)
        if wait_time > 0:
            logger.info(f"Rate limiting: waiting {wait_time:.2f}s for {provider_name}")
            time.sleep(wait_time)
    
    def get_stats(self, provider_name: Optional[str] = None) -> Dict[str, any]:
        """Get rate limiting statistics.
        
        Args:
            provider_name: Specific provider name, or None for all providers
            
        Returns:
            Dictionary with statistics
        """
        if provider_name:
            bucket = self.buckets.get(provider_name)
            if bucket:
                return {provider_name: bucket.get_stats()}
            else:
                return {}
        else:
            return {
                name: bucket.get_stats()
                for name, bucket in self.buckets.items()
            }
class AdaptiveRateLimiter(RateLimiter):
    """Adaptive rate limiter that adjusts based on API responses.
    
    This limiter can automatically reduce rate limits when receiving
    rate limit errors and gradually increase them when successful.
    """
    
    def __init__(self, adaptation_factor: float = 0.5):
        """Initialize adaptive rate limiter.
        
        Args:
            adaptation_factor: Factor for rate limit adjustments (0.0-1.0)
        """
        super().__init__()
        self.adaptation_factor = adaptation_factor
        self.original_configs: Dict[str, RateLimitConfig] = {}
    
    def add_provider(self, provider_name: str, config: RateLimitConfig) -> None:
        """Add provider with adaptive rate limiting.
        
        Args:
            provider_name: Name of the API provider
            config: Initial rate limiting configuration
        """
        super().add_provider(provider_name, config)
        self.original_configs[provider_name] = config
    
    def report_rate_limit_error(self, provider_name: str) -> None:
        """Report a rate limit error to trigger adaptation.
        
        Args:
            provider_name: Name of the API provider
        """
        bucket = self.buckets.get(provider_name)
        if not bucket:
            return
        
        # Reduce rate limit
        new_rate = bucket.fill_rate * (1 - self.adaptation_factor)
        new_rpm = int(new_rate * 60)
        
        logger.warning(
            f"Adapting rate limit for {provider_name}: "
            f"{bucket.config.requests_per_minute} -> {new_rpm} RPM"
        )
        
        # Update configuration
        new_config = RateLimitConfig(
            requests_per_minute=new_rpm,
            burst_size=bucket.config.burst_size,
            window_size=bucket.config.window_size,
        )
        
        # Replace bucket with new configuration
        with self.lock:
            self.buckets[provider_name] = TokenBucket(new_config)
    
    def report_success(self, provider_name: str) -> None:
        """Report successful API call for potential rate limit increase.
        
        Args:
            provider_name: Name of the API provider
        """
        bucket = self.buckets.get(provider_name)
        original_config = self.original_configs.get(provider_name)
        
        if not bucket or not original_config:
            return
        
        # Only increase if below original rate
        if bucket.fill_rate < original_config.requests_per_second:
            # Gradually increase rate limit
            new_rate = min(
                original_config.requests_per_second,
                bucket.fill_rate * (1 + self.adaptation_factor * 0.1)
            )
            new_rpm = int(new_rate * 60)
            
            if new_rpm > bucket.config.requests_per_minute:
                logger.info(
                    f"Increasing rate limit for {provider_name}: "
                    f"{bucket.config.requests_per_minute} -> {new_rpm} RPM"
                )
                
                # Update configuration
                new_config = RateLimitConfig(
                    requests_per_minute=new_rpm,
                    burst_size=bucket.config.burst_size,
                    window_size=bucket.config.window_size,
                )
                
                # Replace bucket with new configuration
                with self.lock:
                    self.buckets[provider_name] = TokenBucket(new_config)


# Global rate limiter instance
_global_rate_limiter = RateLimiter()


def get_rate_limiter() -> RateLimiter:
    """Get the global rate limiter instance.
    
    Returns:
        Global rate limiter instance
    """
    return _global_rate_limiter


def configure_provider_rate_limit(
    provider_name: str,
    requests_per_minute: int,
    burst_size: int = 10
) -> None:
    """Configure rate limiting for a provider.
    
    Args:
        provider_name: Name of the API provider
        requests_per_minute: Maximum requests per minute
        burst_size: Maximum burst size
    """
    config = RateLimitConfig(
        requests_per_minute=requests_per_minute,
        burst_size=burst_size,
    )
    _global_rate_limiter.add_provider(provider_name, config)