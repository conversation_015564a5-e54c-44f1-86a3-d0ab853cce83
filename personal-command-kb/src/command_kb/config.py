"""
Configuration management for the Personal Command-Line Vector Knowledge Base.

This module handles loading and validation of configuration from multiple sources:
- Environment variables
- YAML configuration files  
- Command line arguments
- Default values from constants

Configuration is loaded in order of precedence:
1. Command line arguments (highest)
2. Environment variables
3. Configuration files
4. Default constants (lowest)
"""

import os
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field

import yaml
from dotenv import load_dotenv

from .constants import (
    DEFAULT_API_TIMEOUT,
    DEFAULT_MAX_RETRIES,
    DEFAULT_BACKOFF_FACTOR,
    DEFAULT_RATE_LIMIT_RPM,
    API_PROVIDERS,
    DEFAULT_API_ENDPOINTS,
    DEFAULT_EMBEDDING_MODELS,
    DEFAULT_GENERATION_MODELS,
    DEFAULT_DAILY_COST_LIMIT,
    DEFAULT_COST_WARNING_THRESHOLD,
    DEFAULT_COLLECTION_NAME,
    DEFAULT_VECTOR_DB_PATH,
    DEFAULT_CHUNK_SIZE,
    DEFAULT_CHUNK_OVERLAP,
    DEFAULT_TOP_K,
    DEFAULT_CACHE_TTL,
    DEFAULT_MAX_CACHE_SIZE,
    DEFAULT_LOG_LEVEL,
    DEFAULT_LOG_FILE,
)

logger = logging.getLogger(__name__)


@dataclass
class APIProviderConfig:
    """Configuration for a single API provider."""
    name: str
    api_key: str
    base_url: str
    embedding_model: str
    generation_model: str
    timeout: int = DEFAULT_API_TIMEOUT
    max_retries: int = DEFAULT_MAX_RETRIES
    rate_limit_rpm: int = DEFAULT_RATE_LIMIT_RPM
    enabled: bool = True

@dataclass  
class DatabaseConfig:
    """Configuration for vector database."""
    path: str = DEFAULT_VECTOR_DB_PATH
    collection_name: str = DEFAULT_COLLECTION_NAME
    distance_metric: str = "cosine"
    
@dataclass
class RetrievalConfig:
    """Configuration for retrieval settings."""
    top_k: int = DEFAULT_TOP_K
    similarity_threshold: float = 0.7
    chunk_size: int = DEFAULT_CHUNK_SIZE
    chunk_overlap: int = DEFAULT_CHUNK_OVERLAP
    enable_hybrid_search: bool = True

@dataclass
class GenerationConfig:
    """Configuration for text generation."""
    max_tokens: int = 1000
    temperature: float = 0.1
    enable_streaming: bool = True
    system_prompt: str = "You are a helpful assistant for command-line queries."

@dataclass
class CacheConfig:
    """Configuration for caching system."""
    enabled: bool = True
    ttl: int = DEFAULT_CACHE_TTL
    max_size: int = DEFAULT_MAX_CACHE_SIZE
    enable_embedding_cache: bool = True
    enable_generation_cache: bool = True
    enable_retrieval_cache: bool = True


@dataclass
class CostControlConfig:
    """Configuration for cost control and monitoring."""
    daily_limit: float = DEFAULT_DAILY_COST_LIMIT
    warning_threshold: float = DEFAULT_COST_WARNING_THRESHOLD
    enable_tracking: bool = True
    auto_fallback: bool = True
    alert_email: Optional[str] = None

@dataclass
class LoggingConfig:
    """Configuration for logging system."""
    level: str = DEFAULT_LOG_LEVEL
    file_path: str = DEFAULT_LOG_FILE
    max_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_structured: bool = True
    format_type: str = "json"

@dataclass
class MonitoringConfig:
    """Configuration for monitoring and health checks."""
    enabled: bool = True
    metrics_port: int = 8080
    health_check_interval: int = 300
    enable_performance_monitoring: bool = True
    slow_query_threshold: float = 2.0

@dataclass
class SecurityConfig:
    """Configuration for security settings."""
    api_key_rotation_days: int = 90
    enable_api_key_monitoring: bool = True
    enable_data_anonymization: bool = True
    enable_sensitive_data_filtering: bool = True
    enable_audit_logging: bool = True
    audit_log_path: str = "./logs/audit.log"


@dataclass
class AppConfig:
    """Main application configuration container."""
    # API providers configuration
    api_providers: Dict[str, APIProviderConfig] = field(default_factory=dict)
    primary_provider: str = "siliconflow"
    fallback_providers: List[str] = field(default_factory=lambda: ["openai", "zhipu"])
    
    # Core components configuration
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    retrieval: RetrievalConfig = field(default_factory=RetrievalConfig)
    generation: GenerationConfig = field(default_factory=GenerationConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    cost_control: CostControlConfig = field(default_factory=CostControlConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    
    # Environment settings
    environment: str = "development"
    debug: bool = False
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate API providers
        if not self.api_providers:
            errors.append("No API providers configured")
        
        if self.primary_provider not in self.api_providers:
            errors.append(f"Primary provider '{self.primary_provider}' not configured")
            
        for provider_name, provider_config in self.api_providers.items():
            if not provider_config.api_key:
                errors.append(f"API key missing for provider '{provider_name}'")
            if not provider_config.base_url:
                errors.append(f"Base URL missing for provider '{provider_name}'")
                
        # Validate paths
        if not self.database.path:
            errors.append("Database path not configured")
            
        # Validate numeric values
        if self.retrieval.top_k <= 0:
            errors.append("Retrieval top_k must be positive")
        if self.retrieval.chunk_size <= 0:
            errors.append("Chunk size must be positive")
        if self.cost_control.daily_limit <= 0:
            errors.append("Daily cost limit must be positive")
            
        return errors
class ConfigLoader:
    """Configuration loader with multiple source support."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize configuration loader.
        
        Args:
            config_dir: Directory containing configuration files
        """

        logger.info("打印默认路径： {}".format(Path("./config")))

        self.config_dir = Path(config_dir) if config_dir else Path("./config") 
        self._load_environment()
    
    def _load_environment(self) -> None:
        """Load environment variables from .env file."""
        env_file = Path(".env")
        if env_file.exists():
            load_dotenv(env_file)
            logger.info(f"Loaded environment from {env_file}")
    
    def _load_yaml_config(self, filename: str) -> Dict[str, Any]:
        """Load configuration from YAML file.
        
        Args:
            filename: Name of YAML configuration file
            
        Returns:
            Dictionary containing configuration data
        """
        config_path = self.config_dir / filename
        if not config_path.exists():
            logger.warning(f"Configuration file not found: {config_path}")
            return {}
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f) or {}
            logger.info(f"Loaded configuration from {config_path}")
            return config_data
        except Exception as e:
            logger.error(f"Error loading configuration from {config_path}: {e}")
            return {}
    
    def _get_env_var(self, key: str, default: Any = None, 
                     var_type: type = str) -> Any:
        """Get environment variable with type conversion.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            var_type: Type to convert to
            
        Returns:
            Environment variable value or default
        """
        value = os.getenv(key, default)
        if value is None:
            return default
            
        try:
            if var_type == bool:
                return str(value).lower() in ('true', '1', 'yes', 'on')
            elif var_type == int:
                return int(value)
            elif var_type == float:
                return float(value)
            else:
                return str(value)
        except (ValueError, TypeError) as e:
            logger.warning(f"Error converting env var {key}={value} to {var_type}: {e}")
            return default    
    def _load_api_providers(self, config_data: Dict[str, Any]) -> Dict[str, APIProviderConfig]:
        """Load API provider configurations.
        
        Args:
            config_data: Configuration data from YAML files
            
        Returns:
            Dictionary of API provider configurations
        """
        providers = {}
        
        # Load from YAML configuration
        yaml_providers = config_data.get("api_providers", {})
        
        # Load each provider
        for provider_name in API_PROVIDERS.values():
            # Get configuration from YAML
            provider_yaml = yaml_providers.get(provider_name, {})
            
            # Get API key from environment (required)
            api_key_env = f"{provider_name.upper()}_API_KEY"
            api_key = self._get_env_var(api_key_env)
            
            if not api_key:
                logger.warning(f"API key not found for provider {provider_name}")
                continue
                
            # Get base URL
            base_url_env = f"{provider_name.upper()}_BASE_URL"
            base_url = (
                self._get_env_var(base_url_env) or
                provider_yaml.get("base_url") or
                DEFAULT_API_ENDPOINTS.get(provider_name)
            )
            
            # Get models (check environment variables first)
            embedding_model = (
                self._get_env_var("EMBEDDING_MODEL") or
                provider_yaml.get("embedding", {}).get("model") or
                DEFAULT_EMBEDDING_MODELS.get(provider_name)
            )
            generation_model = (
                self._get_env_var("GENERATION_MODEL") or
                provider_yaml.get("generation", {}).get("model") or
                DEFAULT_GENERATION_MODELS.get(provider_name)
            )
            
            # Create provider configuration
            providers[provider_name] = APIProviderConfig(
                name=provider_name,
                api_key=api_key,
                base_url=base_url,
                embedding_model=embedding_model,
                generation_model=generation_model,
                timeout=provider_yaml.get("timeout", DEFAULT_API_TIMEOUT),
                max_retries=provider_yaml.get("max_retries", DEFAULT_MAX_RETRIES),
                rate_limit_rpm=provider_yaml.get("rate_limit_rpm", DEFAULT_RATE_LIMIT_RPM),
                enabled=provider_yaml.get("enabled", True),
            )
            
        return providers    
    def load_config(self) -> AppConfig:
        """Load complete application configuration.
        
        Returns:
            Complete application configuration
        """
        # Load YAML configuration files
        main_config = self._load_yaml_config("config.yaml")
        api_config = self._load_yaml_config("api_providers.yaml")
        
        # Merge configurations
        config_data = {**main_config, **api_config}
        
        # Load API providers
        api_providers = self._load_api_providers(config_data)
        
        # Create main configuration
        app_config = AppConfig(
            api_providers=api_providers,
            primary_provider=self._get_env_var(
                "DEFAULT_API_PROVIDER", 
                config_data.get("primary_provider", "siliconflow")
            ),
            fallback_providers=config_data.get(
                "fallback_providers", 
                ["openai", "zhipu"]
            ),
            
            # Database configuration
            database=DatabaseConfig(
                path=self._get_env_var(
                    "VECTOR_DB_PATH",
                    config_data.get("database", {}).get("path", DEFAULT_VECTOR_DB_PATH)
                ),
                collection_name=self._get_env_var(
                    "VECTOR_DB_COLLECTION",
                    config_data.get("database", {}).get("collection_name", DEFAULT_COLLECTION_NAME)
                ),
            ),
            
            # Retrieval configuration
            retrieval=RetrievalConfig(
                top_k=self._get_env_var(
                    "RETRIEVAL_TOP_K",
                    config_data.get("retrieval", {}).get("top_k", DEFAULT_TOP_K),
                    int
                ),
                similarity_threshold=self._get_env_var(
                    "SIMILARITY_THRESHOLD",
                    config_data.get("retrieval", {}).get("similarity_threshold", 0.7),
                    float
                ),
                chunk_size=self._get_env_var(
                    "CHUNK_SIZE",
                    config_data.get("retrieval", {}).get("chunk_size", DEFAULT_CHUNK_SIZE),
                    int
                ),
                chunk_overlap=self._get_env_var(
                    "CHUNK_OVERLAP",
                    config_data.get("retrieval", {}).get("chunk_overlap", DEFAULT_CHUNK_OVERLAP),
                    int
                ),
                enable_hybrid_search=self._get_env_var(
                    "ENABLE_HYBRID_SEARCH",
                    config_data.get("retrieval", {}).get("enable_hybrid_search", True),
                    bool
                ),
            ),            # Generation configuration
            generation=GenerationConfig(
                max_tokens=self._get_env_var(
                    "MAX_TOKENS",
                    config_data.get("generation", {}).get("max_tokens", 1000),
                    int
                ),
                temperature=self._get_env_var(
                    "TEMPERATURE",
                    config_data.get("generation", {}).get("temperature", 0.1),
                    float
                ),
                enable_streaming=self._get_env_var(
                    "ENABLE_STREAMING",
                    config_data.get("generation", {}).get("enable_streaming", True),
                    bool
                ),
            ),
            
            # Cache configuration
            cache=CacheConfig(
                enabled=self._get_env_var(
                    "ENABLE_CACHE",
                    config_data.get("cache", {}).get("enabled", True),
                    bool
                ),
                ttl=self._get_env_var(
                    "CACHE_TTL",
                    config_data.get("cache", {}).get("ttl", DEFAULT_CACHE_TTL),
                    int
                ),
                max_size=self._get_env_var(
                    "MAX_CACHE_SIZE",
                    config_data.get("cache", {}).get("max_size", DEFAULT_MAX_CACHE_SIZE),
                    int
                ),
            ),
            
            # Cost control configuration
            cost_control=CostControlConfig(
                daily_limit=self._get_env_var(
                    "DAILY_COST_LIMIT",
                    config_data.get("cost_control", {}).get("daily_limit", DEFAULT_DAILY_COST_LIMIT),
                    float
                ),
                warning_threshold=self._get_env_var(
                    "COST_WARNING_THRESHOLD",
                    config_data.get("cost_control", {}).get("warning_threshold", DEFAULT_COST_WARNING_THRESHOLD),
                    float
                ),
                enable_tracking=self._get_env_var(
                    "ENABLE_COST_TRACKING",
                    config_data.get("cost_control", {}).get("enable_tracking", True),
                    bool
                ),
            ),
            
            # Environment settings
            environment=self._get_env_var("ENVIRONMENT", "development"),
            debug=self._get_env_var("DEBUG", False, bool),
        )
        
        return app_config


def load_config(config_dir: Optional[str] = None) -> AppConfig:
    """Load application configuration from multiple sources.
    
    Args:
        config_dir: Directory containing configuration files
        
    Returns:
        Complete application configuration
        
    Raises:
        ValueError: If configuration validation fails
    """
    loader = ConfigLoader(config_dir)
    config = loader.load_config()
    
    # Validate configuration
    errors = config.validate()
    if errors:
        error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
        raise ValueError(error_msg)
    
    logger.info("Configuration loaded and validated successfully")
    return config