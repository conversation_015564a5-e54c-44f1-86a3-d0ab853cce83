"""
Main entry point for the Personal Command-Line Vector Knowledge Base.

This module provides the main application entry point and interactive mode.
"""

import sys
import logging
from typing import Optional

from .commands import cli
from .interface import InteractiveInterface, QueryResult
from ..config import load_config
from ..api.api_manager import APIManager
from ..core.storage import ChromaStorage
from ..core.embedder import APIEmbedder
from ..core.retriever import SemanticRetriever
from ..core.generator import APIGenerator

logger = logging.getLogger(__name__)


class CommandKBApp:
    """Main application class for the Command Knowledge Base."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize the application.
        
        Args:
            config_dir: Optional configuration directory
        """
        self.config_dir = config_dir
        self.config = None
        self.api_manager = None
        self.storage = None
        self.embedder = None
        self.retriever = None
        self.generator = None
        self.interface = InteractiveInterface()
        
        # Initialize components
        self._initialize()
    
    def _initialize(self):
        """Initialize application components."""
        try:
            # Load configuration
            self.config = load_config(self.config_dir)
            
            # Initialize core components
            self.api_manager = APIManager(self.config)
            self.storage = ChromaStorage(self.config)
            self.embedder = APIEmbedder(self.config, self.api_manager)
            self.retriever = SemanticRetriever(self.config, self.storage, self.embedder)
            self.generator = APIGenerator(self.config, self.api_manager)
            
            logger.info("Application initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            self.interface.display_error(
                "Failed to initialize application",
                str(e)
            )
            sys.exit(1)
    
    def run_interactive(self):
        """Run the application in interactive mode."""
        self.interface.print_welcome()
        
        while True:
            try:
                # Get user command
                command = self.interface.get_user_input("kb>", "").strip().lower()
                
                if not command:
                    continue
                
                # Handle commands
                if command in ['exit', 'quit', 'q']:
                    self.interface.display_success("Goodbye! 👋")
                    break
                elif command in ['help', 'h', '?']:
                    self._show_help()
                elif command.startswith('query ') or command.startswith('q '):
                    query_text = command.split(' ', 1)[1] if ' ' in command else ""
                    if query_text:
                        self._handle_query(query_text)
                    else:
                        self.interface.display_error("Please provide a query")
                elif command == 'status':
                    self._handle_status()
                elif command == 'clear':
                    self.interface.clear_screen()
                elif command.startswith('import '):
                    path = command.split(' ', 1)[1] if ' ' in command else ""
                    if path:
                        self._handle_import(path)
                    else:
                        self.interface.display_error("Please provide a path to import")
                else:
                    # Try to interpret as a direct query
                    if len(command) > 3:  # Avoid single character queries
                        self._handle_query(command)
                    else:
                        self.interface.display_error(
                            f"Unknown command: {command}",
                            "Type 'help' for available commands"
                        )
            
            except KeyboardInterrupt:
                print("\n")
                if self.interface.confirm("Are you sure you want to exit?"):
                    break
            except Exception as e:
                logger.exception("Error in interactive mode")
                self.interface.display_error("An error occurred", str(e))
    
    def _show_help(self):
        """Show help information."""
        help_text = """
Available Commands:

🔍 Query Commands:
  query <text>     - Search for commands and get AI-powered answers
  q <text>         - Short form of query
  <text>           - Direct query (if text is longer than 3 characters)

📊 System Commands:
  status           - Show system status and statistics
  import <path>    - Import documents from specified path
  clear            - Clear the screen

🛠️ Utility Commands:
  help, h, ?       - Show this help message
  exit, quit, q    - Exit the application

Examples:
  query how to list files in linux
  q docker container logs
  git commit with message
  status
  import ./my-docs/
        """
        
        if hasattr(self.interface, 'console') and self.interface.console:
            from rich.panel import Panel
            help_panel = Panel(
                help_text.strip(),
                title="📚 Help",
                border_style="blue"
            )
            self.interface.console.print(help_panel)
        else:
            print(help_text)
    
    def _handle_query(self, query_text: str):
        """Handle a query command.
        
        Args:
            query_text: Query text to process
        """
        try:
            import time
            start_time = time.time()
            
            # Show progress
            with self.interface.progress.show_spinner("Searching for relevant documents..."):
                # Retrieve relevant documents
                retrieval_result = self.retriever.retrieve(query_text, top_k=5)
            
            if not retrieval_result.documents:
                self.interface.display_error("No relevant documents found for your query")
                return
            
            # Generate answer
            with self.interface.progress.show_spinner("Generating AI-powered answer..."):
                generation_result = self.generator.generate_answer(
                    query_text, 
                    retrieval_result.documents
                )
            
            if not generation_result.success:
                self.interface.display_error(
                    "Failed to generate answer",
                    generation_result.error
                )
                return
            
            # Prepare result for display
            response_time = time.time() - start_time
            
            # Convert documents to source format
            sources = []
            for doc in retrieval_result.documents:
                sources.append({
                    'content': doc.page_content,
                    'source': doc.metadata.get('source', 'Unknown'),
                    'metadata': doc.metadata
                })
            
            result = QueryResult(
                query=query_text,
                answer=generation_result.answer,
                sources=sources,
                provider=generation_result.provider_used,
                cost=generation_result.cost,
                response_time=response_time,
                cached=generation_result.cached
            )
            
            # Display result
            self.interface.display_query_result(result)
            
        except Exception as e:
            logger.exception("Query handling failed")
            self.interface.display_error("Query failed", str(e))
    
    def _handle_status(self):
        """Handle status command."""
        try:
            # Get system status
            storage_stats = self.storage.get_collection_stats()
            api_stats = self.api_manager.get_stats()
            
            status_data = {
                "Database": {
                    "status": "Connected",
                    "details": f"{storage_stats.get('document_count', 0)} documents"
                },
                "API Providers": {
                    "status": f"{len(self.config.api_providers)} configured",
                    "details": f"{api_stats['total_requests']} total requests"
                },
                "Total Cost": {
                    "status": f"${api_stats['total_cost']:.6f}",
                    "details": "All-time spending"
                },
                "Primary Provider": {
                    "status": self.config.primary_provider,
                    "details": "Currently active"
                }
            }
            
            # Add provider health
            health_results = self.api_manager.health_check()
            healthy_providers = sum(1 for result in health_results.values() if result.success)
            total_providers = len(health_results)
            
            status_data["Provider Health"] = {
                "status": f"{healthy_providers}/{total_providers} healthy",
                "details": "API connectivity status"
            }
            
            self.interface.display_status(status_data)
            
        except Exception as e:
            logger.exception("Status check failed")
            self.interface.display_error("Failed to get status", str(e))
    
    def _handle_import(self, path: str):
        """Handle import command.
        
        Args:
            path: Path to import documents from
        """
        try:
            from pathlib import Path
            from ..core.loader import MarkdownLoader
            from ..core.chunker import SmartChunker
            
            source_path = Path(path)
            if not source_path.exists():
                self.interface.display_error(f"Path does not exist: {path}")
                return
            
            # Confirm import
            if not self.interface.confirm(f"Import documents from {path}?"):
                return
            
            # Load documents
            with self.interface.progress.show_spinner("Loading documents..."):
                loader = MarkdownLoader()
                documents = loader.load_documents(str(source_path))
            
            if not documents:
                self.interface.display_error("No documents found to import")
                return
            
            # Chunk documents
            with self.interface.progress.show_spinner("Processing documents..."):
                chunker = SmartChunker()
                chunked_docs = chunker.chunk_documents(documents)
            
            # Process in batches
            batch_size = 50
            total_cost = 0.0
            
            with self.interface.progress.show_progress(
                ["Embedding documents", "Storing in database"], 
                len(chunked_docs)
            ) as progress:
                
                task = progress.add_task("Processing...", total=len(chunked_docs))
                
                for i in range(0, len(chunked_docs), batch_size):
                    batch = chunked_docs[i:i + batch_size]
                    
                    # Generate embeddings
                    embedding_result = self.embedder.embed_documents(batch)
                    
                    if not embedding_result.success:
                        self.interface.display_error(
                            f"Failed to embed batch: {embedding_result.error}"
                        )
                        continue
                    
                    # Store in database
                    success = self.storage.store_documents(batch, embedding_result.embeddings)
                    
                    if success:
                        total_cost += embedding_result.cost
                        progress.update(task, advance=len(batch))
                    else:
                        self.interface.display_error("Failed to store batch")
            
            # Show results
            self.interface.display_success(
                f"Import completed! Processed {len(chunked_docs)} chunks. "
                f"Total cost: ${total_cost:.6f}"
            )
            
        except Exception as e:
            logger.exception("Import failed")
            self.interface.display_error("Import failed", str(e))


def main():
    """Main entry point for the application."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Personal Command-Line Vector Knowledge Base"
    )
    parser.add_argument(
        '--config-dir', 
        type=str, 
        help='Configuration directory'
    )
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='Run in interactive mode'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.INFO if args.verbose else logging.WARNING
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # If no arguments provided, run CLI
    if len(sys.argv) == 1:
        cli()
        return
    
    # Run interactive mode if requested
    if args.interactive:
        app = CommandKBApp(args.config_dir) # 获取配置目录 ，args.config_dir 为命令行参数 --config-dir 的值
        app.run_interactive()
    else:
        # Run CLI with arguments
        cli()


if __name__ == '__main__':
    main()