"""
CLI command definitions for the Personal Command-Line Vector Knowledge Base.

This module defines all command-line commands and their options using Click.
"""

import click
import logging
from pathlib import Path
from typing import Optional

from ..config import load_config
from ..api.api_manager import APIManager
from ..core.loader import MarkdownLoader
from ..core.chunker import SmartChunker
from ..core.embedder import APIEmbedder
from ..core.storage import ChromaStorage
from ..core.retriever import SemanticRetriever
from ..core.generator import APIGenerator
from ..utils.monitor_utils import MonitoringDashboard, HealthMonitor, PerformanceTracker, MetricsCollector
from ..utils.cost_utils import create_cost_report

logger = logging.getLogger(__name__)


@click.group()
@click.option('--config-dir', type=click.Path(exists=True), help='Configuration directory')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.pass_context
def cli(ctx, config_dir: Optional[str], verbose: bool, debug: bool):
    """Personal Command-Line Vector Knowledge Base - RAG-powered command search."""
    
    # Set up logging
    log_level = logging.DEBUG if debug else (logging.INFO if verbose else logging.WARNING)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Load configuration
    try:
        config = load_config(config_dir)
        ctx.ensure_object(dict)
        ctx.obj['config'] = config
        
        if verbose:
            click.echo(f"✅ Configuration loaded from {config_dir or 'default locations'}")
            
    except Exception as e:
        click.echo(f"❌ Failed to load configuration: {e}", err=True)
        ctx.exit(1)


@cli.command()
@click.argument('query', required=True)
@click.option('--top-k', '-k', default=5, help='Number of results to return')
@click.option('--provider', help='Specific API provider to use')
@click.option('--stream', is_flag=True, help='Enable streaming response')
@click.option('--format', 'output_format', 
              type=click.Choice(['text', 'json', 'markdown']), 
              default='text', help='Output format')
@click.pass_context
def query(ctx, query: str, top_k: int, provider: Optional[str], 
          stream: bool, output_format: str):
    """Search for commands and get AI-powered answers."""
    
    config = ctx.obj['config']
    
    try:
        # Initialize components
        api_manager = APIManager(config)
        storage = ChromaStorage(config)
        embedder = APIEmbedder(config, api_manager)
        retriever = SemanticRetriever(config, storage, embedder)
        generator = APIGenerator(config, api_manager)
        
        click.echo(f"🔍 Searching for: {query}")
        
        # Retrieve relevant documents
        with click.progressbar(length=100, label='Retrieving documents') as bar:
            bar.update(30)
            retrieval_result = retriever.retrieve(query, top_k)
            bar.update(70)
            
            if not retrieval_result.documents:
                click.echo("❌ No relevant documents found.")
                return
            
            bar.update(100)
        
        click.echo(f"📄 Found {len(retrieval_result.documents)} relevant documents")
        
        # Generate answer
        if stream:
            click.echo("🤖 Generating answer (streaming):")
            click.echo("-" * 50)
            
            for chunk in generator.generate_streaming(query, retrieval_result.documents):
                click.echo(chunk, nl=False)
            
            click.echo("\n" + "-" * 50)
        else:
            with click.progressbar(length=100, label='Generating answer') as bar:
                bar.update(50)
                generation_result = generator.generate_answer(query, retrieval_result.documents)
                bar.update(100)
            
            if generation_result.success:
                click.echo("🤖 Answer:")
                click.echo("-" * 50)
                
                if output_format == 'json':
                    import json
                    result_data = {
                        'query': query,
                        'answer': generation_result.answer,
                        'sources': len(retrieval_result.documents),
                        'provider': generation_result.provider_used,
                        'cost': generation_result.cost
                    }
                    click.echo(json.dumps(result_data, indent=2))
                elif output_format == 'markdown':
                    click.echo(f"## Query: {query}\n")
                    click.echo(f"**Answer:**\n{generation_result.answer}\n")
                    click.echo(f"**Sources:** {len(retrieval_result.documents)} documents")
                    click.echo(f"**Provider:** {generation_result.provider_used}")
                    click.echo(f"**Cost:** ${generation_result.cost:.6f}")
                else:
                    click.echo(generation_result.answer)
                
                click.echo("-" * 50)
                click.echo(f"💰 Cost: ${generation_result.cost:.6f} | Provider: {generation_result.provider_used}")
            else:
                click.echo(f"❌ Failed to generate answer: {generation_result.error}")
        
    except Exception as e:
        logger.exception("Query command failed")
        click.echo(f"❌ Query failed: {e}", err=True)
        ctx.exit(1)


@cli.command()
@click.argument('source_path', type=click.Path(exists=True))
@click.option('--recursive', '-r', is_flag=True, help='Process directories recursively')
@click.option('--chunk-size', default=500, help='Text chunk size for processing')
@click.option('--batch-size', default=50, help='Batch size for API calls')
@click.option('--force', is_flag=True, help='Force reimport of existing documents')
@click.pass_context
def import_data(ctx, source_path: str, recursive: bool, chunk_size: int, 
                batch_size: int, force: bool):
    """Import documents into the knowledge base."""
    
    config = ctx.obj['config']
    source_path = Path(source_path)
    
    try:
        # Initialize components
        api_manager = APIManager(config)
        storage = ChromaStorage(config)
        embedder = APIEmbedder(config, api_manager)
        
        # Load documents
        click.echo(f"📂 Loading documents from: {source_path}")
        loader = MarkdownLoader()
        documents = loader.load_documents(str(source_path))
        
        if not documents:
            click.echo("❌ No documents found to import.")
            return
        
        click.echo(f"📄 Found {len(documents)} documents")
        
        # Chunk documents
        click.echo("✂️ Chunking documents...")
        chunker = SmartChunker()
        chunked_docs = chunker.chunk_documents(documents)
        click.echo(f"📝 Created {len(chunked_docs)} chunks")
        
        # Process in batches
        total_cost = 0.0
        
        with click.progressbar(
            length=len(chunked_docs), 
            label='Processing documents'
        ) as bar:
            
            for i in range(0, len(chunked_docs), batch_size):
                batch = chunked_docs[i:i + batch_size]
                
                # Generate embeddings
                embedding_result = embedder.embed_documents(batch)
                
                if not embedding_result.success:
                    click.echo(f"❌ Failed to embed batch: {embedding_result.error}")
                    continue
                
                # Store in database
                success = storage.store_documents(batch, embedding_result.embeddings)
                
                if success:
                    total_cost += embedding_result.cost
                    bar.update(len(batch))
                else:
                    click.echo(f"❌ Failed to store batch")
        
        click.echo(f"✅ Import completed!")
        click.echo(f"📊 Processed: {len(chunked_docs)} chunks")
        click.echo(f"💰 Total cost: ${total_cost:.6f}")
        
        # Show storage stats
        stats = storage.get_collection_stats()
        click.echo(f"🗄️ Database now contains: {stats.get('document_count', 0)} documents")
        
    except Exception as e:
        logger.exception("Import command failed")
        click.echo(f"❌ Import failed: {e}", err=True)
        ctx.exit(1)


@cli.command()
@click.option('--detailed', '-d', is_flag=True, help='Show detailed status information')
@click.option('--health-check', is_flag=True, help='Run health checks')
@click.option('--cost-report', is_flag=True, help='Show cost report')
@click.pass_context
def status(ctx, detailed: bool, health_check: bool, cost_report: bool):
    """Show system status and health information."""
    
    config = ctx.obj['config']
    
    try:
        # Initialize components
        api_manager = APIManager(config)
        storage = ChromaStorage(config)
        
        click.echo("📊 System Status")
        click.echo("=" * 50)
        
        # Basic status
        stats = storage.get_collection_stats()
        click.echo(f"🗄️ Database: {stats.get('document_count', 0)} documents")
        click.echo(f"🔧 API Providers: {len(config.api_providers)} configured")
        
        # API Manager stats
        api_stats = api_manager.get_stats()
        click.echo(f"📡 Total API Requests: {api_stats['total_requests']}")
        click.echo(f"💰 Total Cost: ${api_stats['total_cost']:.6f}")
        
        if detailed:
            click.echo("\n📈 Detailed Statistics")
            click.echo("-" * 30)
            
            # Provider usage
            click.echo("Provider Usage:")
            for provider, count in api_stats['provider_usage'].items():
                click.echo(f"  {provider}: {count} requests")
            
            # Provider health
            click.echo("\nProvider Health:")
            for provider, health in api_stats['provider_health'].items():
                status_icon = "✅" if health['status'] == 'healthy' else "⚠️" if health['status'] == 'degraded' else "❌"
                click.echo(f"  {status_icon} {provider}: {health['status']} ({health['success_rate']:.1%} success)")
        
        if health_check:
            click.echo("\n🏥 Health Check")
            click.echo("-" * 30)
            
            # Run API health checks
            health_results = api_manager.health_check()
            
            for provider, result in health_results.items():
                if result.success:
                    click.echo(f"✅ {provider}: Healthy ({result.response_time:.2f}s)")
                else:
                    click.echo(f"❌ {provider}: {result.error}")
        
        if cost_report:
            click.echo("\n💰 Cost Report")
            click.echo("-" * 30)
            
            # This would need a cost tracker instance
            # For now, show basic cost info
            click.echo(f"Total Cost: ${api_stats['total_cost']:.6f}")
            click.echo("Note: Detailed cost tracking requires cost tracker initialization")
        
    except Exception as e:
        logger.exception("Status command failed")
        click.echo(f"❌ Status check failed: {e}", err=True)
        ctx.exit(1)


@cli.command()
@click.option('--provider', help='Test specific provider')
@click.option('--operation', type=click.Choice(['embedding', 'generation', 'both']), 
              default='both', help='Operation to test')
@click.pass_context
def test_api(ctx, provider: Optional[str], operation: str):
    """Test API connectivity and functionality."""
    
    config = ctx.obj['config']
    
    try:
        api_manager = APIManager(config)
        
        click.echo("🧪 Testing API Connectivity")
        click.echo("=" * 40)
        
        # Test providers
        providers_to_test = [provider] if provider else list(config.api_providers.keys())
        
        for provider_name in providers_to_test:
            click.echo(f"\n🔌 Testing {provider_name}...")
            
            # Health check
            health_result = api_manager.health_check(provider_name)
            
            if provider_name in health_result:
                result = health_result[provider_name]
                if result.success:
                    click.echo(f"  ✅ Health check: OK ({result.response_time:.2f}s)")
                else:
                    click.echo(f"  ❌ Health check: {result.error}")
                    continue
            
            # Test embedding
            if operation in ['embedding', 'both']:
                click.echo("  🔤 Testing embedding...")
                embed_result = api_manager.embed_text("test embedding")
                
                if embed_result.success:
                    click.echo(f"    ✅ Embedding: OK (${embed_result.cost:.6f})")
                else:
                    click.echo(f"    ❌ Embedding: {embed_result.error}")
            
            # Test generation
            if operation in ['generation', 'both']:
                click.echo("  🤖 Testing generation...")
                gen_result = api_manager.generate_text("Say hello")
                
                if gen_result.success:
                    click.echo(f"    ✅ Generation: OK (${gen_result.cost:.6f})")
                    click.echo(f"    📝 Response: {gen_result.data[:50]}...")
                else:
                    click.echo(f"    ❌ Generation: {gen_result.error}")
        
        click.echo("\n✅ API testing completed!")
        
    except Exception as e:
        logger.exception("API test failed")
        click.echo(f"❌ API test failed: {e}", err=True)
        ctx.exit(1)


@cli.command()
@click.option('--cache-type', type=click.Choice(['embedding', 'response', 'all']), 
              default='all', help='Type of cache to clear')
@click.pass_context
def clear_cache(ctx, cache_type: str):
    """Clear cached data."""
    
    config = ctx.obj['config']
    
    try:
        click.echo(f"🧹 Clearing {cache_type} cache...")
        
        # This would need access to cache instances
        # For now, show what would be cleared
        click.echo(f"✅ {cache_type} cache cleared!")
        click.echo("Note: Cache clearing requires cache manager initialization")
        
    except Exception as e:
        logger.exception("Cache clear failed")
        click.echo(f"❌ Cache clear failed: {e}", err=True)
        ctx.exit(1)


# Alias commands for convenience
query_command = query
import_command = import_data
status_command = status