"""
Interactive interface components for the Personal Command-Line Vector Knowledge Base.

This module provides interactive UI components:
- Progress display and status updates
- Interactive query interface
- Rich text formatting and display
- User input handling and validation
"""

import time
import logging
from typing import List, Dict, Any, Optional, Iterator
from dataclasses import dataclass

try:
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    from rich.prompt import Prompt, Confirm
    from rich.markdown import Markdown
    from rich.syntax import Syntax
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class QueryResult:
    """Container for query results."""
    query: str
    answer: str
    sources: List[Dict[str, Any]]
    provider: str
    cost: float
    response_time: float
    cached: bool = False


class ProgressDisplay:
    """Progress display with fallback for systems without rich."""
    
    def __init__(self, use_rich: bool = RICH_AVAILABLE):
        """Initialize progress display.
        
        Args:
            use_rich: Whether to use rich formatting
        """
        self.use_rich = use_rich and RICH_AVAILABLE
        self.console = Console() if self.use_rich else None
    
    def show_progress(self, tasks: List[str], total_steps: int = 100):
        """Show progress for multiple tasks.
        
        Args:
            tasks: List of task descriptions
            total_steps: Total number of steps
        """
        if self.use_rich:
            return self._rich_progress(tasks, total_steps)
        else:
            return self._simple_progress(tasks, total_steps)
    
    def _rich_progress(self, tasks: List[str], total_steps: int):
        """Rich progress display."""
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        )
        
        return progress
    
    def _simple_progress(self, tasks: List[str], total_steps: int):
        """Simple text-based progress display."""
        class SimpleProgress:
            def __init__(self, tasks):
                self.tasks = tasks
                self.current_task = 0
            
            def add_task(self, description, total=100):
                return len(self.tasks)
            
            def update(self, task_id, advance=1):
                print(f"Progress: {self.tasks[min(task_id, len(self.tasks)-1)]}")
            
            def __enter__(self):
                return self
            
            def __exit__(self, *args):
                print("✅ Completed!")
        
        return SimpleProgress(tasks)
    
    def show_spinner(self, message: str):
        """Show a spinner with message."""
        if self.use_rich:
            return self.console.status(message)
        else:
            print(f"⏳ {message}")
            return self._dummy_context()
    
    def _dummy_context(self):
        """Dummy context manager for non-rich environments."""
        class DummyContext:
            def __enter__(self):
                return self
            def __exit__(self, *args):
                pass
        return DummyContext()


class InteractiveInterface:
    """Interactive command-line interface."""
    
    def __init__(self, use_rich: bool = RICH_AVAILABLE):
        """Initialize interactive interface.
        
        Args:
            use_rich: Whether to use rich formatting
        """
        self.use_rich = use_rich and RICH_AVAILABLE
        self.console = Console() if self.use_rich else None
        self.progress = ProgressDisplay(use_rich)
    
    def print_welcome(self):
        """Print welcome message."""
        if self.use_rich:
            welcome_panel = Panel(
                "[bold blue]Personal Command-Line Vector Knowledge Base[/bold blue]\n"
                "[dim]RAG-powered intelligent command search[/dim]\n\n"
                "Commands:\n"
                "• [bold]query[/bold] - Search for commands and get AI answers\n"
                "• [bold]import[/bold] - Import documents into knowledge base\n"
                "• [bold]status[/bold] - Show system status\n"
                "• [bold]help[/bold] - Show help information\n"
                "• [bold]exit[/bold] - Exit the application",
                title="🤖 Welcome",
                border_style="blue"
            )
            self.console.print(welcome_panel)
        else:
            print("=" * 60)
            print("Personal Command-Line Vector Knowledge Base")
            print("RAG-powered intelligent command search")
            print("=" * 60)
            print("\nCommands:")
            print("• query - Search for commands and get AI answers")
            print("• import - Import documents into knowledge base")
            print("• status - Show system status")
            print("• help - Show help information")
            print("• exit - Exit the application")
            print()
    
    def get_user_input(self, prompt: str = "Enter command", default: str = "") -> str:
        """Get user input with prompt.
        
        Args:
            prompt: Prompt message
            default: Default value
            
        Returns:
            User input string
        """
        if self.use_rich:
            return Prompt.ask(f"[bold cyan]{prompt}[/bold cyan]", default=default)
        else:
            user_input = input(f"{prompt}: ")
            return user_input or default
    
    def confirm(self, message: str, default: bool = True) -> bool:
        """Get user confirmation.
        
        Args:
            message: Confirmation message
            default: Default value
            
        Returns:
            User confirmation
        """
        if self.use_rich:
            return Confirm.ask(message, default=default)
        else:
            default_str = "Y/n" if default else "y/N"
            response = input(f"{message} ({default_str}): ").lower()
            if not response:
                return default
            return response.startswith('y')
    
    def display_query_result(self, result: QueryResult):
        """Display query result in formatted way.
        
        Args:
            result: Query result to display
        """
        if self.use_rich:
            self._display_rich_result(result)
        else:
            self._display_simple_result(result)
    
    def _display_rich_result(self, result: QueryResult):
        """Display result using rich formatting."""
        # Query panel
        query_panel = Panel(
            f"[bold]{result.query}[/bold]",
            title="🔍 Query",
            border_style="cyan"
        )
        self.console.print(query_panel)
        
        # Answer panel
        answer_markdown = Markdown(result.answer)
        answer_panel = Panel(
            answer_markdown,
            title="🤖 Answer",
            border_style="green"
        )
        self.console.print(answer_panel)
        
        # Metadata table
        metadata_table = Table(title="📊 Query Metadata")
        metadata_table.add_column("Metric", style="cyan")
        metadata_table.add_column("Value", style="white")
        
        metadata_table.add_row("Provider", result.provider)
        metadata_table.add_row("Sources", str(len(result.sources)))
        metadata_table.add_row("Cost", f"${result.cost:.6f}")
        metadata_table.add_row("Response Time", f"{result.response_time:.2f}s")
        metadata_table.add_row("Cached", "✅" if result.cached else "❌")
        
        self.console.print(metadata_table)
        
        # Sources if available
        if result.sources:
            self.console.print("\n[bold]📄 Sources:[/bold]")
            for i, source in enumerate(result.sources[:3], 1):
                source_text = source.get('content', '')[:200] + "..."
                source_panel = Panel(
                    source_text,
                    title=f"Source {i}: {source.get('source', 'Unknown')}",
                    border_style="dim"
                )
                self.console.print(source_panel)
    
    def _display_simple_result(self, result: QueryResult):
        """Display result using simple text formatting."""
        print("\n" + "=" * 60)
        print(f"🔍 Query: {result.query}")
        print("=" * 60)
        
        print("\n🤖 Answer:")
        print("-" * 40)
        print(result.answer)
        print("-" * 40)
        
        print(f"\n📊 Metadata:")
        print(f"  Provider: {result.provider}")
        print(f"  Sources: {len(result.sources)}")
        print(f"  Cost: ${result.cost:.6f}")
        print(f"  Response Time: {result.response_time:.2f}s")
        print(f"  Cached: {'Yes' if result.cached else 'No'}")
        
        if result.sources:
            print(f"\n📄 Sources:")
            for i, source in enumerate(result.sources[:3], 1):
                print(f"  {i}. {source.get('source', 'Unknown')}")
        
        print("=" * 60)
    
    def display_streaming_response(self, query: str, response_stream: Iterator[str]):
        """Display streaming response.
        
        Args:
            query: Original query
            response_stream: Iterator of response chunks
        """
        if self.use_rich:
            self.console.print(f"[bold cyan]🔍 Query:[/bold cyan] {query}")
            self.console.print("[bold green]🤖 Answer (streaming):[/bold green]")
            self.console.print("-" * 50)
            
            for chunk in response_stream:
                self.console.print(chunk, end="")
            
            self.console.print("\n" + "-" * 50)
        else:
            print(f"🔍 Query: {query}")
            print("🤖 Answer (streaming):")
            print("-" * 50)
            
            for chunk in response_stream:
                print(chunk, end="", flush=True)
            
            print("\n" + "-" * 50)
    
    def display_status(self, status_data: Dict[str, Any]):
        """Display system status information.
        
        Args:
            status_data: Status information dictionary
        """
        if self.use_rich:
            self._display_rich_status(status_data)
        else:
            self._display_simple_status(status_data)
    
    def _display_rich_status(self, status_data: Dict[str, Any]):
        """Display status using rich formatting."""
        # System overview
        overview_table = Table(title="🖥️ System Overview")
        overview_table.add_column("Component", style="cyan")
        overview_table.add_column("Status", style="white")
        overview_table.add_column("Details", style="dim")
        
        # Add status rows
        for component, info in status_data.items():
            if isinstance(info, dict):
                status = info.get('status', 'Unknown')
                details = info.get('details', '')
                overview_table.add_row(component, status, str(details))
            else:
                overview_table.add_row(component, str(info), "")
        
        self.console.print(overview_table)
    
    def _display_simple_status(self, status_data: Dict[str, Any]):
        """Display status using simple text formatting."""
        print("\n📊 System Status")
        print("=" * 40)
        
        for component, info in status_data.items():
            if isinstance(info, dict):
                status = info.get('status', 'Unknown')
                print(f"{component}: {status}")
            else:
                print(f"{component}: {info}")
    
    def display_error(self, message: str, details: Optional[str] = None):
        """Display error message.
        
        Args:
            message: Error message
            details: Optional error details
        """
        if self.use_rich:
            error_panel = Panel(
                f"[bold red]{message}[/bold red]" + 
                (f"\n[dim]{details}[/dim]" if details else ""),
                title="❌ Error",
                border_style="red"
            )
            self.console.print(error_panel)
        else:
            print(f"❌ Error: {message}")
            if details:
                print(f"Details: {details}")
    
    def display_success(self, message: str):
        """Display success message.
        
        Args:
            message: Success message
        """
        if self.use_rich:
            success_panel = Panel(
                f"[bold green]{message}[/bold green]",
                title="✅ Success",
                border_style="green"
            )
            self.console.print(success_panel)
        else:
            print(f"✅ {message}")
    
    def display_code_block(self, code: str, language: str = "bash"):
        """Display code block with syntax highlighting.
        
        Args:
            code: Code to display
            language: Programming language for highlighting
        """
        if self.use_rich:
            syntax = Syntax(code, language, theme="monokai", line_numbers=True)
            self.console.print(syntax)
        else:
            print(f"```{language}")
            print(code)
            print("```")
    
    def clear_screen(self):
        """Clear the terminal screen."""
        if self.use_rich:
            self.console.clear()
        else:
            import os
            os.system('cls' if os.name == 'nt' else 'clear')