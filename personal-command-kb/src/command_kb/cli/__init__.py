"""
Command-line interface for the Personal Command-Line Vector Knowledge Base.

This package provides CLI functionality:
- Command definitions and parsing
- Interactive user interface
- Main application entry point
- Progress display and user feedback
"""

from .commands import cli, query_command, import_command, status_command
from .interface import InteractiveInterface, ProgressDisplay
from .main import main

__all__ = [
    "cli",
    "query_command", 
    "import_command",
    "status_command",
    "InteractiveInterface",
    "ProgressDisplay",
    "main",
]