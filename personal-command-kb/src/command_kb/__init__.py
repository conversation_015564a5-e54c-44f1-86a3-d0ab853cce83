"""
Personal Command-Line Vector Knowledge Base

A RAG-based intelligent retrieval system for command-line snippets and code fragments.
Built with privacy-first principles and multi-provider API support for high availability.

Key Features:
- Multi-provider API integration (OpenAI, SiliconFlow, Zhipu, etc.)
- Local ChromaDB vector storage for privacy
- Intelligent text chunking and semantic search
- Cost control and monitoring
- High availability with automatic failover
- Comprehensive caching system

Author: Developer
Version: 0.1.0
License: MIT
"""

__version__ = "0.1.0"
__author__ = "Developer"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Core exports
from .config import AppConfig, load_config
from .constants import APP_NAME, APP_VERSION, APP_DESCRIPTION

__all__ = [
    "__version__",
    "__author__", 
    "__email__",
    "__license__",
    "APP_NAME",
    "APP_VERSION", 
    "APP_DESCRIPTION",
    "AppConfig",
    "load_config",
]