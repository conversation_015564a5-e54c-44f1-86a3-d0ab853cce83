#!/usr/bin/env python3
"""
Setup script for Personal Command-Line Vector Knowledge Base.

This script helps users set up the application for first-time use:
- Creates necessary directories
- Validates configuration
- Tests API connectivity
- Initializes the vector database
- Imports sample data (optional)
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from command_kb.config import load_config
from command_kb.api.api_manager import APIManager
from command_kb.core.storage import ChromaStorage
from command_kb.utils.file_utils import ensure_directories

logger = logging.getLogger(__name__)


class SetupManager:
    """Manages the setup process for the application."""
    
    def __init__(self):
        """Initialize setup manager."""
        self.config = None
        self.api_manager = None
        self.storage = None
        self.setup_steps = [
            ("Creating directories", self._create_directories),
            ("Loading configuration", self._load_configuration),
            ("Testing API connectivity", self._test_api_connectivity),
            ("Initializing database", self._initialize_database),
            ("Validating setup", self._validate_setup),
        ]
    
    def run_setup(self, include_sample_data: bool = False) -> bool:
        """Run the complete setup process.
        
        Args:
            include_sample_data: Whether to import sample data
            
        Returns:
            True if setup successful, False otherwise
        """
        print("🚀 Personal Command-Line Vector Knowledge Base Setup")
        print("=" * 60)
        
        try:
            # Add sample data import if requested
            if include_sample_data:
                self.setup_steps.append(
                    ("Importing sample data", self._import_sample_data)
                )
            
            # Run setup steps
            for step_name, step_func in self.setup_steps:
                print(f"\n📋 {step_name}...")
                
                try:
                    success = step_func()
                    if success:
                        print(f"✅ {step_name} completed successfully")
                    else:
                        print(f"❌ {step_name} failed")
                        return False
                except Exception as e:
                    print(f"❌ {step_name} failed: {e}")
                    logger.exception(f"Setup step failed: {step_name}")
                    return False
            
            print("\n🎉 Setup completed successfully!")
            print("\nNext steps:")
            print("1. Copy .env.example to .env and add your API keys")
            print("2. Run: python -m command_kb.cli.main --interactive")
            print("3. Try: query 'how to list files in linux'")
            
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            logger.exception("Setup process failed")
            return False
    
    def _create_directories(self) -> bool:
        """Create necessary directories."""
        directories = [
            "./data",
            "./data/raw",
            "./data/processed", 
            "./data/cache",
            "./data/vector_db",
            "./logs",
            "./config",
        ]
        
        try:
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
                print(f"  📁 Created: {directory}")
            
            return True
        except Exception as e:
            print(f"Failed to create directories: {e}")
            return False
    
    def _load_configuration(self) -> bool:
        """Load and validate configuration."""
        try:
            # Check if .env file exists
            env_file = Path(".env")
            if not env_file.exists():
                print("  ⚠️  .env file not found. Please copy .env.example to .env")
                print("     and configure your API keys before proceeding.")
                
                # Create a basic .env file
                with open(".env", "w") as f:
                    f.write("# Copy values from .env.example and configure your API keys\n")
                    f.write("# OPENAI_API_KEY=your-key-here\n")
                    f.write("# SILICONFLOW_API_KEY=your-key-here\n")
                
                return False
            
            # Load configuration
            self.config = load_config()
            print(f"  ✅ Configuration loaded successfully")
            print(f"  📊 Found {len(self.config.api_providers)} API providers")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Configuration loading failed: {e}")
            return False
    
    def _test_api_connectivity(self) -> bool:
        """Test API connectivity for configured providers."""
        if not self.config:
            return False
        
        try:
            self.api_manager = APIManager(self.config)
            
            # Test each provider
            health_results = self.api_manager.health_check()
            
            healthy_count = 0
            for provider, result in health_results.items():
                if result.success:
                    print(f"  ✅ {provider}: Healthy ({result.response_time:.2f}s)")
                    healthy_count += 1
                else:
                    print(f"  ❌ {provider}: {result.error}")
            
            if healthy_count == 0:
                print("  ⚠️  No API providers are healthy. Please check your API keys.")
                return False
            
            print(f"  📊 {healthy_count}/{len(health_results)} providers healthy")
            return True
            
        except Exception as e:
            print(f"  ❌ API connectivity test failed: {e}")
            return False
    
    def _initialize_database(self) -> bool:
        """Initialize the vector database."""
        if not self.config:
            return False
        
        try:
            self.storage = ChromaStorage(self.config)
            
            # Test database connection
            stats = self.storage.get_collection_stats()
            print(f"  ✅ Database initialized successfully")
            print(f"  📊 Current document count: {stats.get('document_count', 0)}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Database initialization failed: {e}")
            return False
    
    def _validate_setup(self) -> bool:
        """Validate the complete setup."""
        try:
            # Check all components are initialized
            if not all([self.config, self.api_manager, self.storage]):
                print("  ❌ Not all components initialized")
                return False
            
            # Check API providers
            if not self.config.api_providers:
                print("  ❌ No API providers configured")
                return False
            
            # Check primary provider
            if self.config.primary_provider not in self.config.api_providers:
                print(f"  ❌ Primary provider '{self.config.primary_provider}' not available")
                return False
            
            print("  ✅ All components validated successfully")
            return True
            
        except Exception as e:
            print(f"  ❌ Validation failed: {e}")
            return False
    
    def _import_sample_data(self) -> bool:
        """Import sample data for testing."""
        try:
            # Create sample markdown files
            sample_data_dir = Path("./data/raw/samples")
            sample_data_dir.mkdir(parents=True, exist_ok=True)
            
            # Sample command documentation
            samples = {
                "linux_commands.md": """# Linux Commands Reference

## File Operations

### List Files
```bash
ls -la          # List all files with details
ls -lh          # List with human-readable sizes
find . -name "*.txt"  # Find all .txt files
```

### File Permissions
```bash
chmod 755 file.sh    # Make file executable
chmod -R 644 *.txt   # Set read permissions recursively
chown user:group file # Change ownership
```

## Process Management

### Process Control
```bash
ps aux           # List all processes
top              # Real-time process monitor
kill -9 PID      # Force kill process
killall firefox  # Kill all firefox processes
```
""",
                "docker_commands.md": """# Docker Commands Reference

## Container Management

### Basic Operations
```bash
docker ps                    # List running containers
docker ps -a                 # List all containers
docker run -it ubuntu bash   # Run interactive container
docker stop container_name   # Stop container
```

### Image Management
```bash
docker images               # List images
docker pull ubuntu:latest   # Pull image
docker build -t myapp .     # Build image from Dockerfile
docker rmi image_id         # Remove image
```

## Docker Compose

### Compose Operations
```bash
docker-compose up -d        # Start services in background
docker-compose down         # Stop and remove containers
docker-compose logs -f      # Follow logs
```
""",
                "git_commands.md": """# Git Commands Reference

## Basic Operations

### Repository Setup
```bash
git init                    # Initialize repository
git clone <url>            # Clone repository
git remote add origin <url> # Add remote
```

### Staging and Commits
```bash
git add .                  # Stage all changes
git add file.txt           # Stage specific file
git commit -m "message"    # Commit with message
git push origin main       # Push to remote
```

## Branching

### Branch Management
```bash
git branch                 # List branches
git branch feature-name    # Create branch
git checkout feature-name  # Switch branch
git merge feature-name     # Merge branch
```
"""
            }
            
            # Write sample files
            for filename, content in samples.items():
                sample_file = sample_data_dir / filename
                with open(sample_file, "w", encoding="utf-8") as f:
                    f.write(content)
                print(f"  📄 Created sample: {filename}")
            
            # Import the sample data
            from command_kb.core.loader import MarkdownLoader
            from command_kb.core.chunker import SmartChunker
            from command_kb.core.embedder import APIEmbedder
            
            # Load documents
            loader = MarkdownLoader()
            documents = loader.load_documents(str(sample_data_dir))
            
            if not documents:
                print("  ⚠️  No sample documents loaded")
                return True  # Not a failure, just no data
            
            # Chunk documents
            chunker = SmartChunker()
            chunked_docs = chunker.chunk_documents(documents)
            
            # Generate embeddings and store
            embedder = APIEmbedder(self.config, self.api_manager)
            embedding_result = embedder.embed_documents(chunked_docs)
            
            if embedding_result.success:
                success = self.storage.store_documents(chunked_docs, embedding_result.embeddings)
                if success:
                    print(f"  ✅ Imported {len(chunked_docs)} sample chunks")
                    print(f"  💰 Cost: ${embedding_result.cost:.6f}")
                    return True
                else:
                    print("  ❌ Failed to store sample data")
                    return False
            else:
                print(f"  ❌ Failed to embed sample data: {embedding_result.error}")
                return False
            
        except Exception as e:
            print(f"  ❌ Sample data import failed: {e}")
            return False


def main():
    """Main setup function."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Setup Personal Command-Line Vector Knowledge Base"
    )
    parser.add_argument(
        "--with-samples", 
        action="store_true",
        help="Import sample data for testing"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.INFO if args.verbose else logging.WARNING
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run setup
    setup_manager = SetupManager()
    success = setup_manager.run_setup(include_sample_data=args.with_samples)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()