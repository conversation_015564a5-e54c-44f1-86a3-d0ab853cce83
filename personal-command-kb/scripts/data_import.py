#!/usr/bin/env python3
"""
Data import script for Personal Command-Line Vector Knowledge Base.

This script provides advanced data import functionality:
- Batch processing with progress tracking
- Multiple file format support
- Duplicate detection and handling
- Cost estimation and monitoring
- Resume interrupted imports
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import hashlib
import time

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from command_kb.config import load_config
from command_kb.api.api_manager import APIManager
from command_kb.core.loader import MarkdownLoader
from command_kb.core.chunker import SmartChunker
from command_kb.core.embedder import APIEmbedder
from command_kb.core.storage import ChromaStorage
from command_kb.utils.file_utils import get_file_hash, scan_directory
from command_kb.utils.cost_utils import CostTracker

logger = logging.getLogger(__name__)


@dataclass
class ImportProgress:
    """Track import progress and state."""
    total_files: int = 0
    processed_files: int = 0
    total_chunks: int = 0
    processed_chunks: int = 0
    total_cost: float = 0.0
    failed_files: List[str] = None
    start_time: float = 0.0
    
    def __post_init__(self):
        if self.failed_files is None:
            self.failed_files = []
        if self.start_time == 0.0:
            self.start_time = time.time()
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def files_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.processed_files / self.elapsed_time
        return 0.0
    
    @property
    def chunks_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.processed_chunks / self.elapsed_time
        return 0.0


class DataImporter:
    """Advanced data import manager."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize data importer.
        
        Args:
            config_dir: Optional configuration directory
        """
        self.config = load_config(config_dir)
        self.api_manager = APIManager(self.config)
        self.storage = ChromaStorage(self.config)
        self.embedder = APIEmbedder(self.config, self.api_manager)
        self.loader = MarkdownLoader()
        self.chunker = SmartChunker()
        self.cost_tracker = CostTracker(self.config)
        
        # Import state
        self.progress = ImportProgress()
        self.processed_hashes = set()
        self.state_file = Path("./data/cache/import_state.json")
        
        # Load previous state if exists
        self._load_state()
    
    def import_directory(
        self,
        source_path: str,
        recursive: bool = True,
        batch_size: int = 50,
        chunk_size: int = 500,
        force_reimport: bool = False,
        dry_run: bool = False,
        resume: bool = True
    ) -> ImportProgress:
        """Import documents from directory.
        
        Args:
            source_path: Source directory path
            recursive: Process subdirectories
            batch_size: Batch size for API calls
            chunk_size: Text chunk size
            force_reimport: Force reimport of existing documents
            dry_run: Only estimate costs, don't import
            resume: Resume from previous state
            
        Returns:
            Import progress information
        """
        source_path = Path(source_path)
        
        if not source_path.exists():
            raise ValueError(f"Source path does not exist: {source_path}")
        
        print(f"🚀 Starting data import from: {source_path}")
        print(f"📊 Settings: batch_size={batch_size}, chunk_size={chunk_size}")
        print(f"🔄 Options: recursive={recursive}, force={force_reimport}, dry_run={dry_run}")
        
        try:
            # Scan for files
            files = self._scan_files(source_path, recursive)
            self.progress.total_files = len(files)
            
            if not files:
                print("❌ No supported files found")
                return self.progress
            
            print(f"📁 Found {len(files)} files to process")
            
            # Filter files if resuming
            if resume and not force_reimport:
                files = self._filter_processed_files(files)
                print(f"📋 {len(files)} files remaining after filtering processed files")
            
            # Process files in batches
            for i in range(0, len(files), batch_size):
                batch_files = files[i:i + batch_size]
                
                try:
                    batch_result = self._process_file_batch(
                        batch_files, 
                        chunk_size, 
                        force_reimport, 
                        dry_run
                    )
                    
                    # Update progress
                    self.progress.processed_files += len(batch_files)
                    self.progress.total_cost += batch_result['cost']
                    
                    # Save state periodically
                    self._save_state()
                    
                    # Print progress
                    self._print_progress()
                    
                    # Check cost limits
                    if not dry_run and self._check_cost_limits():
                        print("⚠️  Cost limit reached, stopping import")
                        break
                        
                except Exception as e:
                    logger.exception(f"Batch processing failed: {e}")
                    self.progress.failed_files.extend([str(f) for f in batch_files])
                    continue
            
            # Final summary
            self._print_summary(dry_run)
            
            return self.progress
            
        except Exception as e:
            logger.exception("Import process failed")
            print(f"❌ Import failed: {e}")
            raise
    
    def estimate_cost(self, source_path: str, recursive: bool = True) -> Dict[str, Any]:
        """Estimate import cost without processing.
        
        Args:
            source_path: Source directory path
            recursive: Process subdirectories
            
        Returns:
            Cost estimation details
        """
        print("💰 Estimating import cost...")
        
        # Run dry run
        progress = self.import_directory(
            source_path, 
            recursive=recursive, 
            dry_run=True
        )
        
        # Calculate detailed estimates
        estimation = {
            'total_files': progress.total_files,
            'total_chunks': progress.total_chunks,
            'estimated_cost': progress.total_cost,
            'cost_breakdown': self.cost_tracker.get_cost_breakdown(),
            'provider_costs': self._estimate_provider_costs(progress.total_chunks),
            'time_estimate': self._estimate_processing_time(progress.total_chunks)
        }
        
        return estimation
    
    def _scan_files(self, source_path: Path, recursive: bool) -> List[Path]:
        """Scan directory for supported files."""
        supported_extensions = {'.md', '.txt', '.py', '.sh', '.bash', '.zsh', '.json', '.yaml', '.yml'}
        files = []
        
        if source_path.is_file():
            if source_path.suffix.lower() in supported_extensions:
                files.append(source_path)
        else:
            pattern = "**/*" if recursive else "*"
            for file_path in source_path.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    files.append(file_path)
        
        return sorted(files)
    
    def _filter_processed_files(self, files: List[Path]) -> List[Path]:
        """Filter out already processed files."""
        filtered_files = []
        
        for file_path in files:
            file_hash = get_file_hash(str(file_path))
            if file_hash not in self.processed_hashes:
                filtered_files.append(file_path)
        
        return filtered_files
    
    def _process_file_batch(
        self, 
        files: List[Path], 
        chunk_size: int, 
        force_reimport: bool, 
        dry_run: bool
    ) -> Dict[str, Any]:
        """Process a batch of files."""
        batch_result = {
            'processed_files': 0,
            'total_chunks': 0,
            'cost': 0.0,
            'errors': []
        }
        
        try:
            # Load documents
            documents = []
            for file_path in files:
                try:
                    file_docs = self.loader.load_documents(str(file_path))
                    documents.extend(file_docs)
                    batch_result['processed_files'] += 1
                except Exception as e:
                    error_msg = f"Failed to load {file_path}: {e}"
                    batch_result['errors'].append(error_msg)
                    logger.error(error_msg)
            
            if not documents:
                return batch_result
            
            # Chunk documents
            chunked_docs = self.chunker.chunk_documents(documents)
            batch_result['total_chunks'] = len(chunked_docs)
            self.progress.total_chunks += len(chunked_docs)
            
            if dry_run:
                # Estimate cost only
                estimated_cost = self._estimate_embedding_cost(chunked_docs)
                batch_result['cost'] = estimated_cost
                return batch_result
            
            # Generate embeddings
            embedding_result = self.embedder.embed_documents(chunked_docs)
            
            if not embedding_result.success:
                error_msg = f"Embedding failed: {embedding_result.error}"
                batch_result['errors'].append(error_msg)
                return batch_result
            
            batch_result['cost'] = embedding_result.cost
            
            # Store in database
            if not force_reimport:
                # Check for duplicates
                chunked_docs = self._filter_duplicate_chunks(chunked_docs)
            
            if chunked_docs:
                success = self.storage.store_documents(chunked_docs, embedding_result.embeddings)
                
                if success:
                    self.progress.processed_chunks += len(chunked_docs)
                    
                    # Track processed files
                    for file_path in files:
                        file_hash = get_file_hash(str(file_path))
                        self.processed_hashes.add(file_hash)
                else:
                    batch_result['errors'].append("Failed to store documents in database")
            
            return batch_result
            
        except Exception as e:
            error_msg = f"Batch processing error: {e}"
            batch_result['errors'].append(error_msg)
            logger.exception(error_msg)
            return batch_result
    
    def _filter_duplicate_chunks(self, chunks):
        """Filter out duplicate chunks."""
        # This would implement duplicate detection logic
        # For now, return all chunks
        return chunks
    
    def _estimate_embedding_cost(self, chunks) -> float:
        """Estimate embedding cost for chunks."""
        total_tokens = sum(len(chunk.page_content.split()) for chunk in chunks)
        
        # Use primary provider for estimation
        provider_name = self.config.primary_provider
        provider_config = self.config.api_providers.get(provider_name)
        
        if provider_config:
            # Rough estimation: 1 word ≈ 1.3 tokens
            estimated_tokens = total_tokens * 1.3
            cost_per_1k = 0.00002  # Default OpenAI cost
            
            # Get actual cost from config if available
            if hasattr(provider_config, 'embedding') and hasattr(provider_config.embedding, 'cost_per_1k_tokens'):
                cost_per_1k = provider_config.embedding.cost_per_1k_tokens
            
            return (estimated_tokens / 1000) * cost_per_1k
        
        return 0.0
    
    def _estimate_provider_costs(self, total_chunks: int) -> Dict[str, float]:
        """Estimate costs for different providers."""
        costs = {}
        estimated_tokens = total_chunks * 100  # Rough estimate
        
        for provider_name, provider_config in self.config.api_providers.items():
            cost_per_1k = 0.00002  # Default
            if hasattr(provider_config, 'embedding'):
                cost_per_1k = getattr(provider_config.embedding, 'cost_per_1k_tokens', cost_per_1k)
            
            costs[provider_name] = (estimated_tokens / 1000) * cost_per_1k
        
        return costs
    
    def _estimate_processing_time(self, total_chunks: int) -> Dict[str, float]:
        """Estimate processing time."""
        # Rough estimates based on typical performance
        return {
            'embedding_time': total_chunks * 0.1,  # 0.1s per chunk
            'storage_time': total_chunks * 0.05,   # 0.05s per chunk
            'total_time': total_chunks * 0.15      # Total estimate
        }
    
    def _check_cost_limits(self) -> bool:
        """Check if cost limits are exceeded."""
        daily_limit = self.config.cost_control.daily_limit
        warning_threshold = self.config.cost_control.warning_threshold
        
        if self.progress.total_cost >= daily_limit:
            return True
        
        if self.progress.total_cost >= daily_limit * warning_threshold:
            print(f"⚠️  Warning: Cost ${self.progress.total_cost:.6f} approaching limit ${daily_limit}")
        
        return False
    
    def _print_progress(self):
        """Print current progress."""
        files_pct = (self.progress.processed_files / self.progress.total_files) * 100 if self.progress.total_files > 0 else 0
        
        print(f"📊 Progress: {self.progress.processed_files}/{self.progress.total_files} files ({files_pct:.1f}%)")
        print(f"📝 Chunks: {self.progress.processed_chunks} processed")
        print(f"💰 Cost: ${self.progress.total_cost:.6f}")
        print(f"⏱️  Speed: {self.progress.files_per_second:.1f} files/sec, {self.progress.chunks_per_second:.1f} chunks/sec")
        
        if self.progress.failed_files:
            print(f"❌ Failed: {len(self.progress.failed_files)} files")
    
    def _print_summary(self, dry_run: bool):
        """Print import summary."""
        print("\n" + "=" * 60)
        print("📊 Import Summary")
        print("=" * 60)
        
        if dry_run:
            print("🔍 DRY RUN - No data was actually imported")
        
        print(f"📁 Total Files: {self.progress.total_files}")
        print(f"✅ Processed Files: {self.progress.processed_files}")
        print(f"📝 Total Chunks: {self.progress.total_chunks}")
        print(f"💰 Total Cost: ${self.progress.total_cost:.6f}")
        print(f"⏱️  Total Time: {self.progress.elapsed_time:.1f} seconds")
        
        if self.progress.failed_files:
            print(f"❌ Failed Files: {len(self.progress.failed_files)}")
            for failed_file in self.progress.failed_files[:5]:  # Show first 5
                print(f"   • {failed_file}")
            if len(self.progress.failed_files) > 5:
                print(f"   ... and {len(self.progress.failed_files) - 5} more")
        
        # Performance stats
        if self.progress.processed_files > 0:
            print(f"📈 Performance:")
            print(f"   • {self.progress.files_per_second:.1f} files/second")
            print(f"   • {self.progress.chunks_per_second:.1f} chunks/second")
            print(f"   • ${self.progress.total_cost / self.progress.processed_files:.6f} cost/file")
    
    def _load_state(self):
        """Load previous import state."""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    state_data = json.load(f)
                
                self.processed_hashes = set(state_data.get('processed_hashes', []))
                
                # Load progress if recent (within 24 hours)
                if time.time() - state_data.get('timestamp', 0) < 86400:
                    progress_data = state_data.get('progress', {})
                    for key, value in progress_data.items():
                        if hasattr(self.progress, key):
                            setattr(self.progress, key, value)
                
                print(f"📋 Loaded state: {len(self.processed_hashes)} processed files")
                
            except Exception as e:
                logger.warning(f"Failed to load import state: {e}")
    
    def _save_state(self):
        """Save current import state."""
        try:
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            state_data = {
                'timestamp': time.time(),
                'processed_hashes': list(self.processed_hashes),
                'progress': asdict(self.progress)
            }
            
            with open(self.state_file, 'w') as f:
                json.dump(state_data, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Failed to save import state: {e}")


def main():
    """Main import function."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Import data into Personal Command-Line Vector Knowledge Base"
    )
    parser.add_argument(
        "source_path",
        help="Source directory or file path"
    )
    parser.add_argument(
        "--recursive", "-r",
        action="store_true",
        help="Process directories recursively"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=50,
        help="Batch size for processing"
    )
    parser.add_argument(
        "--chunk-size",
        type=int,
        default=500,
        help="Text chunk size"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force reimport of existing documents"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Estimate costs without importing"
    )
    parser.add_argument(
        "--no-resume",
        action="store_true",
        help="Don't resume from previous state"
    )
    parser.add_argument(
        "--estimate-only",
        action="store_true",
        help="Only show cost estimation"
    )
    parser.add_argument(
        "--config-dir",
        help="Configuration directory"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.INFO if args.verbose else logging.WARNING
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Initialize importer
        importer = DataImporter(args.config_dir)
        
        if args.estimate_only:
            # Show cost estimation only
            estimation = importer.estimate_cost(args.source_path, args.recursive)
            
            print("\n💰 Cost Estimation")
            print("=" * 40)
            print(f"📁 Files: {estimation['total_files']}")
            print(f"📝 Chunks: {estimation['total_chunks']}")
            print(f"💰 Estimated Cost: ${estimation['estimated_cost']:.6f}")
            print(f"⏱️  Estimated Time: {estimation['time_estimate']['total_time']:.1f} seconds")
            
            print("\n🏷️  Provider Cost Comparison:")
            for provider, cost in estimation['provider_costs'].items():
                print(f"   • {provider}: ${cost:.6f}")
        
        else:
            # Run import
            progress = importer.import_directory(
                source_path=args.source_path,
                recursive=args.recursive,
                batch_size=args.batch_size,
                chunk_size=args.chunk_size,
                force_reimport=args.force,
                dry_run=args.dry_run,
                resume=not args.no_resume
            )
            
            # Exit with error code if there were failures
            if progress.failed_files:
                sys.exit(1)
    
    except Exception as e:
        print(f"❌ Import failed: {e}")
        logger.exception("Import process failed")
        sys.exit(1)


if __name__ == "__main__":
    main()