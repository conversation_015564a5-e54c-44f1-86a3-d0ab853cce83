# Qwen模型测试文档

## 测试新的Qwen模型配置

### Embedding模型
- 模型名称: Qwen/Qwen3-Embedding-8B
- 维度: 8192
- 提供商: SiliconFlow

### 生成模型
- 模型名称: Qwen/Qwen2.5-72B-Instruct
- 类型: 指令微调模型
- 提供商: SiliconFlow

## 常用命令示例

### Docker命令
```bash
docker ps -a                    # 查看所有容器
docker logs container_name      # 查看容器日志
docker exec -it container bash  # 进入容器
```

### Git命令
```bash
git status                      # 查看状态
git add .                       # 添加所有文件
git commit -m "message"         # 提交更改
```

### Linux命令
```bash
ls -la                          # 列出详细文件信息
find . -name "*.py"             # 查找Python文件
grep -r "pattern" .             # 递归搜索文本
```