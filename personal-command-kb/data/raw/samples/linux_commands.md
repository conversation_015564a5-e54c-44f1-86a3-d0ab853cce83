# Linux基础命令指令集

## 文件和目录操作

### 目录导航
- 命令类型: 基础导航
- 使用频率: 极高
- 适用场景: 日常文件系统操作

### 文件查看
- 命令类型: 文件内容查看
- 使用频率: 高
- 适用场景: 查看文件内容、日志分析

## 常用命令示例

### 目录操作
```bash
pwd                             # 显示当前工作目录
ls                              # 列出当前目录文件
ls -la                          # 列出详细文件信息（包括隐藏文件）
ls -lh                          # 以人类可读格式显示文件大小
cd /path/to/directory           # 切换到指定目录
cd ..                           # 返回上级目录
cd ~                            # 返回用户主目录
cd -                            # 返回上次访问的目录
mkdir dirname                   # 创建目录
mkdir -p path/to/new/dir        # 递归创建目录
rmdir dirname                   # 删除空目录
rm -rf dirname                  # 强制删除目录及其内容
```

### 文件操作
```bash
touch filename                  # 创建空文件或更新时间戳
cp source dest                  # 复制文件
cp -r source_dir dest_dir       # 递归复制目录
mv oldname newname              # 重命名或移动文件
rm filename                     # 删除文件
rm -f filename                  # 强制删除文件
rm -rf directory                # 强制删除目录
ln -s target linkname           # 创建软链接
find . -name "*.txt"            # 查找txt文件
find /path -type f -size +100M  # 查找大于100M的文件
```

### 文件内容查看
```bash
cat filename                    # 显示文件全部内容
less filename                   # 分页查看文件内容
head filename                   # 显示文件前10行
head -n 20 filename             # 显示文件前20行
tail filename                   # 显示文件后10行
tail -n 20 filename             # 显示文件后20行
tail -f filename                # 实时跟踪文件变化
grep "pattern" filename         # 在文件中搜索模式
grep -r "pattern" directory     # 递归搜索目录中的模式
grep -i "pattern" filename      # 忽略大小写搜索
grep -n "pattern" filename      # 显示行号
```

### 文件权限和属性
```bash
chmod 755 filename              # 设置文件权限
chmod +x filename               # 添加执行权限
chmod -x filename               # 移除执行权限
chown user:group filename       # 更改文件所有者
chgrp group filename            # 更改文件组
stat filename                   # 显示文件详细信息
file filename                   # 显示文件类型
du -h filename                  # 显示文件大小
du -sh directory                # 显示目录总大小
df -h                           # 显示磁盘使用情况
```

## 进程和系统管理

### 进程管理
- 命令类型: 系统监控
- 使用频率: 高
- 适用场景: 系统性能监控、进程管理

### 系统信息
- 命令类型: 系统状态查看
- 使用频率: 中
- 适用场景: 系统诊断、性能分析

### 进程操作
```bash
ps aux                          # 显示所有进程
ps -ef                          # 显示所有进程（另一种格式）
top                             # 实时显示进程信息
htop                            # 增强版top（需要安装）
kill PID                        # 终止指定进程
kill -9 PID                     # 强制终止进程
killall process_name            # 终止所有同名进程
pgrep process_name              # 查找进程ID
pkill process_name              # 按名称终止进程
nohup command &                 # 后台运行命令
jobs                            # 显示当前作业
bg                              # 将作业放到后台
fg                              # 将作业调到前台
```

### 系统信息查看
```bash
uname -a                        # 显示系统信息
whoami                          # 显示当前用户
id                              # 显示用户和组ID
w                               # 显示登录用户
who                             # 显示当前登录用户
uptime                          # 显示系统运行时间
date                            # 显示当前日期时间
cal                             # 显示日历
free -h                         # 显示内存使用情况
lscpu                           # 显示CPU信息
lsblk                           # 显示块设备信息
lsusb                           # 显示USB设备
lspci                           # 显示PCI设备
```

## 网络和连接

### 网络诊断
- 命令类型: 网络工具
- 使用频率: 中
- 适用场景: 网络故障排查、连接测试

### 网络命令
```bash
ping hostname                   # 测试网络连接
ping -c 4 hostname              # ping 4次后停止
wget url                        # 下载文件
curl url                        # 获取网页内容
curl -O url                     # 下载文件
ssh user@hostname               # SSH连接远程主机
scp file user@host:/path        # 安全复制文件到远程
rsync -av source dest           # 同步文件
netstat -tuln                   # 显示网络连接
ss -tuln                        # 现代版netstat
ifconfig                        # 显示网络接口信息
ip addr show                    # 显示IP地址信息
```

## 文本处理

### 文本编辑
- 命令类型: 文本处理
- 使用频率: 高
- 适用场景: 文本编辑、数据处理

### 文本处理命令
```bash
nano filename                   # 使用nano编辑器
vim filename                    # 使用vim编辑器
sort filename                   # 排序文件内容
sort -r filename                # 反向排序
sort -n filename                # 数字排序
uniq filename                   # 去除重复行
cut -d',' -f1 file.csv          # 提取CSV第一列
awk '{print $1}' filename       # 打印第一列
sed 's/old/new/g' filename      # 替换文本
tr 'a-z' 'A-Z' < filename       # 转换大小写
wc filename                     # 统计字数、行数
wc -l filename                  # 统计行数
```

## 压缩和归档

### 文件压缩
- 命令类型: 文件归档
- 使用频率: 中
- 适用场景: 文件备份、传输

### 压缩命令
```bash
tar -czf archive.tar.gz files   # 创建gzip压缩包
tar -xzf archive.tar.gz         # 解压gzip压缩包
tar -cjf archive.tar.bz2 files  # 创建bzip2压缩包
tar -xjf archive.tar.bz2        # 解压bzip2压缩包
zip archive.zip files           # 创建zip压缩包
unzip archive.zip               # 解压zip文件
gzip filename                   # 压缩文件
gunzip filename.gz              # 解压gzip文件
```

## 环境变量和别名

### 环境配置
- 命令类型: 环境管理
- 使用频率: 中
- 适用场景: 系统配置、开发环境设置

### 环境变量
```bash
echo $PATH                      # 显示PATH环境变量
export VAR=value                # 设置环境变量
env                             # 显示所有环境变量
printenv                        # 显示环境变量
which command                   # 显示命令位置
whereis command                 # 查找命令位置
type command                    # 显示命令类型
alias ll='ls -la'               # 创建别名
unalias ll                      # 删除别名
history                         # 显示命令历史
!!                              # 执行上一条命令
!n                              # 执行历史中第n条命令
```

## 系统服务管理

### 服务控制
- 命令类型: 系统服务
- 使用频率: 中
- 适用场景: 服务管理、系统维护

### systemctl命令（systemd系统）
```bash
systemctl status service        # 查看服务状态
systemctl start service         # 启动服务
systemctl stop service          # 停止服务
systemctl restart service       # 重启服务
systemctl enable service        # 开机自启动
systemctl disable service       # 禁用开机自启动
systemctl list-units            # 列出所有单元
journalctl -u service           # 查看服务日志
journalctl -f                   # 实时查看系统日志
```
