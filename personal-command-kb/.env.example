# =============================================================================
# Personal Command-Line Vector Knowledge Base - Environment Configuration
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env file to version control!

# =============================================================================
# API Keys Configuration (REQUIRED)
# =============================================================================
# Get your API keys from respective providers:
# - OpenAI: https://platform.openai.com/api-keys
# - SiliconFlow: https://cloud.siliconflow.cn/
# - Zhipu AI: https://open.bigmodel.cn/
# - Moonshot: https://platform.moonshot.cn/

# OpenAI API Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# SiliconFlow API Configuration (OpenAI Compatible)
SILICONFLOW_API_KEY=sk-your-siliconflow-api-key-here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# Zhipu AI API Configuration
ZHIPU_API_KEY=your-zhipu-api-key-here
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4

# Moonshot API Configuration
MOONSHOT_API_KEY=sk-your-moonshot-api-key-here
MOONSHOT_BASE_URL=https://api.moonshot.cn/v1

# =============================================================================
# Database and Storage Configuration
# =============================================================================
# Vector database path (ChromaDB)
VECTOR_DB_PATH=./data/vector_db
VECTOR_DB_COLLECTION=command_kb

# Data source and processing paths
DATA_SOURCE_PATH=./data/raw
PROCESSED_DATA_PATH=./data/processed
CACHE_PATH=./data/cache

# =============================================================================
# API Provider Configuration
# =============================================================================
# Primary API provider (siliconflow, openai, zhipu, moonshot)
DEFAULT_API_PROVIDER=siliconflow

# API timeout and retry settings
API_TIMEOUT=30
MAX_RETRIES=3
RETRY_BACKOFF_FACTOR=2.0

# Rate limiting (requests per minute)
API_RATE_LIMIT=60
API_BURST_SIZE=10# =============================================================================
# Cost Control Configuration
# =============================================================================
# Daily cost limits (in USD)
DAILY_COST_LIMIT=10.0
COST_WARNING_THRESHOLD=0.8

# Cost tracking and alerts
ENABLE_COST_TRACKING=true
COST_ALERT_EMAIL=<EMAIL>

# =============================================================================
# Caching Configuration
# =============================================================================
# Enable/disable caching
ENABLE_CACHE=true
CACHE_TTL=3600
MAX_CACHE_SIZE=1000

# Cache types to enable
ENABLE_EMBEDDING_CACHE=true
ENABLE_GENERATION_CACHE=true
ENABLE_RETRIEVAL_CACHE=true

# =============================================================================
# Logging Configuration
# =============================================================================
# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
LOG_FILE=./logs/command_kb.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Enable structured logging
ENABLE_STRUCTURED_LOGGING=true
LOG_FORMAT=json

# =============================================================================
# Monitoring and Health Check Configuration
# =============================================================================
# Enable monitoring
ENABLE_MONITORING=true
METRICS_PORT=8080
HEALTH_CHECK_INTERVAL=300

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=2.0

# =============================================================================
# RAG Configuration
# =============================================================================
# Embedding model settings
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSIONS=1536
EMBEDDING_BATCH_SIZE=100

# Text chunking settings
CHUNK_SIZE=500
CHUNK_OVERLAP=50
MAX_CHUNK_SIZE=2000

# Retrieval settings
RETRIEVAL_TOP_K=5
SIMILARITY_THRESHOLD=0.7
ENABLE_HYBRID_SEARCH=true

# Generation settings
GENERATION_MODEL=gpt-4o-mini
MAX_TOKENS=1000
TEMPERATURE=0.1
ENABLE_STREAMING=true

# =============================================================================
# Security Configuration
# =============================================================================
# API key rotation (days)
API_KEY_ROTATION_DAYS=90
ENABLE_API_KEY_MONITORING=true

# Data privacy settings
ENABLE_DATA_ANONYMIZATION=true
ENABLE_SENSITIVE_DATA_FILTERING=true

# Audit logging
ENABLE_AUDIT_LOGGING=true
AUDIT_LOG_PATH=./logs/audit.log

# =============================================================================
# Development and Testing Configuration
# =============================================================================
# Environment mode (development, production, testing)
ENVIRONMENT=development

# Debug settings
DEBUG=false
ENABLE_DEBUG_LOGGING=false

# Testing settings
TEST_DATA_PATH=./tests/data
ENABLE_TEST_MODE=false
MOCK_API_CALLS=false