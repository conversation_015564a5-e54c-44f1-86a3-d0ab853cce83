# =============================================================================
# Personal Command-Line Vector Knowledge Base - API Providers Configuration
# =============================================================================
# This file contains detailed configuration for each API provider.
# API keys should be set via environment variables for security.

# =============================================================================
# API Provider Definitions
# =============================================================================

api_providers:
  # SiliconFlow - OpenAI Compatible (Recommended for cost efficiency)
  siliconflow:
    enabled: true
    base_url: "https://api.siliconflow.cn/v1"
    timeout: 30
    max_retries: 3
    rate_limit_rpm: 60
    
    # Model configurations
    embedding:
      model: "text-embedding-3-small"
      dimensions: 1536
      batch_size: 100
      cost_per_1k_tokens: 0.000007  # USD
    
    generation:
      model: "gpt-4o-mini"
      max_tokens: 1000
      temperature: 0.1
      cost_per_1k_tokens: 0.000042  # USD
      supports_streaming: true
    
    # Provider-specific settings
    features:
      supports_batch_embedding: true
      supports_streaming: true
      supports_function_calling: true
      max_context_length: 128000

  # OpenAI - Original provider (Higher cost, highest quality)
  openai:
    enabled: true
    base_url: "https://api.openai.com/v1"
    timeout: 30
    max_retries: 3
    rate_limit_rpm: 60
    
    # Model configurations
    embedding:
      model: "text-embedding-3-small"
      dimensions: 1536
      batch_size: 100
      cost_per_1k_tokens: 0.00002  # USD
    
    generation:
      model: "gpt-4o-mini"
      max_tokens: 1000
      temperature: 0.1
      cost_per_1k_tokens: 0.00015  # USD
      supports_streaming: true
    
    # Provider-specific settings
    features:
      supports_batch_embedding: true
      supports_streaming: true
      supports_function_calling: true
      max_context_length: 128000

  # Zhipu AI - Chinese provider (Good for Chinese content)
  zhipu:
    enabled: true
    base_url: "https://open.bigmodel.cn/api/paas/v4"
    timeout: 30
    max_retries: 3
    rate_limit_rpm: 60
    
    # Model configurations
    embedding:
      model: "embedding-2"
      dimensions: 1024
      batch_size: 100
      cost_per_1k_tokens: 0.00001  # USD
    
    generation:
      model: "glm-4-flash"
      max_tokens: 1000
      temperature: 0.1
      cost_per_1k_tokens: 0.00001  # USD
      supports_streaming: true
    
    # Provider-specific settings
    features:
      supports_batch_embedding: true
      supports_streaming: true
      supports_function_calling: false
      max_context_length: 128000

  # Moonshot - Alternative provider
  moonshot:
    enabled: true
    base_url: "https://api.moonshot.cn/v1"
    timeout: 30
    max_retries: 3
    rate_limit_rpm: 60
    
    # Model configurations
    embedding:
      model: "text-embedding-v1"
      dimensions: 1536
      batch_size: 100
      cost_per_1k_tokens: 0.00001  # USD
    
    generation:
      model: "moonshot-v1-8k"
      max_tokens: 1000
      temperature: 0.1
      cost_per_1k_tokens: 0.00012  # USD
      supports_streaming: true
    
    # Provider-specific settings
    features:
      supports_batch_embedding: true
      supports_streaming: true
      supports_function_calling: false
      max_context_length: 8000

# =============================================================================
# Provider Selection Strategy
# =============================================================================
selection_strategy:
  # Primary selection criteria
  primary_criteria: "cost"  # cost, speed, quality, availability
  
  # Cost-based selection
  cost_optimization:
    enabled: true
    prefer_cheaper: true
    cost_difference_threshold: 0.5  # Switch if cost difference > 50%
  
  # Performance-based selection
  performance_optimization:
    enabled: true
    response_time_threshold: 5.0  # seconds
    success_rate_threshold: 0.95  # 95%
  
  # Fallback strategy
  fallback:
    enabled: true
    max_failures_before_fallback: 3
    fallback_cooldown: 300  # 5 minutes
    auto_recovery: true

# =============================================================================
# Load Balancing Configuration
# =============================================================================
load_balancing:
  enabled: false  # Enable for high-volume usage
  strategy: "round_robin"  # round_robin, weighted, least_connections
  
  # Weighted distribution (if using weighted strategy)
  weights:
    siliconflow: 0.4
    openai: 0.3
    zhipu: 0.2
    moonshot: 0.1

# =============================================================================
# Health Check Configuration
# =============================================================================
health_check:
  enabled: true
  interval: 300  # 5 minutes
  timeout: 10  # seconds
  
  # Health check endpoints
  endpoints:
    siliconflow: "/models"
    openai: "/models"
    zhipu: "/models"
    moonshot: "/models"
  
  # Failure thresholds
  max_consecutive_failures: 3
  recovery_check_interval: 60  # 1 minute

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
rate_limiting:
  enabled: true
  global_limit: 1000  # requests per hour
  
  # Per-provider limits
  provider_limits:
    siliconflow: 300  # requests per hour
    openai: 300
    zhipu: 200
    moonshot: 200
  
  # Burst handling
  burst_size: 10
  burst_window: 60  # seconds

# =============================================================================
# Retry Configuration
# =============================================================================
retry:
  enabled: true
  max_retries: 3
  backoff_factor: 2.0
  max_backoff: 60  # seconds
  
  # Retry conditions
  retry_on_status_codes: [429, 500, 502, 503, 504]
  retry_on_timeout: true
  retry_on_connection_error: true
  
  # Per-provider retry settings
  provider_overrides:
    zhipu:
      max_retries: 5  # Higher retry for less stable provider
    moonshot:
      backoff_factor: 1.5

# =============================================================================
# Monitoring and Alerting
# =============================================================================
monitoring:
  enabled: true
  
  # Metrics to collect
  metrics:
    - "request_count"
    - "response_time"
    - "error_rate"
    - "cost_per_request"
    - "tokens_used"
  
  # Alerting thresholds
  alerts:
    high_error_rate: 0.1  # 10%
    slow_response_time: 10.0  # seconds
    high_cost_rate: 1.0  # USD per hour
  
  # Alert destinations
  alert_channels:
    - "log"  # Always log alerts
    # - "email"  # Configure email in main config
    # - "webhook"  # Configure webhook URL

# =============================================================================
# Caching Configuration (Provider-specific)
# =============================================================================
caching:
  # Cache API responses to reduce costs
  response_cache:
    enabled: true
    ttl: 3600  # 1 hour
    max_size: 1000
  
  # Cache embeddings (longer TTL as they rarely change)
  embedding_cache:
    enabled: true
    ttl: 86400  # 24 hours
    max_size: 5000
  
  # Cache model lists and metadata
  metadata_cache:
    enabled: true
    ttl: 3600  # 1 hour
    max_size: 100