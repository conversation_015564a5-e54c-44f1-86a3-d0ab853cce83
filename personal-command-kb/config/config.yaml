# =============================================================================
# Personal Command-Line Vector Knowledge Base - Main Configuration
# =============================================================================
# This file contains the main application configuration settings.
# Environment variables will override these values when present.

# =============================================================================
# Application Settings
# =============================================================================
app:
  name: "Personal Command-Line Vector Knowledge Base"
  version: "0.1.0"
  environment: "development"  # development, production, testing
  debug: false

# =============================================================================
# API Provider Configuration
# =============================================================================
# Primary provider selection and fallback order
primary_provider: "siliconflow"  # siliconflow, openai, zhipu, moonshot
fallback_providers:
  - "openai"
  - "zhipu"
  - "moonshot"

# Global API settings (优化性能)
api:
  timeout: 10
  max_retries: 2
  backoff_factor: 1.5
  rate_limit_rpm: 60
  burst_size: 10

# =============================================================================
# Database Configuration
# =============================================================================
database:
  path: "./data/vector_db"
  collection_name: "command_kb"
  distance_metric: "cosine"
  batch_size: 100
  max_batch_size: 1000

# =============================================================================
# Text Processing and RAG Configuration
# =============================================================================
# Document loading settings
loader:
  supported_extensions: [".md", ".txt", ".py", ".sh", ".bash", ".zsh", ".json", ".yaml", ".yml"]
  max_file_size: 10485760  # 10MB
  encoding: "utf-8"

# Text chunking configuration
chunker:
  chunk_size: 500
  chunk_overlap: 50
  min_chunk_size: 100
  max_chunk_size: 2000
  preserve_code_blocks: true
  preserve_headers: true

# Retrieval configuration
retrieval:
  top_k: 5
  similarity_threshold: 0.7
  enable_hybrid_search: true
  rerank_results: true
  max_query_results: 100

# Generation configuration
generation:
  max_tokens: 1000
  temperature: 0.1
  enable_streaming: true
  system_prompt: |
    You are a helpful assistant for command-line queries. 
    Provide accurate, concise answers based on the provided context.
    Include relevant code examples and command snippets when appropriate.
    Format your responses clearly with proper markdown formatting.

# =============================================================================
# Caching Configuration
# =============================================================================
cache:
  enabled: true
  ttl: 3600  # 1 hour default
  max_size: 1000
  
  # Cache-specific settings
  embedding_cache:
    enabled: true
    ttl: 86400  # 24 hours
    max_size: 5000
  
  generation_cache:
    enabled: true
    ttl: 1800  # 30 minutes
    max_size: 1000
  
  retrieval_cache:
    enabled: true
    ttl: 600  # 10 minutes
    max_size: 2000

# =============================================================================
# Cost Control Configuration
# =============================================================================
cost_control:
  daily_limit: 10.0  # USD
  warning_threshold: 0.8  # 80%
  enable_tracking: true
  auto_fallback: true
  check_interval: 300  # seconds
  alert_email: null  # Set via environment variable

# =============================================================================
# Monitoring and Health Configuration
# =============================================================================
monitoring:
  enabled: true
  metrics_port: 8080
  health_check_interval: 300  # 5 minutes
  enable_performance_monitoring: true
  slow_query_threshold: 2.0  # seconds
  
  # Metrics collection
  collect_api_metrics: true
  collect_performance_metrics: true
  collect_cost_metrics: true
  metrics_retention_days: 30

# =============================================================================
# Security Configuration
# =============================================================================
security:
  api_key_rotation_days: 90
  enable_api_key_monitoring: true
  enable_data_anonymization: true
  enable_sensitive_data_filtering: true
  enable_audit_logging: true
  audit_log_path: "./logs/audit.log"
  
  # Sensitive data patterns to filter
  sensitive_patterns:
    - "password\\s*[:=]\\s*['\"]?([^'\"\\s]+)"
    - "api[_-]?key\\s*[:=]\\s*['\"]?([^'\"\\s]+)"
    - "secret\\s*[:=]\\s*['\"]?([^'\"\\s]+)"
    - "token\\s*[:=]\\s*['\"]?([^'\"\\s]+)"

# =============================================================================
# Logging Configuration
# =============================================================================
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file_path: "./logs/command_kb.log"
  max_size: 10485760  # 10MB
  backup_count: 5
  enable_structured: true
  format_type: "json"  # json, text
  
  # Logger-specific levels
  loggers:
    "command_kb.api": "INFO"
    "command_kb.core": "INFO"
    "command_kb.utils": "WARNING"
    "chromadb": "WARNING"
    "openai": "WARNING"
    "httpx": "WARNING"

# =============================================================================
# CLI Configuration
# =============================================================================
cli:
  default_output_format: "table"  # table, json, yaml, csv
  progress_bar_width: 50
  enable_colors: true
  enable_rich_formatting: true
  
  # Interactive mode settings
  interactive:
    welcome_message: true
    show_help_on_start: false
    auto_complete: true
    history_size: 1000

# =============================================================================
# Data Paths Configuration
# =============================================================================
paths:
  data_root: "./data"
  raw_data: "./data/raw"
  processed_data: "./data/processed"
  cache_data: "./data/cache"
  logs: "./logs"
  config: "./config"
  
  # Ensure directories exist
  create_missing_dirs: true