#!/bin/bash
# 性能优化脚本 - optimize_performance.sh
# 一键解决7.77秒响应时间问题

echo "🚀 Personal Command KB 性能优化工具"
echo "=================================="
echo "📊 当前问题：查询响应时间7.77秒"
echo "🎯 优化目标：降低到2-3秒"
echo ""

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo "❌ .env文件不存在，请先运行: cp .env.example .env"
    exit 1
fi

# 备份原配置
backup_file=".env.backup.$(date +%Y%m%d_%H%M%S)"
cp .env "$backup_file"
echo "✅ 已备份原配置到: $backup_file"

# 显示当前配置
echo ""
echo "📋 当前配置分析："
echo "   模型: $(grep GENERATION_MODEL .env | cut -d'=' -f2)"
echo "   嵌入: $(grep EMBEDDING_MODEL .env | cut -d'=' -f2)"
echo "   维度: $(grep EMBEDDING_DIMENSIONS .env | cut -d'=' -f2)"
echo "   检索: $(grep RETRIEVAL_TOP_K .env | cut -d'=' -f2) 个结果"

# 询问用户选择优化级别
echo ""
echo "🎛️  请选择优化级别："
echo "   1) 平衡模式 (推荐) - 2-3秒响应，质量损失<10%"
echo "   2) 极速模式 - 1-1.5秒响应，质量损失20-30%"
echo "   3) 自定义优化"
echo ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🎯 应用平衡模式优化..."
        
        # 更新模型配置
        sed -i.bak 's/GENERATION_MODEL=.*/GENERATION_MODEL=Qwen\/Qwen2.5-7B-Instruct/' .env
        sed -i.bak 's/EMBEDDING_MODEL=.*/EMBEDDING_MODEL=text-embedding-3-small/' .env
        sed -i.bak 's/EMBEDDING_DIMENSIONS=.*/EMBEDDING_DIMENSIONS=1536/' .env
        
        # 更新检索配置
        sed -i.bak 's/RETRIEVAL_TOP_K=.*/RETRIEVAL_TOP_K=3/' .env
        sed -i.bak 's/SIMILARITY_THRESHOLD=.*/SIMILARITY_THRESHOLD=0.75/' .env
        sed -i.bak 's/MAX_TOKENS=.*/MAX_TOKENS=600/' .env
        
        # 更新性能配置
        sed -i.bak 's/API_TIMEOUT=.*/API_TIMEOUT=8/' .env
        sed -i.bak 's/RETRY_BACKOFF_FACTOR=.*/RETRY_BACKOFF_FACTOR=1.2/' .env
        
        echo "✅ 平衡模式配置完成"
        echo "📊 预期响应时间: 2-3秒"
        ;;
        
    2)
        echo "⚡ 应用极速模式优化..."
        
        # 极速模式配置
        sed -i.bak 's/GENERATION_MODEL=.*/GENERATION_MODEL=Qwen\/Qwen2.5-1.5B-Instruct/' .env
        sed -i.bak 's/EMBEDDING_MODEL=.*/EMBEDDING_MODEL=text-embedding-3-small/' .env
        sed -i.bak 's/EMBEDDING_DIMENSIONS=.*/EMBEDDING_DIMENSIONS=1536/' .env
        
        # 极速检索配置
        sed -i.bak 's/RETRIEVAL_TOP_K=.*/RETRIEVAL_TOP_K=2/' .env
        sed -i.bak 's/SIMILARITY_THRESHOLD=.*/SIMILARITY_THRESHOLD=0.85/' .env
        sed -i.bak 's/MAX_TOKENS=.*/MAX_TOKENS=300/' .env
        
        # 极速性能配置
        sed -i.bak 's/API_TIMEOUT=.*/API_TIMEOUT=5/' .env
        sed -i.bak 's/TEMPERATURE=.*/TEMPERATURE=0.0/' .env
        
        echo "✅ 极速模式配置完成"
        echo "📊 预期响应时间: 1-1.5秒"
        ;;
        
    3)
        echo "🛠️  自定义优化模式"
        echo ""
        
        # 模型选择
        echo "📱 选择生成模型："
        echo "   1) Qwen2.5-7B-Instruct (推荐，快速)"
        echo "   2) Qwen2.5-1.5B-Instruct (最快)"
        echo "   3) 保持当前 (Qwen2.5-72B-Instruct)"
        read -p "选择 (1-3): " model_choice
        
        case $model_choice in
            1) sed -i.bak 's/GENERATION_MODEL=.*/GENERATION_MODEL=Qwen\/Qwen2.5-7B-Instruct/' .env ;;
            2) sed -i.bak 's/GENERATION_MODEL=.*/GENERATION_MODEL=Qwen\/Qwen2.5-1.5B-Instruct/' .env ;;
            3) echo "保持当前模型" ;;
        esac
        
        # 检索数量
        echo ""
        read -p "🔍 检索结果数量 (当前5，推荐3): " top_k
        if [ ! -z "$top_k" ]; then
            sed -i.bak "s/RETRIEVAL_TOP_K=.*/RETRIEVAL_TOP_K=$top_k/" .env
        fi
        
        # 超时时间
        echo ""
        read -p "⏱️  API超时时间/秒 (当前10，推荐8): " timeout
        if [ ! -z "$timeout" ]; then
            sed -i.bak "s/API_TIMEOUT=.*/API_TIMEOUT=$timeout/" .env
        fi
        
        echo "✅ 自定义优化完成"
        ;;
        
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

# 确保缓存配置优化
echo ""
echo "🚀 优化缓存配置..."

# 检查并添加缓存配置
if ! grep -q "CACHE_TTL" .env; then
    echo "CACHE_TTL=3600" >> .env
fi

if ! grep -q "ENABLE_EMBEDDING_CACHE" .env; then
    echo "ENABLE_EMBEDDING_CACHE=true" >> .env
fi

if ! grep -q "ENABLE_GENERATION_CACHE" .env; then
    echo "ENABLE_GENERATION_CACHE=true" >> .env
fi

# 清理临时文件
rm -f .env.bak

# 显示优化后的配置
echo ""
echo "📊 优化后配置："
echo "   生成模型: $(grep GENERATION_MODEL .env | cut -d'=' -f2)"
echo "   嵌入模型: $(grep EMBEDDING_MODEL .env | cut -d'=' -f2)"
echo "   检索数量: $(grep RETRIEVAL_TOP_K .env | cut -d'=' -f2)"
echo "   API超时: $(grep API_TIMEOUT .env | cut -d'=' -f2)秒"
echo "   缓存启用: $(grep ENABLE_CACHE .env | cut -d'=' -f2)"

echo ""
echo "🎉 性能优化完成！"
echo ""
echo "📋 下一步操作："
echo "   1. 重启应用: ./start.sh"
echo "   2. 测试查询: kb> query 'docker logs'"
echo "   3. 检查状态: kb> status"
echo ""
echo "📈 预期改进："
echo "   • 响应时间: 7.77秒 → 2-3秒"
echo "   • 成本节省: 60-70%"
echo "   • 缓存命中: <0.5秒"
echo ""
echo "🔄 如需恢复原配置: cp $backup_file .env"