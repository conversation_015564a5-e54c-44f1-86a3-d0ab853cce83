[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "personal-command-kb"
version = "0.1.0"
description = "Personal Command-Line Vector Knowledge Base with RAG Technology"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
keywords = ["rag", "vector-database", "command-line", "knowledge-base", "ai"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

# Core dependencies - stable versions for production use
dependencies = [
    # Core framework - LangChain stable version
    "langchain>=0.1.0,<0.2.0",
    "langchain-community>=0.0.20",
    "langchain-core>=0.1.0",
    
    # Vector database - ChromaDB stable version
    "chromadb>=0.4.0,<0.5.0",
    
    # API integrations - OpenAI compatible
    "openai>=1.0.0,<2.0.0",
    "requests>=2.31.0,<3.0.0",
    
    # Text processing and embeddings
    "sentence-transformers>=2.2.0",
    "tiktoken>=0.5.0",
    
    # Document processing
    "beautifulsoup4>=4.12.0",
    "html2text>=2020.1.16",
    "markdown>=3.5.0",
    
    # Configuration and environment
    "pyyaml>=6.0.0",
    "python-dotenv>=1.0.0",
    
    # CLI and utilities
    "click>=8.1.0",
    "rich>=13.0.0",
    "tqdm>=4.65.0",
    
    # Caching and serialization
    "diskcache>=5.6.0",
    "joblib>=1.3.0",
    
    # Monitoring and logging
    "structlog>=23.1.0",
    "psutil>=5.9.0",
]# Optional dependencies for development and testing
[project.optional-dependencies]
dev = [
    # Testing framework
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    
    # Code quality
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    
    # Security scanning
    "bandit>=1.7.5",
    "safety>=2.3.0",
    
    # Documentation
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
]

# API provider specific dependencies
api-providers = [
    # Additional API providers
    "anthropic>=0.3.0",  # Claude API
    "google-generativeai>=0.3.0",  # Gemini API
]

# Performance optimization dependencies
performance = [
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "faiss-cpu>=1.7.4",  # Alternative vector search
]

# All optional dependencies
all = [
    "personal-command-kb[dev,api-providers,performance]"
]

[project.scripts]
command-kb = "command_kb.main:main"
kb-setup = "command_kb.scripts.setup_api:main"
kb-import = "command_kb.scripts.data_import:main"

[project.urls]
Homepage = "https://github.com/example/personal-command-kb"
Repository = "https://github.com/example/personal-command-kb"
Documentation = "https://personal-command-kb.readthedocs.io"
"Bug Tracker" = "https://github.com/example/personal-command-kb/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"command_kb" = ["config/*.yaml", "data/examples/*"]# Development tools configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["command_kb"]
known_third_party = ["langchain", "chromadb", "openai"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "chromadb.*",
    "sentence_transformers.*",
    "tiktoken.*",
    "diskcache.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/command_kb",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=80",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "api: marks tests that require API access",
]

[tool.coverage.run]
source = ["src/command_kb"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]